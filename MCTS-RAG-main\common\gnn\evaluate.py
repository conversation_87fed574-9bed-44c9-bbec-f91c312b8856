
from tqdm import tqdm
tqdm.monitor_iterval = 0
import torch
import numpy as np
import math, os
import json
import pickle

def cal_accuracy(pred, answer_dist):
    """
    pred: batch_size
    answer_dist: batch_size, max_local_entity
    """
    num_correct = 0.0
    num_answerable = 0.0
    for i, l in enumerate(pred):
        num_correct += (answer_dist[i, l] != 0)
    for dist in answer_dist:
        if np.sum(dist) != 0:
            num_answerable += 1
    return num_correct / len(pred), num_answerable / len(pred)


def f1_and_hits(answers, candidate2prob, id2entity, entity2name, eps=0.5):
    ans = []
    retrieved = []
    for a in answers:
        if entity2name is None:
            ans.append(id2entity[a])
        else:
            ans.append(entity2name[id2entity[a]])
    correct = 0
    cand_list = sorted(candidate2prob, key=lambda x:x[1], reverse=True)
    if len(cand_list) == 0:
        best_ans = -1
    else:
        best_ans = cand_list[0][0]
    # max_prob = cand_list[0][1]
    tp_prob = 0.0
    for c, prob in cand_list:
        if entity2name is None:
            retrieved.append((id2entity[c], prob))
        else:
            retrieved.append((entity2name[id2entity[c]], prob))
        tp_prob += prob
        if c in answers:
            correct += 1
        if tp_prob > eps:
            break
    if correct > 0:
        em = 1
    else:
        em = 0
    if len(answers) == 0:
        if len(retrieved) == 0:
            return 1.0, 1.0, 1.0, 1.0, 1.0, 0, retrieved, ans  # precision, recall, f1, hits, em
        else:
            return 0.0, 1.0, 0.0, 1.0, 1.0, 1, retrieved , ans # precision, recall, f1, hits, em
    else:
        hits = float(best_ans in answers)
        if len(retrieved) == 0:
            return 1.0, 0.0, 0.0, hits, hits, 2, retrieved , ans # precision, recall, f1, hits, em
        else:
            p, r = correct / len(retrieved), correct / len(answers)
            f1 = 2.0 / (1.0 / p + 1.0 / r) if p != 0 and r != 0 else 0.0
            return p, r, f1, hits, em, 3, retrieved, ans


class Evaluator:

    def __init__(self, args, model, entity2id, relation2id, device):
        self.model = model
        self.args = args
        self.eps = args['eps']
        self.model_name = args["model_name"]

        id2entity = {idx: entity for entity, idx in entity2id.items()}
        self.id2entity = id2entity

        self.entity2name = None
        if 'sr-' in args["data_folder"]:
            file = open('ent2id.pickle', 'rb')
            self.entity2name = list((pickle.load(file)).keys())
            file.close()


        id2relation = {idx: relation for relation, idx in relation2id.items()}
        num_rel_ori = len(relation2id)

        if 'use_inverse_relation' in args:
            self.use_inverse_relation = args['use_inverse_relation']
            if self.use_inverse_relation:
                for i in range(len(id2relation)):
                    id2relation[i + num_rel_ori] = id2relation[i] + "_rev"

        if 'use_self_loop' in args:
            self.use_self_loop = args['use_self_loop']
            if self.use_self_loop:
                id2relation[len(id2relation)] = "self_loop"

        self.id2relation = id2relation
        self.file_write = None
        self.device = device

    def write_info(self, valid_data, tp_list, num_step):
        question_list = valid_data.get_quest()
        #num_step = steps
        obj_list = []
        if tp_list is not None:
            # attn_list = [tp[1] for tp in tp_list]
            action_list = [tp[0] for tp in tp_list]
        for i in range(len(question_list)):
            obj_list.append({})
        for j in range(num_step):
            if tp_list is None:
                actions = None
            else:
                actions = action_list[j]
                actions = actions.cpu().numpy()
            # if attn_list is not None:
            #     attention = attn_list[j].cpu().numpy()
            for i in range(len(question_list)):
                tp_obj = obj_list[i]
                q = question_list[i]
                # real_index = self.true_batch_id[i][0]
                tp_obj['question'] = q
                tp_obj[j] = {}
                # print(actions)
                if tp_list is not None:
                    action = actions[i]
                    rel_action = self.id2relation[action]
                    tp_obj[j]['rel_action'] = rel_action
                    tp_obj[j]['action'] = str(action)
                    # if attn_list is not None:
                    #     attention_tp = attention[i]
                    #     tp_obj[j]['attention'] = attention_tp.tolist()
        return obj_list

    def evaluate(self, valid_data, test_batch_size=20, write_info=False):
        write_info = True
        self.model.eval()
        self.count = 0
        eps = self.eps
        id2entity = self.id2entity
        eval_loss, eval_acc, eval_max_acc = [], [], []
        f1s, hits, ems,  precisions, recalls = [], [], [], [], []
        valid_data.reset_batches(is_sequential=True)
        num_epoch = math.ceil(valid_data.num_data / test_batch_size)
        if write_info and self.file_write is None:
            filename = os.path.join(self.args['checkpoint_dir'],
                                    "{}_test.info".format(self.args['experiment_name']))
            self.file_write = open(filename, "w")
        case_ct = {}
        max_local_entity = valid_data.max_local_entity
        ignore_prob = (1 - eps) / max_local_entity
        for iteration in tqdm(range(num_epoch)):
            batch = valid_data.get_batch(iteration, test_batch_size, fact_dropout=0.0, test=True)
            with torch.no_grad():
                loss, extras, pred_dist, tp_list = self.model(batch[:-1])
                pred = torch.max(pred_dist, dim=1)[1]
            if self.model_name == 'GraftNet':
                local_entity, query_entities, _, _, query_text, _, \
                    seed_dist, true_batch_id, answer_dist, answer_list = batch
            else:
                local_entity, query_entities, _, query_text, \
                    seed_dist, true_batch_id, answer_dist, answer_list = batch
            # self.true_batch_id = true_batch_id
            if write_info:
                obj_list = self.write_info(valid_data, tp_list, self.model.num_iter)
                # pred_sum = torch.sum(pred_dist, dim=1)
                # print(pred_sum)
            candidate_entities = torch.from_numpy(local_entity).type('torch.LongTensor')
            true_answers = torch.from_numpy(answer_dist).type('torch.FloatTensor')
            query_entities = torch.from_numpy(query_entities).type('torch.LongTensor')
            # acc, max_acc = cal_accuracy(pred, true_answers.cpu().numpy())
            eval_loss.append(loss.item())
            # eval_acc.append(acc)
            # eval_max_acc.append(max_acc)
            #pr_dist2 = pred_dist#.copy()
            #pred_dist = pr_dist2[-1]
            batch_size = pred_dist.size(0)
            batch_answers = answer_list
            batch_candidates = candidate_entities
            pad_ent_id = len(id2entity)
            #pr_dist2 = pred_dist.copy()
            #for pred_dist in pr_dist2:
            for batch_id in range(batch_size):
                answers = batch_answers[batch_id]
                candidates = batch_candidates[batch_id, :].tolist()
                probs = pred_dist[batch_id, :].tolist()
                seed_entities = query_entities[batch_id, :].tolist()
                #print(seed_entities)
                #print(candidates)
                candidate2prob = []
                for c, p, s in zip(candidates, probs, seed_entities):
                    if s == 1.0:
                        # ignore seed entities
                        #print(c, self.id2entity)
                        # print(c, p, s)
                        # if c < pad_ent_id:
                        #     tp_obj['seed'] = self.id2entity[c]
                        continue
                    if c == pad_ent_id:
                        continue
                    if p < ignore_prob:
                        continue
                    candidate2prob.append((c, p))
                precision, recall, f1, hit, em, case, retrived , ans = f1_and_hits(answers, candidate2prob, self.id2entity, self.entity2name ,eps)
                if write_info:
                    tp_obj = obj_list[batch_id]
                    tp_obj['answers'] = ans
                    tp_obj['precison'] = precision
                    tp_obj['recall'] = recall
                    tp_obj['f1'] = f1
                    tp_obj['hit'] = hit
                    tp_obj['em'] = em
                    tp_obj['cand'] = retrived
                    self.file_write.write(json.dumps(tp_obj) + "\n")
                case_ct.setdefault(case, 0)
                case_ct[case] += 1
                f1s.append(f1)
                hits.append(hit)
                ems.append(em)
                precisions.append(precision)
                recalls.append(recall)
        print('evaluation.......')
        print('how many eval samples......', len(f1s))
        # print('avg_f1', np.mean(f1s))
        print('avg_em', np.mean(ems))
        print('avg_hits', np.mean(hits))
        print('avg_f1', np.mean(f1s))
        print('avg_precision', np.mean(precisions))
        print('avg_recall', np.mean(recalls))

        print(case_ct)
        
        # 输出AMHR统计信息（如果启用）
        self._log_amhr_evaluation_summary()
        
        if write_info:
            self.file_write.close()
            self.file_write = None
        return np.mean(f1s), np.mean(hits), np.mean(ems)

    def _log_amhr_evaluation_summary(self):
        """
        在评估结束时输出AMHR统计摘要
        """
        if not hasattr(self.model, 'get_amhr_summary'):
            return
        
        try:
            amhr_summary = self.model.get_amhr_summary()
            
            if amhr_summary.get('amhr_enabled', False):
                print('\n' + '='*60)
                print('🧠 AMHR反思机制评估摘要')
                print('='*60)
                
                # 全局统计
                global_stats = amhr_summary.get('global_stats', {})
                if global_stats:
                    print(f"📊 总体质量评估: {global_stats.get('overall_quality', '未知')}")
                    print(f"🔍 微观反思总次数: {global_stats.get('total_micro_count', 0)}")
                    print(f"🌍 宏观反思总次数: {global_stats.get('total_macro_count', 0)}")
                    print(f"⚖️  平均宏观/微观比例: {global_stats.get('avg_ratio', 0):.3f}")
                    
                    # 判断反思平衡质量
                    ratio = global_stats.get('avg_ratio', 0)
                    if ratio < 0.1:
                        print("🔍 分析: 微观主导型 - 注重局部细节优化")
                        print("   建议: 可以考虑增加宏观反思频率以提升全局视野")
                    elif 0.3 <= ratio <= 1.0:
                        print("✅ 分析: 理想平衡型 - 微观宏观协调良好")
                        print("   状态: 反思机制运行在最佳平衡状态")
                    elif ratio > 2.0:
                        print("🌍 分析: 宏观主导型 - 注重全局方向调整")
                        print("   建议: 可以考虑增加微观反思以优化细节")
                    else:
                        print("⚠️  分析: 需要调整平衡比例")
                        print("   建议: 通过参数调优找到更好的平衡点")
                
                # 各模块详细统计
                reforms_stats = amhr_summary.get('reforms', {})
                if reforms_stats:
                    print(f"\n📈 各QueryReform模块详细统计:")
                    for reform_name, stats in reforms_stats.items():
                        print(f"  📋 {reform_name}:")
                        print(f"    🔄 微观EMA强度: {stats.get('micro_ema', 0):.4f}")
                        print(f"    🔄 宏观EMA强度: {stats.get('macro_ema', 0):.4f}")
                        print(f"    📊 比例: {stats.get('ratio', 0):.3f}")
                        print(f"    🎯 质量评价: {stats.get('quality', '未知')}")
                        
                        # 添加模块级建议
                        module_ratio = stats.get('ratio', 0)
                        if module_ratio == 0:
                            print(f"    💡 建议: {reform_name} 缺少宏观反思，可能需要调整参数")
                        elif module_ratio > 3.0:
                            print(f"    💡 建议: {reform_name} 宏观反思过多，可能影响效率")
                        
                print(f"\n🎯 性能影响评估:")
                total_reflections = global_stats.get('total_micro_count', 0) + global_stats.get('total_macro_count', 0)
                if total_reflections > 0:
                    efficiency_score = min(100, (1000 / total_reflections) * 100)
                    print(f"  ⚡ 效率评分: {efficiency_score:.1f}/100")
                    if efficiency_score > 80:
                        print("  ✅ 反思频率合理，对性能影响较小")
                    elif efficiency_score > 60:
                        print("  ⚠️  反思频率中等，可能轻微影响性能")
                    else:
                        print("  🔴 反思频率较高，可能显著影响性能")
                
                # 保存AMHR摘要到文件
                if hasattr(self, 'args') and 'checkpoint_dir' in self.args:
                    amhr_file = os.path.join(
                        self.args['checkpoint_dir'],
                        f"{self.args.get('experiment_name', 'model')}_amhr_summary.json"
                    )
                    try:
                        # 添加时间戳和评估结果
                        import time
                        amhr_summary['evaluation_timestamp'] = time.strftime('%Y-%m-%d %H:%M:%S')
                        amhr_summary['evaluation_context'] = {
                            'num_samples_evaluated': len(f1s) if 'f1s' in locals() else 0,
                            'avg_f1': np.mean(f1s) if 'f1s' in locals() and len(f1s) > 0 else 0,
                            'avg_hits': np.mean(hits) if 'hits' in locals() and len(hits) > 0 else 0,
                        }
                        
                        with open(amhr_file, 'w', encoding='utf-8') as f:
                            json.dump(amhr_summary, f, indent=2, ensure_ascii=False)
                        print(f"\n💾 AMHR摘要已保存到: {amhr_file}")
                    except Exception as e:
                        print(f"❌ 保存AMHR摘要失败: {e}")
                
                # 生成改进建议
                print(f"\n💡 改进建议:")
                if global_stats.get('avg_ratio', 0) < 0.2:
                    print("  🔹 考虑增加 --amhr_max_macro_rounds 参数")
                elif global_stats.get('avg_ratio', 0) > 1.5:
                    print("  🔹 考虑减少 --amhr_max_macro_rounds 或增加 --amhr_max_depth")
                
                if global_stats.get('total_micro_count', 0) < 10:
                    print("  🔹 微观反思次数较少，可能需要更多训练iteration")
                
                print('='*60)
            else:
                print("\n📝 AMHR机制未启用")
                print("   要启用AMHR，请在训练时添加参数: --enable_amhr True")
                
        except Exception as e:
            print(f"\n❌ 输出AMHR统计摘要时出错: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")

    def get_evaluation_metrics_with_amhr(self, valid_data, test_batch_size=20):
        """
        获取包含AMHR信息的评估指标
        
        Returns:
            dict: 包含标准评估指标和AMHR统计的字典
        """
        # 执行标准评估
        f1, hits, em = self.evaluate(valid_data, test_batch_size, write_info=False)
        
        # 获取AMHR统计
        amhr_stats = {}
        if hasattr(self.model, 'get_amhr_summary'):
            try:
                amhr_summary = self.model.get_amhr_summary()
                amhr_stats = amhr_summary
            except Exception as e:
                print(f"获取AMHR统计失败: {e}")
        
        # KGE集成信息
        kge_info = {}
        if hasattr(self.model, 'use_complex_kge') and self.model.use_complex_kge:
            kge_info = {
                'kge_enabled': True,
                'kge_model_type': getattr(self.model, 'kge_model_type', 'ComplX'),
                'fusion_method': getattr(self.model, 'kge_fusion_method', 'concat'),
                'hidden_dim': getattr(self.model, 'complex_hidden_dim', 50),
                'parameters_frozen': getattr(self.model, 'freeze_kge_embeddings', True)
            }
        else:
            kge_info = {'kge_enabled': False}

        return {
            'standard_metrics': {
                'f1': f1,
                'hits': hits,
                'em': em
            },
            'amhr_stats': amhr_stats,
            'kge_info': kge_info,
            'evaluation_summary': {
                'amhr_enabled': amhr_stats.get('amhr_enabled', False),
                'kge_enabled': kge_info.get('kge_enabled', False),
                'reflection_quality': amhr_stats.get('global_stats', {}).get('overall_quality', 'Unknown'),
                'reflection_balance': amhr_stats.get('global_stats', {}).get('avg_ratio', 0)
            }
        }



