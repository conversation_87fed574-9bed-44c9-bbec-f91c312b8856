"""
CCMGT训练器
基于三个项目的训练策略整合:
1. SimKGC的对比学习训练
2. GraphMAE的掩码预训练
3. KGTransformer的多任务学习

实现多阶段训练和质量保障机制
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, List, Tuple, Optional, Any
import logging
import numpy as np
from tqdm import tqdm
import os
import time
import math
from collections import defaultdict

from .ccmgt_config import CCMGTConfig
from .ccmgt_model import CCMGT
from .negative_sampling import TripletDict, TripletExample, HardNegativeSampler
from .loss_functions import compute_metrics

logger = logging.getLogger(__name__)


# 删除MultiOptimizer类，使用单一优化器策略（参考SOTA模型）

class CCMGTTrainer:
    """
    CCMGT训练器
    整合多阶段训练策略
    """
    
    def __init__(self,
                 model: CCMGT,
                 config: CCMGTConfig,
                 train_triples: List[Tuple[int, int, int]],
                 valid_triples: Optional[List[Tuple[int, int, int]]] = None,
                 entity2id: Optional[Dict[str, int]] = None,
                 relation2id: Optional[Dict[str, int]] = None,
                 device: Optional[torch.device] = None):
        
        # 智能设备检测
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model = model.to(device)
        self.config = config
        self.device = device
        
        # 数据
        self.train_triples = train_triples
        self.valid_triples = valid_triples or train_triples[:200]  # 默认取前200个作为验证
        self.entity2id = entity2id or {}
        self.relation2id = relation2id or {}
        
        # 构建三元组字典 (用于SimKGC的triplet mask)
        self.triplet_dict = TripletDict()
        self.triplet_dict.add_triplets(train_triples)
        
        # 更新负采样器的batch size (根据实际训练调整)
        if hasattr(self.model.core, 'negative_sampler'):
            self.model.core.negative_sampler.batch_size = 128  # 匹配实际batch size
        
        # 优化器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 🔥 混合精度训练支持 (SimKGC/GraphMAE策略)
        self.use_amp = getattr(config, 'use_amp', True)  # 默认启用
        if self.use_amp and torch.cuda.is_available():
            # 🔥 使用新版API避免FutureWarning
            try:
                # PyTorch 2.0+ 新API
                self.scaler = torch.amp.GradScaler('cuda')
                logger.info("混合精度训练已启用 (AMP) - 使用新版API")
            except AttributeError:
                # 旧版API兼容
                self.scaler = torch.cuda.amp.GradScaler()
                logger.info("混合精度训练已启用 (AMP) - 使用旧版API")
        else:
            self.scaler = None
            logger.info("混合精度训练未启用")
        
        # 训练状态
        self.current_epoch = 0
        self.patience_counter = 0
        self.training_history = defaultdict(list)
        
        # 🔥 多指标最佳模型追踪
        self.best_metrics = {
            'mrr': 0.0,
            'hits@1': 0.0,
            'hits@3': 0.0,
            'hits@10': 0.0
        }
        self.best_epochs = {
            'mrr': 0,
            'hits@1': 0,
            'hits@3': 0,
            'hits@10': 0
        }
        
        # 阶段性训练参数
        self.warmup_epochs = int(config.warmup_ratio * 200)  # 假设总共200轮
        self.refinement_start_epoch = int((1 - config.refinement_ratio) * 200)
        
        logger.info(f"CCMGT训练器初始化完成:")
        logger.info(f"  训练三元组: {len(train_triples)}")
        logger.info(f"  验证三元组: {len(self.valid_triples)}")
        logger.info(f"  预热阶段: 前{self.warmup_epochs}轮")
        logger.info(f"  精炼阶段: 第{self.refinement_start_epoch}轮开始")
    
    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器 - 简化版本，统一使用Adam处理所有密集参数"""
        # 获取所有可训练参数
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        
        if not trainable_params:
            raise ValueError("没有找到需要训练的参数")
        
        logger.info(f"总参数数量: {len(trainable_params)}个")
        
        # 🔧 简化：统一使用Adam优化器（保持原有逻辑）
        optimizer = optim.Adam(
            trainable_params,
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        logger.info("优化器创建完成: Adam")
        
        return optimizer
    
    def _create_scheduler(self) -> optim.lr_scheduler._LRScheduler:
        """创建SimKGC风格的学习率调度器"""
        # 🔥 修复：计算真实的训练步数 - 每个batch更新一次
        actual_batch_size = 32
        gradient_accumulation_steps = 8
        effective_batch_size = actual_batch_size * gradient_accumulation_steps
        num_batches_per_epoch = len(self.train_triples) // effective_batch_size
        num_training_steps = 200 * num_batches_per_epoch  # 真实训练步数
        warmup_steps = min(num_training_steps // 10, 1000)  # warmup 10%的步数，最多1000步
        
        logger.info(f"🔥 学习率调度器配置: 总步数={num_training_steps}, 预热步数={warmup_steps}, 每轮{num_batches_per_epoch}步")
        
        return self._create_simkgc_lr_scheduler(
            lr_scheduler_type=getattr(self.config, 'lr_scheduler', 'linear'),
            num_training_steps=num_training_steps,
            warmup_steps=warmup_steps
        )
    
    def _create_simkgc_lr_scheduler(self,
                                  lr_scheduler_type: str = 'linear',
                                  num_training_steps: int = 1000,
                                  warmup_steps: int = 100) -> optim.lr_scheduler._LRScheduler:
        """
        创建SimKGC风格的学习率调度器
        基于SimKGC/trainer.py的_create_lr_scheduler方法 (L197-209)
        """
        if lr_scheduler_type == 'linear':
            # 线性调度：warmup后线性衰减到0
            return optim.lr_scheduler.LambdaLR(
                self.optimizer,
                lambda step: min(1.0, step / warmup_steps) if step < warmup_steps 
                else max(0.0, (num_training_steps - step) / (num_training_steps - warmup_steps))
            )
        elif lr_scheduler_type == 'cosine':
            # 余弦调度：warmup后余弦衰减
            return optim.lr_scheduler.LambdaLR(
                self.optimizer,
                lambda step: min(1.0, step / warmup_steps) if step < warmup_steps
                else 0.5 * (1 + math.cos(math.pi * (step - warmup_steps) / (num_training_steps - warmup_steps)))
            )
        else:
            # 默认使用余弦调度
            logger.warning(f"未知的学习率调度器: {lr_scheduler_type}，使用cosine调度")
            return self._create_simkgc_lr_scheduler('cosine', num_training_steps, warmup_steps)
    
    def _get_training_phase(self, epoch: int) -> str:
        """确定当前训练阶段"""
        if epoch < self.warmup_epochs:
            return "warmup"
        elif epoch >= self.refinement_start_epoch:
            return "refinement"
        else:
            return "main"
    
    def _create_batch_examples(self, 
                             heads: torch.Tensor,
                             relations: torch.Tensor,
                             tails: torch.Tensor) -> List[TripletExample]:
        """创建batch的三元组样本 (用于SimKGC)"""
        examples = []
        for h, r, t in zip(heads.cpu().numpy(), relations.cpu().numpy(), tails.cpu().numpy()):
            examples.append(TripletExample(int(h), int(r), int(t)))
        return examples
    
    def _prepare_simkgc_batch(self,
                            heads: torch.Tensor,
                            relations: torch.Tensor,
                            tails: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        准备SimKGC风格的批次数据
        使用批次内对比学习，无需显式负采样
        """
        batch_size = heads.size(0)
        
        # SimKGC核心: 负样本来自批次内其他样本
        # 不需要额外生成负样本，节省大量内存
        return {
            'heads': heads,
            'relations': relations, 
            'tails': tails,
            'batch_size': batch_size
        }
    
    def _sparse_aware_grad_clip(self, max_norm: float):
        """
        稀疏梯度感知的梯度裁剪
        解决torch.nn.utils.clip_grad_norm_与稀疏张量的兼容性问题
        """
        parameters = [p for p in self.model.parameters() if p.grad is not None]
        
        if len(parameters) == 0:
            return torch.tensor(0.)
        
        # 分离稀疏和密集梯度
        sparse_grads = []
        dense_grads = []
        
        for p in parameters:
            if p.grad.is_sparse:
                sparse_grads.append(p.grad)
            else:
                dense_grads.append(p.grad)
        
        # 计算梯度范数
        total_norm = 0.0
        
        # 处理密集梯度
        if dense_grads:
            dense_norm = torch.nn.utils.clip_grad_norm_(
                [p for p in parameters if not p.grad.is_sparse], 
                float('inf')  # 不实际裁剪，只计算范数
            )
            total_norm += dense_norm.item() ** 2
        
        # 处理稀疏梯度
        for grad in sparse_grads:
            # 对于稀疏梯度，只计算非零元素的范数
            # 🔥 修复：确保在访问values之前调用coalesce
            if grad.is_sparse:
                grad = grad.coalesce()  # 确保稀疏张量是合并格式
                sparse_norm = torch.norm(grad.values()).item()
            else:
                sparse_norm = torch.norm(grad).item()
            total_norm += sparse_norm ** 2
        
        total_norm = total_norm ** 0.5
        
        # 计算裁剪系数
        clip_coef = max_norm / (total_norm + 1e-6)
        
        # 如果需要裁剪
        if clip_coef < 1:
            # 裁剪密集梯度
            for p in parameters:
                if not p.grad.is_sparse:
                    p.grad.mul_(clip_coef)
            
            # 裁剪稀疏梯度 (只裁剪values部分)
            for p in parameters:
                if p.grad.is_sparse:
                    # 🔥 修复：确保稀疏张量处理的一致性
                    p.grad = p.grad.coalesce()  # 确保是合并格式
                    # 创建新的稀疏张量，裁剪values
                    indices = p.grad.indices()
                    values = p.grad.values() * clip_coef
                    p.grad = torch.sparse_coo_tensor(
                        indices, values, p.grad.size(), device=p.grad.device
                    ).coalesce()  # 确保结果也是合并格式
        
        return torch.tensor(total_norm)
    

    
    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        phase = self._get_training_phase(epoch)
        
        # 动态调整损失权重
        self.model.core.unified_loss.update_weights(epoch, 200, phase)
        
        # 准备数据 - 🔥 显存优化策略
        actual_batch_size = 32  # 大幅减少实际batch size (原来是128)
        gradient_accumulation_steps = 8  # 增加梯度累积 (原来是2)
        effective_batch_size = actual_batch_size * gradient_accumulation_steps  # 有效batch size = 32 * 8 = 256
        
        num_batches = len(self.train_triples) // effective_batch_size
        
        epoch_metrics = defaultdict(list)
        
        pbar = tqdm(range(num_batches), desc=f"Epoch {epoch} ({phase})")
        
        # 梯度累积循环
        accumulated_loss = 0.0
        self.optimizer.zero_grad()
        
        for batch_idx in pbar:
            for accumulation_step in range(gradient_accumulation_steps):
                # 1. 准备batch数据
                global_step = batch_idx * gradient_accumulation_steps + accumulation_step
                start_idx = global_step * actual_batch_size
                end_idx = min(start_idx + actual_batch_size, len(self.train_triples))
                batch_triples = self.train_triples[start_idx:end_idx]
                
                if len(batch_triples) < actual_batch_size:
                    continue  # 跳过不完整的batch
                
                # 转换为tensor
                heads = torch.tensor([t[0] for t in batch_triples], device=self.device)
                relations = torch.tensor([t[1] for t in batch_triples], device=self.device)
                tails = torch.tensor([t[2] for t in batch_triples], device=self.device)
                
                # 2. 准备SimKGC批次数据 (无需显式负采样)
                batch_data = self._prepare_simkgc_batch(heads, relations, tails)
                
                # 3. 创建batch examples (用于SimKGC triplet mask)
                batch_examples = self._create_batch_examples(heads, relations, tails)
                
                # 4. 子采样权重 (ComplX策略)
                subsampling_weight = torch.ones(actual_batch_size, device=self.device)
                
                # 5. 前向传播和损失计算 (SimKGC风格)
                # 🔥 混合精度训练上下文管理器
                if self.use_amp and self.scaler is not None:
                    # 🔥 使用新版API避免FutureWarning
                    try:
                        # PyTorch 2.0+ 新API
                        with torch.amp.autocast('cuda'):
                            losses = self.model.compute_loss(
                                heads, relations, tails,
                                negative_tail_ids=None,  # 不使用显式负样本
                                subsampling_weight=subsampling_weight,
                                batch_examples=batch_examples,
                                triplet_dict=self.triplet_dict
                            )
                    except (AttributeError, TypeError):
                        # 旧版API兼容
                        with torch.cuda.amp.autocast():
                            losses = self.model.compute_loss(
                                heads, relations, tails,
                                negative_tail_ids=None,  # 不使用显式负样本
                                subsampling_weight=subsampling_weight,
                                batch_examples=batch_examples,
                                triplet_dict=self.triplet_dict
                            )
                else:
                    losses = self.model.compute_loss(
                        heads, relations, tails,
                        negative_tail_ids=None,  # 不使用显式负样本
                        subsampling_weight=subsampling_weight,
                        batch_examples=batch_examples,
                        triplet_dict=self.triplet_dict
                    )
                
                total_loss = losses['total_loss'] / gradient_accumulation_steps
                accumulated_loss += total_loss.item()
                
                # 6. 反向传播 (混合精度梯度累积)
                if self.use_amp and self.scaler is not None:
                    # 混合精度反向传播
                    self.scaler.scale(total_loss).backward()
                else:
                    # 标准反向传播
                    total_loss.backward()
            
            # 7. 梯度裁剪和优化器步骤 (每个有效batch后执行)
            if self.use_amp and self.scaler is not None:
                # 混合精度优化步骤
                self.scaler.unscale_(self.optimizer)
                # 稀疏梯度感知的梯度裁剪
                total_norm = self._sparse_aware_grad_clip(self.config.grad_clip)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # 标准优化步骤
                # 🔥 使用稀疏梯度感知的梯度裁剪
                total_norm = self._sparse_aware_grad_clip(self.config.grad_clip)
                self.optimizer.step()
            
            # 🔥 修复：每个batch后调用学习率调度器 (SimKGC策略)
            self.scheduler.step()
            
            self.optimizer.zero_grad()
            
            # 8. 记录指标 (使用累积损失)
            epoch_metrics['total_loss'].append(accumulated_loss)
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{accumulated_loss:.4f}',
                'Phase': phase,
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}',
                'EffBS': effective_batch_size,  # 显示有效batch size
                'Step': f'{global_step + 1}/{num_batches * gradient_accumulation_steps}' # 添加步数统计
            })
            
            # 重置累积损失
            accumulated_loss = 0.0
        
        # 🔥 删除：学习率调度已移到每个batch中
        # self.scheduler.step()  # 现在每个batch调用，不是每个epoch
        
        # 计算epoch平均指标
        avg_metrics = {}
        for key, values in epoch_metrics.items():
            if values:
                avg_metrics[key] = np.mean(values)
        
        avg_metrics['phase'] = phase
        avg_metrics['learning_rate'] = self.optimizer.param_groups[0]['lr']
        
        return avg_metrics
    
    def evaluate(self, eval_triples: Optional[List[Tuple[int, int, int]]] = None) -> Dict[str, float]:
        """评估模型 - 修复：使用SimKGC的内积相似度评估"""
        self.model.eval()
        
        if eval_triples is None:
            eval_triples = self.valid_triples
        
        # 采样评估 (避免评估太慢)
        eval_sample_size = min(50, len(eval_triples))  # 🔥 进一步减少样本数
        sampled_triples = eval_triples[:eval_sample_size]
        
        metrics = {'mrr': 0.0, 'hits@1': 0.0, 'hits@3': 0.0, 'hits@10': 0.0}
        total_samples = 0
        
        with torch.no_grad():
            # 🔥 修复1: 使用候选实体采样代替全实体计算 (内存友好)
            num_candidates = min(10000, self.config.num_entities)  # 采样1万个候选实体
            candidate_entities = torch.randperm(self.config.num_entities)[:num_candidates].to(self.device)
            
            logger.debug(f"使用{num_candidates}个候选实体进行评估...")
            
            # 分批计算候选实体表示
            entity_batch_size = 5000  # 每批5000个实体
            candidate_vectors = []
            
            for start_idx in range(0, len(candidate_entities), entity_batch_size):
                end_idx = min(start_idx + entity_batch_size, len(candidate_entities))
                batch_entity_ids = candidate_entities[start_idx:end_idx]
                
                # 使用虚拟的head/relation来获取实体表示
                dummy_head = torch.zeros_like(batch_entity_ids)
                dummy_relation = torch.zeros_like(batch_entity_ids)
                
                # 获取模型的forward输出，提取融合后的tail表示
                batch_output = self.model.core(dummy_head, dummy_relation, batch_entity_ids, mode="eval")
                batch_vectors = batch_output['fused']['fused_tail']  # [batch_size, dim]
                candidate_vectors.append(batch_vectors)
            
            # 拼接所有批次
            all_entity_vectors = torch.cat(candidate_vectors, dim=0)
            logger.debug(f"候选实体表示维度: {all_entity_vectors.shape}")
            
            for head, rel, tail in sampled_triples:
                # 确保目标实体在候选中
                if tail not in candidate_entities:
                    # 替换一个随机候选实体为目标实体
                    candidate_entities[-1] = tail
                    # 重新计算最后一个实体的表示
                    dummy_head = torch.tensor([0], device=self.device)
                    dummy_relation = torch.tensor([0], device=self.device)
                    tail_tensor = torch.tensor([tail], device=self.device)
                    tail_output = self.model.core(dummy_head, dummy_relation, tail_tensor, mode="eval")
                    all_entity_vectors[-1] = tail_output['fused']['fused_tail'].squeeze()
                
                # 🔥 修复2: 获取hr_vector (融合表示)
                head_tensor = torch.tensor([head], device=self.device)
                rel_tensor = torch.tensor([rel], device=self.device)
                dummy_tail = torch.tensor([0], device=self.device)  # 虚拟tail
                
                # 获取hr表示
                hr_output = self.model.core(head_tensor, rel_tensor, dummy_tail, mode="eval")
                hr_vector = hr_output['fused']['fused_hr_vector']  # [1, dim]
                
                # 🔥 修复3: 使用内积相似度计算分数 (SimKGC方式)
                scores = torch.mm(hr_vector, all_entity_vectors.t()).squeeze()  # [num_candidates]
                
                # 🔥 修复4: 在候选实体中找到正确答案的排名
                sorted_indices = torch.argsort(scores, descending=True)
                target_idx = (candidate_entities == tail).nonzero(as_tuple=True)[0].item()
                rank = (sorted_indices == target_idx).nonzero(as_tuple=True)[0].item() + 1
                
                # 更新指标
                metrics['mrr'] += 1.0 / rank
                if rank <= 1:
                    metrics['hits@1'] += 1
                if rank <= 3:
                    metrics['hits@3'] += 1
                if rank <= 10:
                    metrics['hits@10'] += 1
                
                total_samples += 1
                
                # 调试信息
                if total_samples <= 3:
                    logger.debug(f"样本{total_samples}: (h={head}, r={rel}, t={tail}) -> rank={rank}, score={scores[tail]:.4f}")
        
        # 计算平均值
        for key in metrics:
            metrics[key] = metrics[key] / total_samples if total_samples > 0 else 0.0
        
        logger.debug(f"评估完成: {total_samples}个样本, MRR={metrics['mrr']:.4f}")
        return metrics
    
    def train(self, 
             num_epochs: int = 200,
             eval_every: int = 1,  # 🔥 修复：每个epoch都评估
             save_every: int = 50,
             checkpoint_dir: str = "./ccmgt_checkpoints/") -> Dict[str, List[float]]:
        """
        完整训练流程
        
        Args:
            num_epochs: 训练轮数
            eval_every: 评估间隔
            save_every: 保存间隔
            checkpoint_dir: 检查点目录
        
        Returns:
            训练历史记录
        """
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        logger.info("🚀 开始CCMGT训练...")
        logger.info(f"  总轮数: {num_epochs}")
        logger.info(f"  评估间隔: {eval_every}")
        logger.info(f"  检查点目录: {checkpoint_dir}")
        
        training_start_time = time.time()
        
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(epoch)
            epoch_time = time.time() - epoch_start_time
            
            # 记录训练指标
            for key, value in train_metrics.items():
                self.training_history[f'train_{key}'].append(value)
            
            # 评估
            if epoch % eval_every == 0:
                eval_metrics = self.evaluate()
                
                # 记录评估指标
                for key, value in eval_metrics.items():
                    self.training_history[f'eval_{key}'].append(value)
                
                # 🔥 检查并保存各个指标的最佳模型
                self._save_best_models(eval_metrics, epoch, checkpoint_dir)
                
                # 早停逻辑（基于MRR）
                current_mrr = eval_metrics['mrr']
                if current_mrr > self.best_metrics['mrr']:
                    self.patience_counter = 0
                else:
                    self.patience_counter += 1
                
                # 记录日志
                logger.info(
                    f"Epoch {epoch}: "
                    f"Loss={train_metrics.get('total_loss', 0):.4f} "
                    f"MRR={current_mrr:.4f} "
                    f"Hits@10={eval_metrics['hits@10']:.4f} "
                    f"Phase={train_metrics.get('phase', 'unknown')} "
                    f"Time={epoch_time:.1f}s"
                )
                
                # 早停检查
                if current_mrr < self.config.min_mrr_threshold and epoch > 20:
                    logger.warning(f"MRR ({current_mrr:.4f}) 低于阈值 ({self.config.min_mrr_threshold})")
                
                if self.patience_counter >= self.config.patience:
                    logger.info(f"早停: {self.config.patience}个评估周期无改善")
                    break
            
            # 定期保存
            if epoch % save_every == 0:
                checkpoint_path = os.path.join(checkpoint_dir, f'ccmgt_epoch_{epoch}.pth')
                self.save_checkpoint(checkpoint_path, epoch)
        
        # 保存最终模型
        final_path = os.path.join(checkpoint_dir, 'ccmgt_final.pth')
        self.save_checkpoint(final_path, num_epochs - 1)
        
        # 🔥 生成训练摘要文件
        self._save_training_summary(checkpoint_dir, time.time() - training_start_time, num_epochs)
        
        total_time = time.time() - training_start_time
        logger.info(f"🎯 训练完成!")
        logger.info(f"📊 各指标最佳结果:")
        for metric, value in self.best_metrics.items():
            epoch = self.best_epochs[metric]
            logger.info(f"  最佳{metric.upper()}: {value:.4f} (Epoch {epoch}) → ccmgt_best_{metric}.pth")
        logger.info(f"  总训练时间: {total_time:.1f}秒")
        logger.info(f"  平均每轮: {total_time/num_epochs:.1f}秒")
        logger.info(f"📁 已保存模型文件:")
        logger.info(f"  🏆 ccmgt_best.pth - 综合最佳模型 (基于MRR)")
        logger.info(f"  📊 ccmgt_best_*.pth - 各指标最佳模型")
        logger.info(f"  📄 training_summary.json - 训练摘要")
        logger.info(f"  🏁 ccmgt_final.pth - 最终模型")
        
        return dict(self.training_history)
    
    def _save_training_summary(self, checkpoint_dir: str, total_time: float, num_epochs: int):
        """保存训练摘要到文件"""
        import json
        from datetime import datetime
        
        summary = {
            'training_completed_at': datetime.now().isoformat(),
            'total_epochs': num_epochs,
            'total_training_time_seconds': total_time,
            'average_time_per_epoch': total_time / num_epochs,
            'best_metrics': self.best_metrics,
            'best_epochs': self.best_epochs,
            'config_summary': {
                'learning_rate': self.config.learning_rate,
                'batch_size': getattr(self.config, 'batch_size', 'unknown'),
                'hidden_size': self.config.hidden_size,
                'embedding_dim': self.config.final_embedding_dim,
                'pre_batch': self.config.pre_batch,
                'temperature': self.config.temperature,
                'use_amp': self.use_amp
            },
            'model_files': {
                'best_overall': 'ccmgt_best.pth',
                'best_mrr': 'ccmgt_best_mrr.pth',
                'best_hits@1': '<EMAIL>',
                'best_hits@3': '<EMAIL>',
                'best_hits@10': '<EMAIL>',
                'final_model': 'ccmgt_final.pth'
            }
        }
        
        summary_path = os.path.join(checkpoint_dir, 'training_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 训练摘要已保存: {summary_path}")
    
    def _save_best_models(self, 
                         eval_metrics: Dict[str, float], 
                         epoch: int, 
                         checkpoint_dir: str):
        """检查并保存各个指标的最佳模型"""
        saved_any = False
        
        for metric_name, current_value in eval_metrics.items():
            if metric_name in self.best_metrics:
                if current_value > self.best_metrics[metric_name]:
                    # 更新最佳值
                    self.best_metrics[metric_name] = current_value
                    self.best_epochs[metric_name] = epoch
                    
                    # 保存模型
                    best_path = os.path.join(checkpoint_dir, f'ccmgt_best_{metric_name}.pth')
                    self.save_checkpoint(best_path, epoch, eval_metrics)
                    saved_any = True
                    
                    logger.info(f"✅ 新的最佳{metric_name.upper()}: {current_value:.4f} (Epoch {epoch})")
        
        # 如果有任何指标创新高，也保存一个通用的best模型（基于MRR）
        if saved_any and eval_metrics['mrr'] == self.best_metrics['mrr']:
            best_path = os.path.join(checkpoint_dir, 'ccmgt_best.pth')
            self.save_checkpoint(best_path, epoch, eval_metrics)
    
    def save_checkpoint(self, 
                       path: str, 
                       epoch: int, 
                       eval_metrics: Optional[Dict[str, float]] = None):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'config': self.config.to_dict(),
            'best_metrics': self.best_metrics,
            'best_epochs': self.best_epochs,
            'training_history': dict(self.training_history),
            'eval_metrics': eval_metrics,
            'patience_counter': self.patience_counter
        }
        
        torch.save(checkpoint, path)
        logger.debug(f"检查点已保存: {path}")
    
    def load_checkpoint(self, path: str) -> Dict[str, Any]:
        """加载检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_metrics = checkpoint.get('best_metrics', {'mrr': 0.0, 'hits@1': 0.0, 'hits@3': 0.0, 'hits@10': 0.0})
        self.best_epochs = checkpoint.get('best_epochs', {'mrr': 0, 'hits@1': 0, 'hits@3': 0, 'hits@10': 0})
        self.patience_counter = checkpoint.get('patience_counter', 0)
        self.training_history = defaultdict(list, checkpoint['training_history'])
        
        logger.info(f"检查点已加载: {path} (epoch {self.current_epoch})")
        
        return checkpoint
    
    def predict(self, 
               test_triples: List[Tuple[int, int, int]],
               batch_size: int = 256) -> List[Dict[str, Any]]:
        """批量预测"""
        self.model.eval()
        results = []
        
        with torch.no_grad():
            for i in range(0, len(test_triples), batch_size):
                batch = test_triples[i:i+batch_size]
                
                heads = torch.tensor([t[0] for t in batch], device=self.device)
                relations = torch.tensor([t[1] for t in batch], device=self.device)
                tails = torch.tensor([t[2] for t in batch], device=self.device)
                
                scores = self.model.score_triples(heads, relations, tails)
                
                for j, (h, r, t) in enumerate(batch):
                    results.append({
                        'head_id': h,
                        'relation_id': r,
                        'tail_id': t,
                        'score': scores[j].item()
                    })
        
        return results
    
    @staticmethod
    def load_best_model(checkpoint_dir: str, metric: str = 'mrr'):
        """
        加载指定指标的最佳模型
        
        Args:
            checkpoint_dir: 检查点目录
            metric: 要加载的指标 ('mrr', 'hits@1', 'hits@3', 'hits@10', 'overall')
        
        Returns:
            模型路径和最佳指标信息
        """
        if metric == 'overall':
            model_path = os.path.join(checkpoint_dir, 'ccmgt_best.pth')
        else:
            model_path = os.path.join(checkpoint_dir, f'ccmgt_best_{metric}.pth')
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 加载摘要信息
        summary_path = os.path.join(checkpoint_dir, 'training_summary.json')
        if os.path.exists(summary_path):
            import json
            with open(summary_path, 'r', encoding='utf-8') as f:
                summary = json.load(f)
            
            best_metrics = summary.get('best_metrics', {})
            best_epochs = summary.get('best_epochs', {})
            
            logger.info(f"✅ 找到最佳{metric.upper()}模型: {model_path}")
            if metric in best_metrics:
                logger.info(f"  最佳{metric.upper()}: {best_metrics[metric]:.4f} (Epoch {best_epochs[metric]})")
        
        return model_path


def create_trainer_from_config(config: CCMGTConfig,
                             train_triples: List[Tuple[int, int, int]],
                             valid_triples: Optional[List[Tuple[int, int, int]]] = None,
                             device: Optional[torch.device] = None) -> CCMGTTrainer:
    """从配置创建训练器"""
    # 智能设备检测
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = CCMGT(config)
    
    # 创建训练器
    trainer = CCMGTTrainer(
        model=model,
        config=config,
        train_triples=train_triples,
        valid_triples=valid_triples,
        device=device
    )
    
    return trainer 