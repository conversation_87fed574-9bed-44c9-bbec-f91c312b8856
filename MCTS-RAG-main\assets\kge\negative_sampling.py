"""
SimKGC风格的负采样模块
基于SimKGC/models.py和triplet_mask.py的精确实现

核心创新:
1. Pre-batch负样本机制 (L122-140)
2. Triplet mask过滤避免假负样本 (triplet_mask.py)
3. Self-negative机制
4. 动态温度调节
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Set
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class TripletDict:
    """
    三元组字典，用于triplet mask计算
    基于SimKGC/triplet.py的实现
    """
    
    def __init__(self):
        self.hr2tails: Dict[Tuple[int, int], Set[int]] = defaultdict(set)
        self.triplet_count = 0
    
    def add_triplet(self, head: int, relation: int, tail: int):
        """添加三元组"""
        key = (head, relation)
        self.hr2tails[key].add(tail)
        self.triplet_count += 1
    
    def add_triplets(self, triplets: List[Tuple[int, int, int]]):
        """批量添加三元组"""
        for head, relation, tail in triplets:
            self.add_triplet(head, relation, tail)
        logger.info(f"TripletDict加载{len(triplets)}个三元组")
    
    def get_neighbors(self, head: int, relation: int) -> Set[int]:
        """获取(head, relation)的所有邻居"""
        return self.hr2tails.get((head, relation), set())


class TripletExample:
    """
    三元组样本类
    基于SimKGC/doc.py的Example类简化版
    """
    
    def __init__(self, head_id: int, relation_id: int, tail_id: int):
        self.head_id = head_id
        self.relation_id = relation_id  
        self.tail_id = tail_id


def construct_triplet_mask(row_examples: List[TripletExample], 
                          col_examples: Optional[List[TripletExample]], 
                          triplet_dict: TripletDict,
                          device: Optional[torch.device] = None) -> torch.Tensor:
    """
    构建triplet mask，过滤已知的正确三元组
    基于SimKGC/triplet_mask.py的construct_mask函数
    
    Args:
        row_examples: 行样本列表
        col_examples: 列样本列表，如果为None则使用row_examples
        triplet_dict: 三元组字典
        device: 目标设备，如果为None则自动检测
    
    Returns:
        triplet_mask: [num_row, num_col]的mask矩阵，True表示可以作为负样本
    """
    # 智能设备检测
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    positive_on_diagonal = col_examples is None
    num_row = len(row_examples)
    col_examples = row_examples if col_examples is None else col_examples
    num_col = len(col_examples)
    
    # 初始化mask矩阵
    # exact match: 不同tail_id的可以作为负样本
    row_tail_ids = torch.LongTensor([ex.tail_id for ex in row_examples]).to(device)
    col_tail_ids = row_tail_ids if positive_on_diagonal else torch.LongTensor([ex.tail_id for ex in col_examples]).to(device)
    
    # [num_row, num_col]: 不同tail_id的位置为True
    triplet_mask = (row_tail_ids.unsqueeze(1) != col_tail_ids.unsqueeze(0))
    
    if positive_on_diagonal:
        triplet_mask.fill_diagonal_(True)  # 对角线保持为True（正样本位置）
    
    # 过滤掉其他可能的邻居 (核心创新)
    for i in range(num_row):
        head_id, relation_id = row_examples[i].head_id, row_examples[i].relation_id
        neighbor_tail_ids = triplet_dict.get_neighbors(head_id, relation_id)
        
        # 如果只有一个邻居，不需要进一步检查
        if len(neighbor_tail_ids) <= 1:
            continue
        
        # 将所有已知邻居标记为False（不能作为负样本）
        for j in range(num_col):
            if i == j and positive_on_diagonal:
                continue  # 跳过对角线正样本
            
            tail_id = col_examples[j].tail_id
            if tail_id in neighbor_tail_ids:
                triplet_mask[i][j] = False
    
    return triplet_mask


def construct_self_negative_mask(examples: List[TripletExample], 
                                triplet_dict: TripletDict) -> torch.Tensor:
    """
    构建self-negative mask
    基于SimKGC/triplet_mask.py的construct_self_negative_mask
    """
    mask = torch.ones(len(examples))
    for idx, ex in enumerate(examples):
        head_id, relation_id = ex.head_id, ex.relation_id
        neighbor_tail_ids = triplet_dict.get_neighbors(head_id, relation_id)
        if head_id in neighbor_tail_ids:
            mask[idx] = 0
    return mask.bool()


class SimKGCNegativeSampler(nn.Module):
    """
    SimKGC风格的负采样器
    基于SimKGC/models.py的CustomBertModel实现
    """
    
    def __init__(self, 
                 embedding_dim: int,
                 batch_size: int,
                 pre_batch: int = 2,
                 pre_batch_weight: float = 0.5,
                 temperature: float = 0.05,
                 finetune_temperature: bool = True,
                 additive_margin: float = 0.02,
                 use_self_negative: bool = True,
                 device: Optional[torch.device] = None):
        super().__init__()
        
        # 智能设备检测
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.embedding_dim = embedding_dim
        self.batch_size = batch_size
        self.pre_batch = pre_batch
        self.pre_batch_weight = pre_batch_weight
        self.additive_margin = additive_margin
        self.use_self_negative = use_self_negative
        self.device = device
        
        # 可学习温度参数 (SimKGC核心创新)
        self.log_inv_t = nn.Parameter(
            torch.tensor(1.0 / temperature).log(), 
            requires_grad=finetune_temperature
        )
        
        # Pre-batch向量缓冲区 (SimKGC核心创新)
        if self.pre_batch > 0:
            num_pre_batch_vectors = max(1, self.pre_batch) * self.batch_size
            random_vector = torch.randn(num_pre_batch_vectors, self.embedding_dim)
            self.register_buffer(
                "pre_batch_vectors",
                F.normalize(random_vector, dim=1),
                persistent=False
            )
            self.offset = 0
            self.pre_batch_examples = [None for _ in range(num_pre_batch_vectors)]
        
        logger.info(f"SimKGC负采样器初始化: pre_batch={pre_batch}, temperature={temperature}")
    
    def compute_contrastive_logits(self, 
                                  hr_vector: torch.Tensor,
                                  tail_vector: torch.Tensor,
                                  head_vector: Optional[torch.Tensor] = None,
                                  batch_examples: Optional[List[TripletExample]] = None,
                                  triplet_mask: Optional[torch.Tensor] = None,
                                  self_negative_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        计算对比学习的logits
        基于SimKGC/models.py的compute_logits方法 (L86-115)
        
        Args:
            hr_vector: head+relation向量 [batch_size, embedding_dim]
            tail_vector: tail向量 [batch_size, embedding_dim]  
            head_vector: head向量，用于self-negative [batch_size, embedding_dim]
            batch_examples: 当前batch的三元组样本
            triplet_mask: 三元组mask [batch_size, batch_size]
            self_negative_mask: self-negative mask [batch_size]
        
        Returns:
            包含logits, labels等的字典
        """
        batch_size = hr_vector.size(0)
        labels = torch.arange(batch_size).to(hr_vector.device)
        
        # 1. 计算基础logits: hr_vector @ tail_vector.T
        logits = hr_vector.mm(tail_vector.t())  # [batch_size, batch_size]
        
        # 2. 训练时添加additive margin (对角线元素减去margin)
        if self.training:
            margin_mask = torch.zeros(logits.size()).fill_diagonal_(self.additive_margin).to(logits.device)
            logits = logits - margin_mask
        
        # 3. 应用温度缩放
        logits = logits * self.log_inv_t.exp()
        
        # 4. 应用triplet mask过滤已知正确三元组
        if triplet_mask is not None:
            logits.masked_fill_(~triplet_mask, -1e4)
        
        # 5. 添加pre-batch负样本 (SimKGC核心创新)
        if self.pre_batch > 0 and self.training and batch_examples is not None:
            pre_batch_logits = self._compute_pre_batch_logits(hr_vector, tail_vector, batch_examples)
            logits = torch.cat([logits, pre_batch_logits], dim=-1)  # [batch_size, batch_size + pre_batch_size]
        
        # 6. 添加self-negative (可选)
        if self.use_self_negative and self.training and head_vector is not None:
            self_neg_logits = torch.sum(hr_vector * head_vector, dim=1) * self.log_inv_t.exp()  # [batch_size]
            if self_negative_mask is not None:
                self_neg_logits.masked_fill_(~self_negative_mask, -1e4)
            logits = torch.cat([logits, self_neg_logits.unsqueeze(1)], dim=-1)  # [batch_size, total_size + 1]
        
        return {
            'logits': logits,
            'labels': labels,
            'temperature': self.log_inv_t.detach().exp(),
            'hr_vector': hr_vector.detach(),
            'tail_vector': tail_vector.detach()
        }
    
    def _compute_pre_batch_logits(self, 
                                 hr_vector: torch.Tensor,
                                 tail_vector: torch.Tensor,
                                 batch_examples: List[TripletExample]) -> torch.Tensor:
        """
        计算pre-batch负样本的logits
        基于SimKGC/models.py的_compute_pre_batch_logits方法 (L117-129)
        """
        assert tail_vector.size(0) == self.batch_size
        
        # 计算与历史batch向量的相似度
        pre_batch_logits = hr_vector.mm(self.pre_batch_vectors.clone().t())  # [batch_size, num_pre_batch_vectors]
        pre_batch_logits = pre_batch_logits * self.log_inv_t.exp() * self.pre_batch_weight
        
        # 应用pre-batch triplet mask (如果历史样本存在)
        if self.pre_batch_examples[-1] is not None:
            # 这里需要TripletDict，暂时简化处理
            # 实际实现中需要维护全局的TripletDict
            pass
        
        # 更新pre-batch缓冲区 (滑动窗口)
        self.pre_batch_vectors[self.offset:(self.offset + self.batch_size)] = tail_vector.data.clone()
        self.pre_batch_examples[self.offset:(self.offset + self.batch_size)] = batch_examples
        self.offset = (self.offset + self.batch_size) % len(self.pre_batch_examples)
        
        return pre_batch_logits
    
    def compute_infonce_loss(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """计算InfoNCE损失"""
        return F.cross_entropy(logits, labels)
    
    def get_temperature(self) -> float:
        """获取当前温度值"""
        return self.log_inv_t.exp().item()


class HardNegativeSampler:
    """
    硬负样本采样器
    基于embedding相似度动态选择困难负样本
    """
    
    def __init__(self, num_entities: int, embedding_dim: int, device: torch.device):
        self.num_entities = num_entities
        self.embedding_dim = embedding_dim
        self.device = device
    
    def sample_hard_negatives(self, 
                            positive_heads: torch.Tensor,
                            positive_relations: torch.Tensor, 
                            positive_tails: torch.Tensor,
                            entity_embeddings: torch.Tensor,
                            num_negatives: int = 32) -> torch.Tensor:
        """
        基于embedding相似度采样硬负样本
        
        Args:
            positive_heads: 正样本头实体 [batch_size]
            positive_relations: 正样本关系 [batch_size]
            positive_tails: 正样本尾实体 [batch_size]
            entity_embeddings: 实体嵌入矩阵 [num_entities, embedding_dim]
            num_negatives: 负样本数量
        
        Returns:
            negative_tails: 硬负样本尾实体 [batch_size, num_negatives]
        """
        batch_size = positive_tails.size(0)
        
        # 计算正样本尾实体与所有实体的相似度
        positive_tail_emb = entity_embeddings[positive_tails]  # [batch_size, embedding_dim]
        similarities = torch.mm(positive_tail_emb, entity_embeddings.t())  # [batch_size, num_entities]
        
        # 排除正样本本身
        similarities[torch.arange(batch_size), positive_tails] = -float('inf')
        
        # 选择最相似的实体作为硬负样本
        _, hard_negative_indices = torch.topk(similarities, num_negatives, dim=1)  # [batch_size, num_negatives]
        
        return hard_negative_indices 