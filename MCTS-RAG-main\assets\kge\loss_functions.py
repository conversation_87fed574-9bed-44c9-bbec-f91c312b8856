"""
CCMGT统一损失函数模块
整合三种核心损失函数:
1. ComplX链接预测损失
2. SimKGC对比学习损失 (InfoNCE)
3. GraphMAE重建损失 (SCE)

基于源码的精确实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any
import logging
from functools import partial

logger = logging.getLogger(__name__)


class ComplExLinkPredictionLoss(nn.Module):
    """
    ComplX链接预测损失
    基于标准的ComplX评分函数和负采样损失
    """
    
    def __init__(self, regularization: float = 1e-6):
        super().__init__()
        self.regularization = regularization
    
    def forward(self,
               positive_scores: torch.Tensor,
               negative_scores: torch.Tensor,
               subsampling_weight: Optional[torch.Tensor] = None,
               entity_embeddings: Optional[torch.Tensor] = None,
               relation_embeddings: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算ComplX链接预测损失
        
        Args:
            positive_scores: 正样本评分 [batch_size]
            negative_scores: 负样本评分 [batch_size, num_negatives]
            subsampling_weight: 子采样权重 [batch_size]
            entity_embeddings: 实体嵌入矩阵，用于正则化
            relation_embeddings: 关系嵌入矩阵，用于正则化
        
        Returns:
            链接预测损失
        """
        # sigmoid激活
        positive_score = torch.sigmoid(positive_scores)
        negative_score = torch.sigmoid(negative_scores)
        
        # 计算损失
        positive_sample_loss = -torch.log(positive_score + 1e-8)
        negative_sample_loss = -torch.log(1 - negative_score + 1e-8).mean(dim=1)
        
        # 应用子采样权重
        if subsampling_weight is not None:
            positive_sample_loss = (subsampling_weight * positive_sample_loss).sum()
            negative_sample_loss = (subsampling_weight * negative_sample_loss).sum()
            loss = (positive_sample_loss + negative_sample_loss) / subsampling_weight.sum()
        else:
            loss = (positive_sample_loss + negative_sample_loss).mean()
        
        # 添加L3正则化 (ComplX特有)
        if self.regularization > 0 and entity_embeddings is not None and relation_embeddings is not None:
            reg_loss = self.regularization * (
                entity_embeddings.norm(p=3)**3 +
                relation_embeddings.norm(p=3)**3
            )
            loss = loss + reg_loss
        
        return loss


class SimKGCContrastiveLoss(nn.Module):
    """
    SimKGC对比学习损失 (InfoNCE)
    基于SimKGC/models.py的精确实现
    """
    
    def __init__(self, 
                 temperature: float = 0.05,
                 additive_margin: float = 0.02):
        super().__init__()
        self.register_parameter('log_inv_t', nn.Parameter(torch.tensor(1.0 / temperature).log()))
        self.additive_margin = additive_margin
    
    def forward(self,
               hr_vector: torch.Tensor,
               tail_vector: torch.Tensor,
               head_vector: Optional[torch.Tensor] = None,
               triplet_mask: Optional[torch.Tensor] = None,
               self_negative_mask: Optional[torch.Tensor] = None,
               pre_batch_logits: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算SimKGC对比学习损失
        基于SimKGC/models.py的compute_logits方法
        
        Args:
            hr_vector: head+relation向量 [batch_size, hidden_dim]
            tail_vector: tail向量 [batch_size, hidden_dim]
            head_vector: head向量，用于self-negative [batch_size, hidden_dim]
            triplet_mask: 三元组过滤mask [batch_size, batch_size]
            self_negative_mask: self-negative mask [batch_size]
            pre_batch_logits: pre-batch负样本logits [batch_size, pre_batch_size]
        
        Returns:
            InfoNCE损失
        """
        batch_size = hr_vector.size(0)
        labels = torch.arange(batch_size).to(hr_vector.device)
        
        # 1. 计算基础logits矩阵
        logits = hr_vector.mm(tail_vector.t())  # [batch_size, batch_size]
        
        # 2. 添加additive margin (训练时)
        if self.training:
            margin_mask = torch.zeros(logits.size()).fill_diagonal_(self.additive_margin).to(logits.device)
            logits = logits - margin_mask
        
        # 3. 温度缩放
        logits = logits * self.log_inv_t.exp()
        
        # 4. 应用triplet mask
        if triplet_mask is not None:
            # 确保triplet_mask在正确的设备上
            triplet_mask = triplet_mask.to(logits.device)
            logits.masked_fill_(~triplet_mask, -1e4)
        
        # 5. 添加pre-batch负样本
        if pre_batch_logits is not None:
            logits = torch.cat([logits, pre_batch_logits], dim=-1)
        
        # 6. 添加self-negative
        if head_vector is not None and self_negative_mask is not None:
            self_neg_logits = torch.sum(hr_vector * head_vector, dim=1) * self.log_inv_t.exp()
            # 确保self_negative_mask在正确的设备上
            self_negative_mask = self_negative_mask.to(self_neg_logits.device)
            self_neg_logits.masked_fill_(~self_negative_mask, -1e4)
            logits = torch.cat([logits, self_neg_logits.unsqueeze(1)], dim=-1)
        
        # 7. 计算InfoNCE损失
        return F.cross_entropy(logits, labels)
    
    def get_temperature(self) -> float:
        """获取当前温度"""
        return self.log_inv_t.exp().item()


class GraphMAEReconstructionLoss(nn.Module):
    """
    GraphMAE重建损失
    基于GraphMAE/edcoder.py和loss_func.py的精确实现
    """
    
    def __init__(self, 
                 loss_fn: str = "sce",
                 alpha_l: float = 3):
        super().__init__()
        self.loss_fn = loss_fn
        self.alpha_l = alpha_l
        self.criterion = self._setup_loss_fn(loss_fn, alpha_l)
    
    def _setup_loss_fn(self, loss_fn: str, alpha_l: float):
        """设置损失函数"""
        if loss_fn == "mse":
            return nn.MSELoss()
        elif loss_fn == "sce":
            return partial(self._sce_loss, alpha=alpha_l)
        elif loss_fn == "sig":
            return self._sig_loss
        else:
            raise NotImplementedError(f"未支持的损失函数: {loss_fn}")
    
    def _sce_loss(self, x: torch.Tensor, y: torch.Tensor, alpha: float = 3) -> torch.Tensor:
        """
        SCE损失函数
        基于GraphMAE/loss_func.py的sce_loss
        """
        x = F.normalize(x, p=2, dim=-1)
        y = F.normalize(y, p=2, dim=-1)
        loss = (1 - (x * y).sum(dim=-1)).pow_(alpha)
        return loss.mean()
    
    def _sig_loss(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        """Sigmoid损失"""
        x = F.normalize(x, p=2, dim=-1)
        y = F.normalize(y, p=2, dim=-1)
        loss = (x * y).sum(1)
        loss = torch.sigmoid(-loss)
        return loss.mean()
    
    def forward(self,
               reconstructed: torch.Tensor,
               original: torch.Tensor,
               mask_indices: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算重建损失
        
        Args:
            reconstructed: 重建特征 [num_nodes, feature_dim]
            original: 原始特征 [num_nodes, feature_dim]
            mask_indices: 被掩码的节点索引
        
        Returns:
            重建损失
        """
        if mask_indices is not None and mask_indices.numel() > 0:
            # 只对被掩码的节点计算损失
            # 确保mask_indices是一维的
            if mask_indices.dim() == 0:
                # 0维tensor转换为1维
                mask_indices = mask_indices.unsqueeze(0)
            recon_masked = reconstructed[mask_indices]
            orig_masked = original[mask_indices]
            return self.criterion(recon_masked, orig_masked)
        else:
            # 对所有节点计算损失
            return self.criterion(reconstructed, original)


class KGTransformerMultiTaskLoss(nn.Module):
    """
    KGTransformer多任务损失
    基于KGTransformer/kg_bert.py的三个任务损失
    """
    
    def __init__(self):
        super().__init__()
        self.nll = nn.NLLLoss(ignore_index=-1)
        self.bce = nn.BCELoss(reduction='mean')
    
    def graph_classification_loss(self,
                                logits: torch.Tensor,
                                labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """图分类损失 (Task 0)"""
        if len(labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        target_labels = labels[:, 0] if labels.dim() > 1 else labels
        loss = self.nll(logits, target_labels)
        
        pred_labels = logits.detach().argmax(dim=1)
        acc = (pred_labels == target_labels).float().mean()
        
        return loss, acc
    
    def relation_prediction_loss(self,
                               logits: torch.Tensor,
                               labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """关系预测损失 (Task 1)"""
        valid_mask = labels != -1
        valid_labels = labels[valid_mask]
        
        if len(valid_labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        loss = self.nll(logits.transpose(1, 2), labels)
        
        pred_labels = logits.detach().argmax(dim=2)[valid_mask]
        acc = (pred_labels == valid_labels).float().mean()
        
        return loss, acc
    
    def entity_contrastive_loss(self,
                              hidden_states: torch.Tensor,
                              labels: torch.Tensor,
                              entity_embeddings: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """实体对比学习损失 (Task 2)"""
        valid_mask = labels != -1
        valid_labels = labels[valid_mask]
        
        if len(valid_labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        valid_hidden = hidden_states[valid_mask]
        valid_entity_emb = entity_embeddings[valid_labels]
        
        # 计算相似度矩阵
        similarity_matrix = torch.mm(valid_hidden, valid_entity_emb.t())
        similarity_matrix = F.sigmoid(similarity_matrix)
        
        # 构建标签矩阵
        label_matrix = (valid_labels.unsqueeze(1) == valid_labels.unsqueeze(0)).float()
        
        # 计算BCE损失
        positive_mask = label_matrix == 1
        negative_mask = label_matrix == 0
        
        pos_similarities = similarity_matrix[positive_mask]
        neg_similarities = similarity_matrix[negative_mask]
        
        loss_pos = self.bce(pos_similarities, label_matrix[positive_mask])
        loss_neg = self.bce(neg_similarities, label_matrix[negative_mask])
        loss = (loss_pos + loss_neg) / 2
        
        # 计算准确率
        acc_pos = (pos_similarities > 0.5).float().mean()
        acc_neg = (neg_similarities < 0.5).float().mean()
        acc = (acc_pos + acc_neg) / 2
        
        return loss, acc


class CCMGTUnifiedLoss(nn.Module):
    """
    CCMGT统一损失函数
    整合所有组件的损失
    """
    
    def __init__(self,
                 lambda_link: float = 1.0,
                 lambda_contrastive: float = 0.1,
                 lambda_recon: float = 0.1,
                 lambda_kgtrans: float = 0.1,
                 complex_regularization: float = 1e-6,
                 contrastive_temperature: float = 0.05,
                 recon_loss_fn: str = "sce",
                 recon_alpha: float = 3):
        super().__init__()
        
        # 损失权重
        self.lambda_link = lambda_link
        self.lambda_contrastive = lambda_contrastive
        self.lambda_recon = lambda_recon
        self.lambda_kgtrans = lambda_kgtrans
        
        # 各组件损失函数
        self.complex_loss = ComplExLinkPredictionLoss(complex_regularization)
        self.contrastive_loss = SimKGCContrastiveLoss(contrastive_temperature)
        self.reconstruction_loss = GraphMAEReconstructionLoss(recon_loss_fn, recon_alpha)
        self.kgtransformer_loss = KGTransformerMultiTaskLoss()
        
        logger.info(f"CCMGT统一损失初始化: λ_link={lambda_link}, λ_contr={lambda_contrastive}, "
                   f"λ_recon={lambda_recon}, λ_kgtrans={lambda_kgtrans}")
    
    def forward(self,
               loss_components: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        计算统一损失
        
        Args:
            loss_components: 包含各组件计算所需数据的字典
        
        Returns:
            包含各项损失和总损失的字典
        """
        losses = {}
        
        # 获取设备信息 - 优先使用CUDA
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        for component in loss_components.values():
            if isinstance(component, dict):
                for tensor in component.values():
                    if isinstance(tensor, torch.Tensor):
                        device = tensor.device
                        break
                else:
                    continue
                break
            elif isinstance(component, torch.Tensor):
                device = component.device
                break
        
        total_loss = torch.tensor(0.0, device=device)
        
        # 1. ComplX链接预测损失
        if 'complex' in loss_components:
            comp = loss_components['complex']
            link_loss = self.complex_loss(
                comp['positive_scores'],
                comp['negative_scores'],
                comp.get('subsampling_weight'),
                comp.get('entity_embeddings'),
                comp.get('relation_embeddings')
            )
            losses['link_loss'] = link_loss
            total_loss += self.lambda_link * link_loss
        
        # 2. SimKGC对比学习损失
        if 'contrastive' in loss_components:
            comp = loss_components['contrastive']
            contr_loss = self.contrastive_loss(
                comp['hr_vector'],
                comp['tail_vector'],
                comp.get('head_vector'),
                comp.get('triplet_mask'),
                comp.get('self_negative_mask'),
                comp.get('pre_batch_logits')
            )
            losses['contrastive_loss'] = contr_loss
            total_loss += self.lambda_contrastive * contr_loss
        
        # 3. GraphMAE重建损失
        if 'reconstruction' in loss_components:
            comp = loss_components['reconstruction']
            recon_loss = self.reconstruction_loss(
                comp['reconstructed'],
                comp['original'],
                comp.get('mask_indices')
            )
            losses['reconstruction_loss'] = recon_loss
            total_loss += self.lambda_recon * recon_loss
        
        # 4. KGTransformer多任务损失
        if 'kgtransformer' in loss_components:
            comp = loss_components['kgtransformer']
            kgt_loss = torch.tensor(0.0, device=total_loss.device)
            
            if 'graph_classification' in comp:
                gc_loss, gc_acc = self.kgtransformer_loss.graph_classification_loss(
                    comp['graph_classification']['logits'],
                    comp['graph_classification']['labels']
                )
                losses['graph_classification_loss'] = gc_loss
                losses['graph_classification_acc'] = gc_acc
                kgt_loss += gc_loss
            
            if 'relation_prediction' in comp:
                rp_loss, rp_acc = self.kgtransformer_loss.relation_prediction_loss(
                    comp['relation_prediction']['logits'],
                    comp['relation_prediction']['labels']
                )
                losses['relation_prediction_loss'] = rp_loss
                losses['relation_prediction_acc'] = rp_acc
                kgt_loss += rp_loss
            
            if 'entity_contrastive' in comp:
                ec_loss, ec_acc = self.kgtransformer_loss.entity_contrastive_loss(
                    comp['entity_contrastive']['hidden_states'],
                    comp['entity_contrastive']['labels'],
                    comp['entity_contrastive']['entity_embeddings']
                )
                losses['entity_contrastive_loss'] = ec_loss
                losses['entity_contrastive_acc'] = ec_acc
                kgt_loss += ec_loss
            
            losses['kgtransformer_loss'] = kgt_loss
            total_loss += self.lambda_kgtrans * kgt_loss
        
        losses['total_loss'] = total_loss
        return losses
    
    def update_weights(self, 
                      epoch: int, 
                      total_epochs: int, 
                      phase: str = "main"):
        """动态调整损失权重"""
        if phase == "warmup":
            # 预热阶段：重建损失权重更高
            self.current_lambda_link = self.lambda_link * 0.5
            self.current_lambda_contrastive = self.lambda_contrastive * 0.5
            self.current_lambda_recon = self.lambda_recon * 2.0
            self.current_lambda_kgtrans = self.lambda_kgtrans * 0.5
        elif phase == "refinement":
            # 精炼阶段：对比学习权重更高
            self.current_lambda_link = self.lambda_link * 1.0
            self.current_lambda_contrastive = self.lambda_contrastive * 1.5
            self.current_lambda_recon = self.lambda_recon * 0.5
            self.current_lambda_kgtrans = self.lambda_kgtrans * 1.0
        else:
            # 主训练阶段：均衡权重
            self.current_lambda_link = self.lambda_link
            self.current_lambda_contrastive = self.lambda_contrastive
            self.current_lambda_recon = self.lambda_recon
            self.current_lambda_kgtrans = self.lambda_kgtrans


def compute_metrics(predictions: torch.Tensor, 
                   targets: torch.Tensor,
                   task_type: str = "classification") -> Dict[str, float]:
    """计算评估指标"""
    if task_type == "classification":
        pred_labels = predictions.argmax(dim=-1)
        accuracy = (pred_labels == targets).float().mean().item()
        return {"accuracy": accuracy}
    elif task_type == "ranking":
        # 计算MRR, Hits@k等排序指标
        sorted_indices = torch.argsort(predictions, descending=True)
        ranks = (sorted_indices == targets.unsqueeze(-1)).nonzero(as_tuple=True)[1] + 1
        
        mrr = (1.0 / ranks.float()).mean().item()
        hits_at_1 = (ranks <= 1).float().mean().item()
        hits_at_3 = (ranks <= 3).float().mean().item()
        hits_at_10 = (ranks <= 10).float().mean().item()
        
        return {
            "mrr": mrr,
            "hits@1": hits_at_1,
            "hits@3": hits_at_3,
            "hits@10": hits_at_10
        }
    else:
        raise ValueError(f"未支持的任务类型: {task_type}") 