"""
复数多头注意力机制实现
基于ComplX嵌入空间的创新注意力机制

核心创新:
1. 复数空间中的Query, Key, Value计算
2. 复数共轭转置用于注意力分数计算  
3. 相位感知的注意力权重
4. 几何旋转不变性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class ComplexLinear(nn.Module):
    """
    复数线性层
    支持复数权重和偏置的线性变换
    """
    
    def __init__(self, in_features: int, out_features: int, bias: bool = True):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        
        # 实部和虚部权重矩阵
        self.weight_real = nn.Parameter(torch.randn(out_features, in_features) * 0.02)
        self.weight_imag = nn.Parameter(torch.randn(out_features, in_features) * 0.02)
        
        if bias:
            self.bias_real = nn.Parameter(torch.zeros(out_features))
            self.bias_imag = nn.Parameter(torch.zeros(out_features))
        else:
            self.register_parameter('bias_real', None)
            self.register_parameter('bias_imag', None)
    
    def forward(self, 
                input_real: torch.Tensor, 
                input_imag: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        复数线性变换: (a + bi) * (c + di) = (ac - bd) + (ad + bc)i
        
        Args:
            input_real: 输入实部 [*, in_features]
            input_imag: 输入虚部 [*, in_features]
            
        Returns:
            output_real, output_imag: 输出的实部和虚部
        """
        # 复数矩阵乘法
        # (a + bi) * (c + di) = (ac - bd) + (ad + bc)i
        output_real = torch.matmul(input_real, self.weight_real.t()) - torch.matmul(input_imag, self.weight_imag.t())
        output_imag = torch.matmul(input_real, self.weight_imag.t()) + torch.matmul(input_imag, self.weight_real.t())
        
        # 添加偏置
        if self.bias_real is not None:
            output_real += self.bias_real
            output_imag += self.bias_imag
            
        return output_real, output_imag


class ComplexLayerNorm(nn.Module):
    """
    复数层归一化
    对复数的模长进行归一化，保持相位信息
    """
    
    def __init__(self, normalized_shape: int, eps: float = 1e-5):
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps
        
        # 可学习的缩放参数（实数）
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
    
    def forward(self, 
                input_real: torch.Tensor, 
                input_imag: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        复数层归一化
        
        Args:
            input_real, input_imag: 输入的实部和虚部
            
        Returns:
            归一化后的实部和虚部
        """
        # 计算复数的模长
        magnitude = torch.sqrt(input_real**2 + input_imag**2 + self.eps)
        
        # 计算均值和方差（基于模长）
        mean = magnitude.mean(dim=-1, keepdim=True)
        var = magnitude.var(dim=-1, keepdim=True, unbiased=False)
        
        # 归一化因子
        norm_factor = (magnitude - mean) / torch.sqrt(var + self.eps)
        
        # 应用归一化，保持相位不变
        phase_real = input_real / (magnitude + self.eps)
        phase_imag = input_imag / (magnitude + self.eps)
        
        # 重新缩放
        output_real = self.weight * norm_factor * phase_real + self.bias
        output_imag = self.weight * norm_factor * phase_imag
        
        return output_real, output_imag


class ComplexMultiHeadAttention(nn.Module):
    """
    复数多头注意力机制
    
    核心创新:
    1. Query, Key, Value 在复数空间中计算
    2. 注意力分数使用复数共轭点积
    3. 相位感知的注意力权重
    4. 支持ComplX几何不变性
    """
    
    def __init__(self,
                 d_model: int,
                 num_heads: int,
                 dropout: float = 0.1,
                 use_phase_aware_weights: bool = True):
        super().__init__()
        assert d_model % 2 == 0, "d_model必须是偶数（实部+虚部）"
        assert (d_model // 2) % num_heads == 0, "d_model//2必须能被num_heads整除"
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = (d_model // 2) // num_heads  # 每个头的复数维度
        self.use_phase_aware_weights = use_phase_aware_weights
        
        # Query, Key, Value 的复数线性变换
        self.W_q = ComplexLinear(d_model // 2, d_model // 2)
        self.W_k = ComplexLinear(d_model // 2, d_model // 2)  
        self.W_v = ComplexLinear(d_model // 2, d_model // 2)
        
        # 输出投影
        self.W_o = ComplexLinear(d_model // 2, d_model // 2)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = ComplexLayerNorm(d_model // 2)
        
        # 相位感知权重（可选）
        if use_phase_aware_weights:
            self.phase_weight = nn.Parameter(torch.ones(num_heads))
    
    def forward(self,
                query_real: torch.Tensor,
                query_imag: torch.Tensor,
                key_real: Optional[torch.Tensor] = None,
                key_imag: Optional[torch.Tensor] = None,
                value_real: Optional[torch.Tensor] = None,
                value_imag: Optional[torch.Tensor] = None,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        复数多头注意力前向传播
        
        Args:
            query_real/imag: 查询的实部/虚部 [batch_size, seq_len, d_model//2]
            key_real/imag: 键的实部/虚部 [batch_size, seq_len, d_model//2]
            value_real/imag: 值的实部/虚部 [batch_size, seq_len, d_model//2]
            mask: 注意力掩码 [batch_size, seq_len, seq_len]
            
        Returns:
            output_real, output_imag: 输出的实部和虚部
        """
        # 默认self-attention
        if key_real is None:
            key_real, key_imag = query_real, query_imag
        if value_real is None:
            value_real, value_imag = query_real, query_imag
            
        batch_size, seq_len = query_real.size(0), query_real.size(1)
        
        # 残差连接的输入
        residual_real, residual_imag = query_real, query_imag
        
        # 1. 线性变换 Q, K, V
        Q_real, Q_imag = self.W_q(query_real, query_imag)
        K_real, K_imag = self.W_k(key_real, key_imag)
        V_real, V_imag = self.W_v(value_real, value_imag)
        
        # 2. 重塑为多头格式
        Q_real = Q_real.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        Q_imag = Q_imag.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K_real = K_real.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K_imag = K_imag.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V_real = V_real.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V_imag = V_imag.view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # 3. 计算复数注意力
        attn_output_real, attn_output_imag = self._complex_attention(
            Q_real, Q_imag, K_real, K_imag, V_real, V_imag, mask
        )
        
        # 4. 合并多头
        attn_output_real = attn_output_real.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model // 2
        )
        attn_output_imag = attn_output_imag.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model // 2
        )
        
        # 5. 输出投影
        output_real, output_imag = self.W_o(attn_output_real, attn_output_imag)
        
        # 6. 残差连接 + 层归一化
        output_real += residual_real
        output_imag += residual_imag
        output_real, output_imag = self.layer_norm(output_real, output_imag)
        
        return output_real, output_imag
    
    def _complex_attention(self,
                          Q_real: torch.Tensor, Q_imag: torch.Tensor,
                          K_real: torch.Tensor, K_imag: torch.Tensor,
                          V_real: torch.Tensor, V_imag: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        复数注意力计算核心
        
        使用复数共轭点积: Q * K^H (K的共轭转置)
        """
        # 复数共轭点积: Q * K^H = (Q_real + i*Q_imag) * (K_real - i*K_imag)
        # = (Q_real*K_real + Q_imag*K_imag) + i*(Q_imag*K_real - Q_real*K_imag)
        scores_real = torch.matmul(Q_real, K_real.transpose(-2, -1)) + torch.matmul(Q_imag, K_imag.transpose(-2, -1))
        scores_imag = torch.matmul(Q_imag, K_real.transpose(-2, -1)) - torch.matmul(Q_real, K_imag.transpose(-2, -1))
        
        # 计算复数分数的模长用于softmax
        scores_magnitude = torch.sqrt(scores_real**2 + scores_imag**2)
        
        # 缩放
        scores_magnitude = scores_magnitude / math.sqrt(self.d_k)
        
        # 应用掩码
        if mask is not None:
            scores_magnitude = scores_magnitude.masked_fill(mask == 0, -1e9)
        
        # Softmax注意力权重（基于模长）
        attn_weights = F.softmax(scores_magnitude, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 相位感知权重调整（可选）
        if self.use_phase_aware_weights:
            # 计算相位角度
            phase_angles = torch.atan2(scores_imag, scores_real + 1e-8)
            # 使用可学习的相位权重
            phase_modulation = torch.cos(phase_angles * self.phase_weight.view(1, -1, 1, 1))
            attn_weights = attn_weights * torch.abs(phase_modulation)
        
        # 应用注意力权重到复数值
        context_real = torch.matmul(attn_weights, V_real)
        context_imag = torch.matmul(attn_weights, V_imag)
        
        return context_real, context_imag


class ComplexPositionalEncoding(nn.Module):
    """
    复数位置编码
    使用复数指数函数进行位置编码
    """
    
    def __init__(self, d_model: int, max_seq_length: int = 5000):
        super().__init__()
        assert d_model % 2 == 0
        
        self.d_model = d_model // 2  # 复数维度
        
        # 创建位置编码
        pe_real = torch.zeros(max_seq_length, self.d_model)
        pe_imag = torch.zeros(max_seq_length, self.d_model)
        
        position = torch.arange(0, max_seq_length).unsqueeze(1).float()
        
        # 使用复数指数: e^(i * position * div_term)
        div_term = torch.exp(torch.arange(0, self.d_model, 2).float() * 
                           -(math.log(10000.0) / self.d_model))
        
        # 复数指数分解: e^(i*θ) = cos(θ) + i*sin(θ)
        angles = position * div_term
        
        pe_real[:, 0::2] = torch.cos(angles)
        pe_real[:, 1::2] = torch.cos(angles)
        pe_imag[:, 0::2] = torch.sin(angles) 
        pe_imag[:, 1::2] = torch.sin(angles)
        
        self.register_buffer('pe_real', pe_real)
        self.register_buffer('pe_imag', pe_imag)
    
    def forward(self, 
                input_real: torch.Tensor, 
                input_imag: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        添加复数位置编码
        
        Args:
            input_real/imag: 输入的实部/虚部 [batch_size, seq_len, d_model//2]
            
        Returns:
            添加位置编码后的实部/虚部
        """
        seq_len = input_real.size(1)
        
        pos_real = self.pe_real[:seq_len].unsqueeze(0)
        pos_imag = self.pe_imag[:seq_len].unsqueeze(0)
        
        return input_real + pos_real, input_imag + pos_imag


class ComplexTransformerBlock(nn.Module):
    """
    复数Transformer块
    包含复数多头注意力和前馈网络
    """
    
    def __init__(self,
                 d_model: int,
                 num_heads: int,
                 d_ff: int,
                 dropout: float = 0.1):
        super().__init__()
        
        self.attention = ComplexMultiHeadAttention(d_model, num_heads, dropout)
        
        # 复数前馈网络
        self.ff1 = ComplexLinear(d_model // 2, d_ff)
        self.ff2 = ComplexLinear(d_ff, d_model // 2)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = ComplexLayerNorm(d_model // 2)
    
    def forward(self,
                input_real: torch.Tensor,
                input_imag: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        复数Transformer块前向传播
        """
        # 多头注意力
        attn_real, attn_imag = self.attention(input_real, input_imag, mask=mask)
        
        # 前馈网络
        residual_real, residual_imag = attn_real, attn_imag
        
        # 第一层前馈 + 激活
        ff1_real, ff1_imag = self.ff1(attn_real, attn_imag)
        
        # 复数ReLU激活: 只对实部应用ReLU，虚部保持线性
        ff1_real = F.relu(ff1_real)
        ff1_imag = self.dropout(ff1_imag)
        
        # 第二层前馈
        ff2_real, ff2_imag = self.ff2(ff1_real, ff1_imag)
        
        # 残差连接 + 层归一化
        output_real = residual_real + self.dropout(ff2_real)
        output_imag = residual_imag + self.dropout(ff2_imag)
        output_real, output_imag = self.layer_norm(output_real, output_imag)
        
        return output_real, output_imag


def create_complex_attention_config():
    """
    创建复数注意力机制的默认配置
    """
    return {
        'use_complex_attention': True,
        'complex_attention_heads': 8,
        'complex_attention_layers': 4,
        'complex_d_ff': 512,
        'complex_dropout': 0.1,
        'use_phase_aware_weights': True,
        'max_sequence_length': 126
    }


# ==================== 测试函数 ====================

def test_complex_attention():
    """
    测试复数注意力机制的功能
    """
    print("🧪 测试复数多头注意力机制...")
    
    # 配置
    batch_size = 2
    seq_len = 10
    d_model = 128  # 复数嵌入维度（实际每个复数64维）
    num_heads = 8
    
    # 创建模型
    attention = ComplexMultiHeadAttention(d_model, num_heads)
    pos_encoding = ComplexPositionalEncoding(d_model)
    transformer_block = ComplexTransformerBlock(d_model, num_heads, 256)
    
    # 创建测试数据
    input_real = torch.randn(batch_size, seq_len, d_model // 2)
    input_imag = torch.randn(batch_size, seq_len, d_model // 2)
    
    print(f"输入形状: 实部{input_real.shape}, 虚部{input_imag.shape}")
    
    # 测试位置编码
    pos_real, pos_imag = pos_encoding(input_real, input_imag)
    print(f"位置编码后: 实部{pos_real.shape}, 虚部{pos_imag.shape}")
    
    # 测试注意力机制
    attn_real, attn_imag = attention(pos_real, pos_imag)
    print(f"注意力输出: 实部{attn_real.shape}, 虚部{attn_imag.shape}")
    
    # 测试Transformer块
    output_real, output_imag = transformer_block(input_real, input_imag)
    print(f"Transformer输出: 实部{output_real.shape}, 虚部{output_imag.shape}")
    
    # 验证梯度
    loss = (output_real.sum() + output_imag.sum())
    loss.backward()
    print(f"梯度检查: ✅ 梯度正常传播")
    
    print("🎉 复数注意力机制测试通过！")


if __name__ == "__main__":
    test_complex_attention() 