<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="21">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="h5py" />
            <item index="2" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="3" class="java.lang.String" itemvalue="torch" />
            <item index="4" class="java.lang.String" itemvalue="numpy" />
            <item index="5" class="java.lang.String" itemvalue="torchvision" />
            <item index="6" class="java.lang.String" itemvalue="pytorch-ignite" />
            <item index="7" class="java.lang.String" itemvalue="tensorflow-gpu" />
            <item index="8" class="java.lang.String" itemvalue="matplotlib-scalebar" />
            <item index="9" class="java.lang.String" itemvalue="atomap" />
            <item index="10" class="java.lang.String" itemvalue="pycpd" />
            <item index="11" class="java.lang.String" itemvalue="abtem" />
            <item index="12" class="java.lang.String" itemvalue="hyperspy" />
            <item index="13" class="java.lang.String" itemvalue="pyyaml" />
            <item index="14" class="java.lang.String" itemvalue="tensorflow" />
            <item index="15" class="java.lang.String" itemvalue="transformers" />
            <item index="16" class="java.lang.String" itemvalue="PyYAML" />
            <item index="17" class="java.lang.String" itemvalue="dgl" />
            <item index="18" class="java.lang.String" itemvalue="wandb" />
            <item index="19" class="java.lang.String" itemvalue="dill" />
            <item index="20" class="java.lang.String" itemvalue="plotly" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>