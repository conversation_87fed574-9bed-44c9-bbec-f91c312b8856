# coding=utf-8
"""
Base classes for Knowledge Graph Embedding models

This module defines the abstract base class for all KGE models,
ensuring consistent interfaces and standardized error handling.
"""

import logging
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)


class BaseKGEEmbedding(nn.Module, ABC):
    """
    Knowledge Graph Embedding模型的抽象基类
    
    所有KGE模型都应继承此类并实现抽象方法，确保接口一致性。
    """
    
    def __init__(self, 
                 num_entities: int,
                 num_relations: int, 
                 embedding_dim: int,
                 device: Optional[torch.device] = None):
        """
        初始化KGE嵌入基类
        
        Args:
            num_entities (int): 实体数量
            num_relations (int): 关系数量  
            embedding_dim (int): 嵌入维度
            device (torch.device): 计算设备
        """
        super().__init__()
        
        # 智能设备检测
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        if num_entities <= 0:
            raise ValueError(f"实体数量必须为正数，得到: {num_entities}")
        if num_relations <= 0:
            raise ValueError(f"关系数量必须为正数，得到: {num_relations}")
        if embedding_dim <= 0:
            raise ValueError(f"嵌入维度必须为正数，得到: {embedding_dim}")
            
        self.num_entities = num_entities
        self.num_relations = num_relations
        self.embedding_dim = embedding_dim
        self.device = device
        
        logger.info(f"初始化{self.__class__.__name__}: "
                   f"实体={num_entities}, 关系={num_relations}, 维度={embedding_dim}")
    
    @abstractmethod
    def get_entity_embedding(self, entity_ids: torch.Tensor) -> torch.Tensor:
        """
        获取实体嵌入
        
        Args:
            entity_ids (torch.Tensor): 实体ID张量, shape=[batch_size] 或 [batch_size, num_entities]
            
        Returns:
            torch.Tensor: 实体嵌入张量, shape=[batch_size, embedding_dim] 或 [batch_size, num_entities, embedding_dim]
        """
        pass
    
    @abstractmethod  
    def get_relation_embedding(self, relation_ids: torch.Tensor) -> torch.Tensor:
        """
        获取关系嵌入
        
        Args:
            relation_ids (torch.Tensor): 关系ID张量, shape=[batch_size] 或 [batch_size, num_relations]
            
        Returns:
            torch.Tensor: 关系嵌入张量, shape=[batch_size, embedding_dim] 或 [batch_size, num_relations, embedding_dim]
        """
        pass
    
    @abstractmethod
    def score_triples(self, 
                     head_ids: torch.Tensor,
                     relation_ids: torch.Tensor, 
                     tail_ids: torch.Tensor) -> torch.Tensor:
        """
        计算三元组(head, relation, tail)的评分
        
        Args:
            head_ids (torch.Tensor): 头实体ID, shape=[batch_size]
            relation_ids (torch.Tensor): 关系ID, shape=[batch_size] 
            tail_ids (torch.Tensor): 尾实体ID, shape=[batch_size]
            
        Returns:
            torch.Tensor: 三元组评分, shape=[batch_size]
        """
        pass
    
    def load_pretrained_weights(self, checkpoint_path: str) -> None:
        """
        加载预训练权重
        
        Args:
            checkpoint_path (str): 检查点文件路径
            
        Raises:
            FileNotFoundError: 检查点文件不存在
            RuntimeError: 权重加载失败
        """
        import os
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            self.load_state_dict(checkpoint, strict=False)
            logger.info(f"✅ 成功加载预训练权重: {checkpoint_path}")
        except Exception as e:
            raise RuntimeError(f"加载预训练权重失败: {e}")
    
    def freeze_parameters(self) -> None:
        """冻结所有参数，禁止梯度更新"""
        for param in self.parameters():
            param.requires_grad = False
        logger.info(f"🔒 已冻结{self.__class__.__name__}的所有参数")
    
    def unfreeze_parameters(self) -> None:
        """解冻所有参数，允许梯度更新"""
        for param in self.parameters():
            param.requires_grad = True
        logger.info(f"🔓 已解冻{self.__class__.__name__}的所有参数")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取模型配置信息
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'model_type': self.__class__.__name__,
            'num_entities': self.num_entities,
            'num_relations': self.num_relations,
            'embedding_dim': self.embedding_dim,
            'device': str(self.device)
        }
    
    def validate_inputs(self, entity_ids: torch.Tensor, relation_ids: Optional[torch.Tensor] = None) -> None:
        """
        验证输入张量的有效性
        
        Args:
            entity_ids (torch.Tensor): 实体ID张量
            relation_ids (Optional[torch.Tensor]): 关系ID张量
            
        Raises:
            ValueError: 输入无效
        """
        if not isinstance(entity_ids, torch.Tensor):
            raise ValueError(f"entity_ids必须是torch.Tensor，得到: {type(entity_ids)}")
        
        if entity_ids.max() >= self.num_entities:
            raise ValueError(f"实体ID超出范围: max_id={entity_ids.max()}, num_entities={self.num_entities}")
        
        if entity_ids.min() < 0:
            raise ValueError(f"实体ID不能为负数: min_id={entity_ids.min()}")
        
        if relation_ids is not None:
            if not isinstance(relation_ids, torch.Tensor):
                raise ValueError(f"relation_ids必须是torch.Tensor，得到: {type(relation_ids)}")
            
            if relation_ids.max() >= self.num_relations:
                raise ValueError(f"关系ID超出范围: max_id={relation_ids.max()}, num_relations={self.num_relations}")
            
            if relation_ids.min() < 0:
                raise ValueError(f"关系ID不能为负数: min_id={relation_ids.min()}") 