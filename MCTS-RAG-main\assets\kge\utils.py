# coding=utf-8
"""
Utility functions for Knowledge Graph Embedding integration

This module provides helper functions for distance computation,
embedding loading, and other KGE-related utilities.
"""

import logging
import torch
import torch.nn as nn
import numpy as np
import os
import pickle
from typing import Dict, Tuple, List, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)


def one_hot_distance(distances: torch.Tensor, max_distance: int = 5) -> torch.Tensor:
    """
    将距离值转换为one-hot编码
    
    Args:
        distances (torch.Tensor): 距离值张量, shape=[batch_size] 或任意形状
        max_distance (int): 最大距离值，超出此值的距离归为最后一类
        
    Returns:
        torch.Tensor: one-hot编码的距离特征, shape=[..., max_distance + 1]
    """
    if not isinstance(distances, torch.Tensor):
        distances = torch.tensor(distances, dtype=torch.long)
    
    # 将超出最大距离的值截断到max_distance
    distances_clamped = torch.clamp(distances, 0, max_distance)
    
    # 转换为one-hot编码
    num_classes = max_distance + 1
    one_hot = torch.zeros(*distances.shape, num_classes, dtype=torch.float, device=distances.device)
    one_hot.scatter_(-1, distances_clamped.unsqueeze(-1), 1.0)
    
    return one_hot


def shortest_path_distance(adjacency_matrix: torch.Tensor,
                          source_nodes: torch.Tensor,
                          target_nodes: torch.Tensor,
                          max_distance: int = 5) -> torch.Tensor:
    """
    计算图中节点间的最短路径距离
    
    Args:
        adjacency_matrix (torch.Tensor): 邻接矩阵, shape=[num_nodes, num_nodes]
        source_nodes (torch.Tensor): 源节点ID, shape=[batch_size]
        target_nodes (torch.Tensor): 目标节点ID, shape=[batch_size]
        max_distance (int): 最大搜索距离
        
    Returns:
        torch.Tensor: 最短路径距离, shape=[batch_size]
    """
    device = adjacency_matrix.device
    num_nodes = adjacency_matrix.size(0)
    batch_size = source_nodes.size(0)
    
    # 初始化距离矩阵
    distances = torch.full((num_nodes, num_nodes), float('inf'), device=device)
    distances.fill_diagonal_(0)
    
    # 设置直接连接的距离为1
    distances[adjacency_matrix > 0] = 1
    
    # Floyd-Warshall算法计算所有节点对的最短距离
    for k in range(num_nodes):
        for i in range(num_nodes):
            for j in range(num_nodes):
                if distances[i, k] + distances[k, j] < distances[i, j]:
                    distances[i, j] = distances[i, k] + distances[k, j]
    
    # 提取指定节点对的距离
    result_distances = distances[source_nodes, target_nodes]
    
    # 将无限距离设置为max_distance
    result_distances[result_distances == float('inf')] = max_distance
    
    return result_distances.long()


def compute_distance_features(entity_pairs: List[Tuple[int, int]],
                            adjacency_matrix: torch.Tensor,
                            max_distance: int = 5) -> torch.Tensor:
    """
    为实体对计算距离特征
    
    Args:
        entity_pairs (List[Tuple[int, int]]): 实体对列表 [(entity1, entity2), ...]
        adjacency_matrix (torch.Tensor): 图的邻接矩阵
        max_distance (int): 最大距离值
        
    Returns:
        torch.Tensor: 距离特征矩阵, shape=[num_pairs, max_distance + 1]
    """
    if not entity_pairs:
        return torch.empty(0, max_distance + 1)
    
    source_nodes = torch.tensor([pair[0] for pair in entity_pairs], dtype=torch.long)
    target_nodes = torch.tensor([pair[1] for pair in entity_pairs], dtype=torch.long)
    
    # 计算最短路径距离
    distances = shortest_path_distance(adjacency_matrix, source_nodes, target_nodes, max_distance)
    
    # 转换为one-hot特征
    distance_features = one_hot_distance(distances, max_distance)
    
    return distance_features


def load_pretrained_embeddings(checkpoint_path: str,
                             device: Optional[torch.device] = None) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
    """
    通用预训练嵌入加载器，支持多种格式
    
    Args:
        checkpoint_path (str): 检查点文件路径
        device (torch.device): 目标设备
        
    Returns:
        Tuple[torch.Tensor, torch.Tensor, Dict]: (实体嵌入, 关系嵌入, 配置信息)
        
    Raises:
        FileNotFoundError: 文件不存在
        RuntimeError: 加载失败或格式不支持
    """
    # 智能设备检测
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"嵌入文件不存在: {checkpoint_path}")
    
    file_ext = Path(checkpoint_path).suffix.lower()
    
    try:
        if file_ext == '.pkl':
            # Pickle格式（grail-master常用）
            with open(checkpoint_path, 'rb') as f:
                data = pickle.load(f)
            
            if isinstance(data, dict):
                entity_emb = data.get('entity_embedding', data.get('entity_embeddings'))
                relation_emb = data.get('relation_embedding', data.get('relation_embeddings'))
                config = data.get('config', {})
            else:
                raise RuntimeError("不支持的pickle格式")
                
        elif file_ext in ['.pt', '.pth']:
            # PyTorch格式
            checkpoint = torch.load(checkpoint_path, map_location=device)
            
            if 'entity_embedding' in checkpoint and 'relation_embedding' in checkpoint:
                # 直接格式
                entity_emb = checkpoint['entity_embedding']
                relation_emb = checkpoint['relation_embedding']
                config = checkpoint.get('config', {})
            elif 'model_state_dict' in checkpoint:
                # 模型状态字典格式
                state_dict = checkpoint['model_state_dict']
                entity_emb = state_dict['entity_embeddings.weight']
                relation_emb = state_dict['relation_embeddings.weight']
                config = checkpoint.get('config', {})
            else:
                raise RuntimeError("不支持的PyTorch检查点格式")
                
        elif file_ext == '.npz':
            # NumPy格式
            data = np.load(checkpoint_path)
            entity_emb = torch.from_numpy(data['entity_embeddings']).float()
            relation_emb = torch.from_numpy(data['relation_embeddings']).float()
            config = data.get('config', {}).item() if 'config' in data else {}
            
        else:
            raise RuntimeError(f"不支持的文件格式: {file_ext}")
        
        # 验证加载的嵌入
        if entity_emb is None or relation_emb is None:
            raise RuntimeError("无法提取实体或关系嵌入")
        
        # 转换为张量并移动到指定设备
        if not isinstance(entity_emb, torch.Tensor):
            entity_emb = torch.from_numpy(entity_emb).float()
        if not isinstance(relation_emb, torch.Tensor):
            relation_emb = torch.from_numpy(relation_emb).float()
            
        entity_emb = entity_emb.to(device)
        relation_emb = relation_emb.to(device)
        
        logger.info(f"✅ 成功加载预训练嵌入: {checkpoint_path}")
        logger.info(f"   实体嵌入: {entity_emb.shape}")
        logger.info(f"   关系嵌入: {relation_emb.shape}")
        
        return entity_emb, relation_emb, config
        
    except Exception as e:
        raise RuntimeError(f"加载预训练嵌入失败: {e}")


def validate_embedding_compatibility(entity_emb: torch.Tensor,
                                   relation_emb: torch.Tensor,
                                   expected_num_entities: int,
                                   expected_num_relations: int,
                                   expected_embedding_dim: int) -> None:
    """
    验证嵌入矩阵的兼容性
    
    Args:
        entity_emb (torch.Tensor): 实体嵌入矩阵
        relation_emb (torch.Tensor): 关系嵌入矩阵
        expected_num_entities (int): 期望的实体数量
        expected_num_relations (int): 期望的关系数量
        expected_embedding_dim (int): 期望的嵌入维度
        
    Raises:
        ValueError: 维度不匹配
    """
    # 检查实体嵌入
    if entity_emb.size(0) != expected_num_entities:
        raise ValueError(f"实体数量不匹配: 期望{expected_num_entities}, 得到{entity_emb.size(0)}")
    if entity_emb.size(1) != expected_embedding_dim:
        raise ValueError(f"实体嵌入维度不匹配: 期望{expected_embedding_dim}, 得到{entity_emb.size(1)}")
    
    # 检查关系嵌入
    if relation_emb.size(0) != expected_num_relations:
        raise ValueError(f"关系数量不匹配: 期望{expected_num_relations}, 得到{relation_emb.size(0)}")
    if relation_emb.size(1) != expected_embedding_dim:
        raise ValueError(f"关系嵌入维度不匹配: 期望{expected_embedding_dim}, 得到{relation_emb.size(1)}")


def create_distance_lookup_table(adjacency_matrix: torch.Tensor,
                                max_distance: int = 5) -> torch.Tensor:
    """
    预计算所有节点对的距离查找表
    
    Args:
        adjacency_matrix (torch.Tensor): 邻接矩阵, shape=[num_nodes, num_nodes]
        max_distance (int): 最大距离值
        
    Returns:
        torch.Tensor: 距离查找表, shape=[num_nodes, num_nodes]
    """
    num_nodes = adjacency_matrix.size(0)
    device = adjacency_matrix.device
    
    # 使用BFS计算所有节点对的最短距离（更高效）
    distance_table = torch.full((num_nodes, num_nodes), max_distance, dtype=torch.long, device=device)
    
    # 自环距离为0
    distance_table.fill_diagonal_(0)
    
    # 直接连接距离为1
    distance_table[adjacency_matrix > 0] = 1
    
    # BFS扩展
    for dist in range(2, max_distance + 1):
        # 找到距离为dist-1的节点对
        prev_dist_mask = (distance_table == dist - 1)
        
        for i in range(num_nodes):
            if prev_dist_mask[i].any():
                # 从节点i通过距离为dist-1的路径可达的节点
                reachable = prev_dist_mask[i]
                # 这些节点的邻居就是距离为dist的节点
                neighbors = adjacency_matrix[reachable].sum(dim=0) > 0
                # 更新距离表（只在当前距离更小时更新）
                distance_table[i][neighbors & (distance_table[i] > dist)] = dist
    
    return distance_table


def save_kge_embeddings(entity_embeddings: torch.Tensor,
                       relation_embeddings: torch.Tensor,
                       save_path: str,
                       config: Optional[Dict] = None,
                       format: str = 'pt') -> None:
    """
    保存KGE嵌入到文件
    
    Args:
        entity_embeddings (torch.Tensor): 实体嵌入矩阵
        relation_embeddings (torch.Tensor): 关系嵌入矩阵
        save_path (str): 保存路径
        config (Optional[Dict]): 配置信息
        format (str): 保存格式 ('pt', 'pkl', 'npz')
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    if format == 'pt':
        torch.save({
            'entity_embeddings': entity_embeddings.cpu(),
            'relation_embeddings': relation_embeddings.cpu(),
            'config': config or {}
        }, save_path)
    elif format == 'pkl':
        with open(save_path, 'wb') as f:
            pickle.dump({
                'entity_embedding': entity_embeddings.cpu().numpy(),
                'relation_embedding': relation_embeddings.cpu().numpy(),
                'config': config or {}
            }, f)
    elif format == 'npz':
        np.savez(save_path,
                entity_embeddings=entity_embeddings.cpu().numpy(),
                relation_embeddings=relation_embeddings.cpu().numpy(),
                config=config or {})
    else:
        raise ValueError(f"不支持的保存格式: {format}")
    
    logger.info(f"✅ KGE嵌入已保存至: {save_path}") 


def load_kg_triples(data_folder: str) -> Tuple[List[Tuple[int, int, int]], Dict[str, int], Dict[str, int]]:
    """
    从KGQA数据文件夹加载知识图谱三元组
    参照grail-master的数据加载方式
    
    Args:
        data_folder (str): 数据文件夹路径
        
    Returns:
        Tuple[List[Tuple[int, int, int]], Dict[str, int], Dict[str, int]]: 
            (三元组列表, 实体到ID映射, 关系到ID映射)
    """
    import json
    import os
    
    # 加载实体和关系映射
    entity2id = {}
    relation2id = {}
    
    entity_file = os.path.join(data_folder, 'entities.txt')
    relation_file = os.path.join(data_folder, 'relations.txt')
    
    # 读取实体映射（webqsp格式：每行一个实体，索引从0开始）
    if os.path.exists(entity_file):
        with open(entity_file, 'r', encoding='utf-8') as f:
            for idx, line in enumerate(f):
                entity = line.strip()
                if entity:  # 跳过空行
                    entity2id[entity] = idx
        logger.info(f"加载了 {len(entity2id)} 个实体")
    else:
        logger.warning(f"实体文件不存在: {entity_file}")
        
    # 读取关系映射（webqsp格式：每行一个关系，索引从0开始）
    if os.path.exists(relation_file):
        with open(relation_file, 'r', encoding='utf-8') as f:
            for idx, line in enumerate(f):
                relation = line.strip()
                if relation:  # 跳过空行
                    relation2id[relation] = idx
        logger.info(f"加载了 {len(relation2id)} 个关系")
    else:
        logger.warning(f"关系文件不存在: {relation_file}")
    
    # 从KGQA数据中提取三元组
    triples = []
    
    # 方法1: 从train.json中的子图提取
    train_file = os.path.join(data_folder, 'train.json')
    if os.path.exists(train_file):
        with open(train_file, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    data = json.loads(line)
                    if 'subgraph' in data and 'tuples' in data['subgraph']:
                        for triple in data['subgraph']['tuples']:
                            if len(triple) >= 3:
                                head_id = int(triple[0])
                                rel_id = int(triple[1])
                                tail_id = int(triple[2])
                                triples.append((head_id, rel_id, tail_id))
                except json.JSONDecodeError:
                    continue
    
    # 方法2: 从kg.txt文件提取（如果存在）
    kg_file = os.path.join(data_folder, 'kg.txt')
    if os.path.exists(kg_file):
        with open(kg_file, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    try:
                        head_id = int(parts[0])
                        rel_id = int(parts[1])
                        tail_id = int(parts[2])
                        triples.append((head_id, rel_id, tail_id))
                    except ValueError:
                        continue
    
    # 如果没有找到三元组，生成一些基于映射的示例三元组
    if not triples and entity2id and relation2id:
        logger.warning("未找到KG三元组，基于实体关系映射生成示例三元组")
        import random
        num_entities = len(entity2id)
        num_relations = len(relation2id)
        
        # 生成随机三元组（确保不超过实体和关系的范围）
        for _ in range(min(10000, num_entities * 2)):
            head = random.randint(0, num_entities - 1)
            rel = random.randint(0, num_relations - 1)
            tail = random.randint(0, num_entities - 1)
            if head != tail:  # 避免自环
                triples.append((head, rel, tail))
    
    # 去重
    triples = list(set(triples))
    logger.info(f"加载了 {len(triples)} 个唯一三元组")
    
    return triples, entity2id, relation2id


def create_negative_samples(positive_triples: List[Tuple[int, int, int]], 
                          num_entities: int,
                          num_negative_per_positive: int = 1,
                          mode: str = 'both') -> List[Tuple[int, int, int]]:
    """
    为正样本三元组创建负样本
    参照grail-master的负采样策略
    
    Args:
        positive_triples: 正样本三元组列表
        num_entities: 实体总数
        num_negative_per_positive: 每个正样本生成的负样本数
        mode: 负采样模式 ('head', 'tail', 'both')
        
    Returns:
        负样本三元组列表
    """
    import random
    
    negative_triples = []
    positive_set = set(positive_triples)
    
    for head, rel, tail in positive_triples:
        neg_count = 0
        attempts = 0
        max_attempts = num_negative_per_positive * 10  # 防止无限循环
        
        while neg_count < num_negative_per_positive and attempts < max_attempts:
            attempts += 1
            
            # 根据模式选择替换策略
            if mode == 'head':
                # 只替换头实体
                neg_head = random.randint(0, num_entities - 1)
                neg_triple = (neg_head, rel, tail)
            elif mode == 'tail':
                # 只替换尾实体
                neg_tail = random.randint(0, num_entities - 1)
                neg_triple = (head, rel, neg_tail)
            else:  # mode == 'both'
                # 随机选择替换头或尾实体
                if random.random() < 0.5:
                    neg_head = random.randint(0, num_entities - 1)
                    neg_triple = (neg_head, rel, tail)
                else:
                    neg_tail = random.randint(0, num_entities - 1)
                    neg_triple = (head, rel, neg_tail)
            
            # 确保负样本不在正样本集合中
            if neg_triple not in positive_set:
                negative_triples.append(neg_triple)
                neg_count += 1
    
    logger.info(f"生成了 {len(negative_triples)} 个负样本")
    return negative_triples


def count_triple_frequency(triples: List[Tuple[int, int, int]], start: int = 4) -> Dict[Tuple[int, int], int]:
    """
    计算三元组中(head, relation)和(tail, -relation-1)的频率
    用于子采样权重计算，参照grail-master实现
    
    Args:
        triples: 三元组列表
        start: 初始计数值
        
    Returns:
        频率字典
    """
    count = {}
    for head, relation, tail in triples:
        # 正向关系频率
        if (head, relation) not in count:
            count[(head, relation)] = start
        else:
            count[(head, relation)] += 1
        
        # 反向关系频率（grail-master的做法）
        if (tail, -relation-1) not in count:
            count[(tail, -relation-1)] = start
        else:
            count[(tail, -relation-1)] += 1
    
    return count


def get_true_head_and_tail(triples: List[Tuple[int, int, int]]) -> Tuple[Dict, Dict]:
    """
    构建真实头实体和尾实体的字典
    用于负采样时过滤真实三元组，参照grail-master实现
    
    Args:
        triples: 三元组列表
        
    Returns:
        (true_head字典, true_tail字典)
    """
    true_head = {}
    true_tail = {}
    
    for head, relation, tail in triples:
        # 记录每个(relation, tail)对应的所有可能头实体
        if (relation, tail) not in true_head:
            true_head[(relation, tail)] = []
        true_head[(relation, tail)].append(head)
        
        # 记录每个(head, relation)对应的所有可能尾实体
        if (head, relation) not in true_tail:
            true_tail[(head, relation)] = []
        true_tail[(head, relation)].append(tail)
    
    # 转换为numpy数组以提高效率
    for key in true_head:
        true_head[key] = np.array(true_head[key])
    for key in true_tail:
        true_tail[key] = np.array(true_tail[key])
    
    return true_head, true_tail


def create_kge_train_dataloader(triples: List[Tuple[int, int, int]],
                               num_entities: int,
                               num_relations: int,
                               batch_size: int = 1024,
                               negative_sample_size: int = 128,
                               mode: str = 'head-batch',
                               shuffle: bool = True) -> torch.utils.data.DataLoader:
    """
    创建KGE训练用的DataLoader
    参照grail-master的TrainDataset实现
    
    Args:
        triples: 训练三元组
        num_entities: 实体数量
        num_relations: 关系数量
        batch_size: 批次大小
        negative_sample_size: 负样本数量
        mode: 训练模式 ('head-batch' 或 'tail-batch')
        shuffle: 是否打乱数据
        
    Returns:
        PyTorch DataLoader
    """
    from torch.utils.data import Dataset, DataLoader
    
    class KGETrainDataset(Dataset):
        def __init__(self, triples, nentity, nrelation, negative_sample_size, mode):
            self.triples = triples
            self.nentity = nentity
            self.nrelation = nrelation
            self.negative_sample_size = negative_sample_size
            self.mode = mode
            self.count = count_triple_frequency(triples)
            self.true_head, self.true_tail = get_true_head_and_tail(triples)
        
        def __len__(self):
            return len(self.triples)
        
        def __getitem__(self, idx):
            positive_sample = self.triples[idx]
            head, relation, tail = positive_sample
            
            # 计算子采样权重
            subsampling_weight = self.count.get((head, relation), 1) + self.count.get((tail, -relation-1), 1)
            subsampling_weight = torch.sqrt(1 / torch.tensor([float(subsampling_weight)]))
            
            # 生成负样本
            negative_sample_list = []
            negative_sample_size = 0
            
            while negative_sample_size < self.negative_sample_size:
                negative_sample = np.random.randint(self.nentity, size=self.negative_sample_size * 2)
                
                if self.mode == 'head-batch':
                    # 过滤真实头实体
                    true_entities = self.true_head.get((relation, tail), np.array([]))
                    mask = np.logical_not(np.isin(negative_sample, true_entities))
                elif self.mode == 'tail-batch':
                    # 过滤真实尾实体
                    true_entities = self.true_tail.get((head, relation), np.array([]))
                    mask = np.logical_not(np.isin(negative_sample, true_entities))
                else:
                    raise ValueError(f'训练模式 {self.mode} 不支持')
                
                negative_sample = negative_sample[mask]
                negative_sample_list.append(negative_sample)
                negative_sample_size += negative_sample.size
            
            negative_sample = np.concatenate(negative_sample_list)[:self.negative_sample_size]
            negative_sample = torch.from_numpy(negative_sample)
            positive_sample = torch.LongTensor(positive_sample)
            
            return positive_sample, negative_sample, subsampling_weight, self.mode
    
    dataset = KGETrainDataset(triples, num_entities, num_relations, negative_sample_size, mode)
    
    def collate_fn(data):
        positive_sample = torch.stack([_[0] for _ in data], dim=0)
        negative_sample = torch.stack([_[1] for _ in data], dim=0)
        subsample_weight = torch.cat([_[2] for _ in data], dim=0)
        mode = data[0][3]
        return positive_sample, negative_sample, subsample_weight, mode
    
    return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle, collate_fn=collate_fn) 