'''
/home/<USER>/download/GNN-RAG-main/gnn/models/ReaRev/rearev.py
文件功能：定义了 ReaRev 模型类，继承自 BaseModel，用于知识图谱问答任务。
主要方法：
__init__：初始化模型参数和组件。
layers：定义模型层，包括实体和关系的线性层、自注意力层等。
get_ent_init：获取初始实体嵌入。
get_rel_feature：将关系标记编码为向量。
private_module_def：定义私有模块，如指令编码器和GNN层。
init_reason：初始化推理过程。
calc_loss_label：计算损失。
forward：前向传播，生成指令并执行GNN推理。
'''


'''
torch 和 numpy：深度学习和科学计算库。
Variable：PyTorch 中的变量类（旧版本）。
BaseModel：基础模型类。
ReasonGNNLayer：图神经网络推理层。
LSTMInstruction 和 BERTInstruction：问题编码器。
TypeLayer：类型层。
AttnEncoder、Fusion 和 QueryReform：查询更新模块。
'''

import torch
import numpy as np
from torch.autograd import Variable
import torch.nn.functional as F
import torch.nn as nn

from models.base_model import BaseModel
from modules.kg_reasoning.reasongnn import ReasonGNNLayer
from modules.question_encoding.lstm_encoder import LSTMInstruction
from modules.question_encoding.bert_encoder import BERTInstruction
from modules.layer_init import TypeLayer
from modules.query_update import AttnEncoder, Fusion, QueryReform

'''
常量定义：
VERY_SMALL_NUMBER：极小值，用于数值稳定性。
VERY_NEG_NUMBER：极大负数，用于初始化。
'''

VERY_SMALL_NUMBER = 1e-10
VERY_NEG_NUMBER = -100000000000

# 定义ReaRev模型类，继承自BaseModel
class ReaRev(BaseModel):
    '''
    模型初始化：
    继承自 BaseModel。
    初始化超参数和模块。
    定义线性层、融合层和查询重构模块。
    '''
    def __init__(self, args, num_entity, num_relation, num_word):
        """
        初始化ReaRev模型。
        """
        args['use_inverse_relation'] = False
        super(ReaRev, self).__init__(args, num_entity, num_relation, num_word)
        #self.embedding_def()
        #self.share_module_def()
        self.norm_rel = args['norm_rel']
        
        # KGE集成相关参数
        self.use_complex_kge = args.get('use_complex_kge', False)
        if self.use_complex_kge:
            self.kge_fusion_method = args.get('kge_fusion_method', 'concat')
            self.complex_hidden_dim = args.get('complex_hidden_dim', 50)
            self.max_distance = args.get('max_distance', 5)
            
            # 根据融合方式调整实体维度
            if self.kge_fusion_method == 'concat':
                kge_dim = self.complex_hidden_dim * 2  # ComplX使用复数嵌入
                distance_dim = self.max_distance + 1
                original_entity_dim = self.entity_dim
                self.entity_dim = self.entity_dim + kge_dim + distance_dim
                print(f"🔧 ReaRev KGE集成: concat模式，实体维度从 {original_entity_dim} 调整为 {self.entity_dim}")
        
        self.layers(args)  # 调用layers方法初始化模型层

        self.loss_type =  args['loss_type']
        self.num_iter = args['num_iter']
        self.num_ins = args['num_ins']
        self.num_gnn = args['num_gnn']
        self.alg = args['alg']
        assert self.alg == 'bfs'
        self.lm = args['lm']
        
        # AMHR相关参数
        self.enable_amhr = args.get('enable_amhr', False)
        self.amhr_max_depth = args.get('amhr_max_depth', 5)
        self.amhr_max_macro_rounds = args.get('amhr_max_macro_rounds', 3)
        self.amhr_dropout = args.get('amhr_dropout', 0.1)
        
        self.private_module_def(args, num_entity, num_relation)  # 定义私有模块

        self.to(self.device)  # 将模型移动到指定设备
        self.lin = nn.Linear(3*self.entity_dim, self.entity_dim)  # 定义线性层

        self.fusion = Fusion(self.entity_dim)  # 定义融合层
        self.reforms = []
        for i in range(self.num_ins):
            # 使用AMHR增强的QueryReform，传递优化参数
            reform_module = QueryReform(
                self.entity_dim, 
                max_depth=self.amhr_max_depth,
                max_macro_rounds=self.amhr_max_macro_rounds,
                enable_amhr=self.enable_amhr,
                dropout_rate=self.amhr_dropout,
                # 效率优化参数
                amhr_macro_trigger_threshold=args.get('amhr_macro_trigger_threshold', 0.1),
                amhr_micro_min_strength=args.get('amhr_micro_min_strength', 0.05),
                amhr_adaptive_scaling=args.get('amhr_adaptive_scaling', True),
                amhr_efficiency_mode=args.get('amhr_efficiency_mode', True)
            )
            self.add_module('reform' + str(i), reform_module)  # 添加查询重构模块



            '''
            定义模型层：
            初始化实体线性层、关系线性层、Dropout 层。
            如果启用类型编码，则初始化类型层。
            定义注意力编码器和损失函数。
            '''

    def layers(self, args):
        # 初始化实体嵌入
        word_dim = self.word_dim
        kg_dim = self.kg_dim
        entity_dim = self.entity_dim

        self.linear_dropout = args['linear_dropout']
        
        self.entity_linear = nn.Linear(in_features=self.ent_dim, out_features=entity_dim)  # 实体线性层
        self.relation_linear = nn.Linear(in_features=self.rel_dim, out_features=entity_dim)  # 关系线性层

        self.linear_drop = nn.Dropout(p=self.linear_dropout)  # 线性层dropout

        if self.encode_type:
            self.type_layer = TypeLayer(in_features=entity_dim, out_features=entity_dim,
                                        linear_drop=self.linear_drop, device=self.device, norm_rel=self.norm_rel)  # 类型层

        self.self_att_r = AttnEncoder(self.entity_dim)  # 自注意力层
        self.kld_loss = nn.KLDivLoss(reduction='none')  # KL散度损失
        self.bce_loss_logits = nn.BCEWithLogitsLoss(reduction='none')  # 二元交叉熵损失
        self.mse_loss = torch.nn.MSELoss()  # 均方误差损失


        '''
        获取初始实体嵌入：
        如果启用类型编码，则通过类型层获取嵌入。
        否则直接通过实体嵌入层获取。
        '''
    def get_ent_init(self, local_entity, kb_adj_mat, rel_features, distance_features=None):
        # 获取初始实体嵌入
        if self.encode_type:
            local_entity_emb = self.type_layer(local_entity=local_entity,
                                               edge_list=kb_adj_mat,
                                               rel_features=rel_features)
        else:
            if self.use_complex_kge:
                # 使用增强的实体嵌入（集成KGE和距离特征）
                local_entity_emb = self.get_enhanced_entity_embeddings(local_entity, distance_features)
                # 不需要额外的线性变换，因为维度已经在get_enhanced_entity_embeddings中处理
        else:
            local_entity_emb = self.entity_embedding(local_entity)  # batch_size, max_local_entity, word_dim
            local_entity_emb = self.entity_linear(local_entity_emb)
        
        return local_entity_emb
    

    '''
    获取关系特征：
    如果没有关系文本，则直接通过关系嵌入层获取。
    否则通过问题编码器和注意力机制获取。
    '''
    def get_rel_feature(self):
        """
        将关系标记编码为向量。
        """
        if self.rel_texts is None:
            rel_features = self.relation_embedding.weight
            rel_features_inv = self.relation_embedding_inv.weight
            rel_features = self.relation_linear(rel_features)
            rel_features_inv = self.relation_linear(rel_features_inv)
        else:
            
            rel_features = self.instruction.question_emb(self.rel_features)
            rel_features_inv = self.instruction.question_emb(self.rel_features_inv)
            
            rel_features = self.self_att_r(rel_features,  (self.rel_texts != self.instruction.pad_val).float())
            rel_features_inv = self.self_att_r(rel_features_inv,  (self.rel_texts != self.instruction.pad_val).float())
            if self.lm == 'lstm':
                rel_features = self.self_att_r(rel_features, (self.rel_texts != self.num_relation+1).float())
                rel_features_inv = self.self_att_r(rel_features_inv, (self.rel_texts_inv != self.num_relation+1).float())

        return rel_features, rel_features_inv

    '''
    定义私有模块：
    初始化 GNN 推理层。
    根据语言模型类型选择不同的指令编码器。
    '''

    def private_module_def(self, args, num_entity, num_relation):
        """
        构建模块：LM编码器，GNN等。
        """
        word_dim = self.word_dim
        kg_dim = self.kg_dim
        entity_dim = self.entity_dim
        self.reasoning = ReasonGNNLayer(args, num_entity, num_relation, entity_dim, self.alg)  # 初始化GNN层
        if args['lm'] == 'lstm':
            self.instruction = LSTMInstruction(args, self.word_embedding, self.num_word)  # LSTM指令编码器
            self.relation_linear = nn.Linear(in_features=entity_dim, out_features=entity_dim)  # 关系线性层
        else:
            self.instruction = BERTInstruction(args, self.word_embedding, self.num_word, args['lm'])  # BERT指令编码器

    '''
    初始化推理：
    获取指令列表和注意力权重。
    获取关系特征和初始实体嵌入。
    初始化 GNN 推理。
    '''
    def init_reason(self, curr_dist, local_entity, kb_adj_mat, q_input, query_entities, distance_features=None):
        """
        初始化推理。
        """
        self.local_entity = local_entity
        self.instruction_list, self.attn_list = self.instruction(q_input)  # 获取指令列表和注意力权重
        rel_features, rel_features_inv  = self.get_rel_feature()  # 获取关系特征
        self.local_entity_emb = self.get_ent_init(local_entity, kb_adj_mat, rel_features, distance_features)  # 获取初始实体嵌入
        self.init_entity_emb = self.local_entity_emb
        self.curr_dist = curr_dist
        self.dist_history = []
        self.action_probs = []
        self.seed_entities = curr_dist
        
        self.reasoning.init_reason( 
                                   local_entity=local_entity,
                                   kb_adj_mat=kb_adj_mat,
                                   local_entity_emb=self.local_entity_emb,
                                   rel_features=rel_features,
                                   rel_features_inv=rel_features_inv,
                                   query_entities=query_entities)  # 初始化GNN推理

    def calc_loss_label(self, curr_dist, teacher_dist, label_valid):
        tp_loss = self.get_loss(pred_dist=curr_dist, answer_dist=teacher_dist, reduction='none')  # 计算损失
        tp_loss = tp_loss * label_valid
        cur_loss = torch.sum(tp_loss) / curr_dist.size(0)  # 计算平均损失
        return cur_loss

    def _compute_path_statistics(self, global_rep, curr_dist):
        """
        计算路径统计信息，用于AMHR宏观反思
        
        Args:
            global_rep: 全局表示 (B, entity_dim)
            curr_dist: 当前分布 (B, num_entities)
            
        Returns:
            path_stats: 路径统计向量 (B, entity_dim)
        """
        batch_size = global_rep.size(0)
        
        # 计算分布的多样性（熵）
        dist_entropy = -torch.sum(curr_dist * torch.log(curr_dist + 1e-8), dim=1)  # (B,)
        
        # 计算分布的集中度（最大值）
        dist_max = torch.max(curr_dist, dim=1)[0]  # (B,)
        
        # 计算非零元素的数量（路径覆盖率）
        dist_coverage = torch.sum((curr_dist > 1e-6).float(), dim=1)  # (B,)
        
        # 归一化统计值到[0,1]范围
        dist_entropy = torch.sigmoid(dist_entropy.unsqueeze(1))  # (B, 1)
        dist_max = dist_max.unsqueeze(1)  # (B, 1)
        dist_coverage = torch.sigmoid(dist_coverage.unsqueeze(1) / 100.0)  # (B, 1)
        
        # 将统计信息编码为与entity_dim相同维度的向量
        stats_raw = torch.cat([dist_entropy, dist_max, dist_coverage], dim=1)  # (B, 3)
        
        # 使用线性层将统计信息映射到entity_dim维度
        if not hasattr(self, 'path_stats_encoder'):
            self.path_stats_encoder = nn.Linear(3, self.entity_dim).to(self.device)
        
        path_stats = self.path_stats_encoder(stats_raw)  # (B, entity_dim)
        
        return path_stats

    def get_amhr_summary(self):
        """
        获取AMHR反思机制的总结信息
        
        Returns:
            dict: 包含所有QueryReform模块的AMHR统计信息
        """
        if not self.enable_amhr:
            return {'amhr_enabled': False}
        
        summary = {'amhr_enabled': True, 'reforms': {}}
        
        for i in range(self.num_ins):
            reform = getattr(self, 'reform' + str(i))
            reform_summary = reform.get_amhr_summary()
            summary['reforms'][f'reform_{i}'] = reform_summary
        
        # 计算全局统计
        total_micro_count = sum([summary['reforms'][f'reform_{i}']['micro_count'] for i in range(self.num_ins)])
        total_macro_count = sum([summary['reforms'][f'reform_{i}']['macro_count'] for i in range(self.num_ins)])
        avg_ratio = sum([summary['reforms'][f'reform_{i}']['ratio'] for i in range(self.num_ins)]) / self.num_ins
        
        summary['global_stats'] = {
            'total_micro_count': total_micro_count,
            'total_macro_count': total_macro_count,
            'avg_ratio': avg_ratio,
            'overall_quality': '理想' if 0.3 <= avg_ratio <= 1.0 else '需调整'
        }
        
        return summary

    def reset_amhr_stats(self):
        """重置所有QueryReform模块的AMHR统计信息"""
        if self.enable_amhr:
            for i in range(self.num_ins):
                reform = getattr(self, 'reform' + str(i))
                reform.reset_amhr_stats()

    def _get_distance_features(self, batch_ids, max_local_entities):
        """
        获取当前批次的距离特征
        
        Args:
            batch_ids: 批次ID数组
            max_local_entities: 最大局部实体数
            
        Returns:
            torch.Tensor: 距离特征张量 shape=[batch_size, max_local_entities, max_distance+1]
        """
        if not self.use_complex_kge:
            return None
            
        # 这里应该从训练数据中获取预计算的距离特征
        # 由于数据加载器的结构比较复杂，这里提供一个占位实现
        batch_size = len(batch_ids)
        distance_features = torch.zeros(
            batch_size, max_local_entities, self.max_distance + 1,
            device=self.device, dtype=torch.float
        )
        
        # TODO: 实际实现需要从数据加载器中获取预计算的距离特征
        # 目前使用零填充作为占位
        return distance_features

    '''
    前向传播：
    处理输入数据并初始化推理。
    执行多步 GNN 推理和查询更新。
    计算损失和预测结果。
    '''
    def forward(self, batch, training=False):
        """
        前向函数：生成指令并执行GNN推理。
        """

        local_entity, query_entities, kb_adj_mat, query_text, seed_dist, true_batch_id, answer_dist = batch
        local_entity = torch.from_numpy(local_entity).type('torch.LongTensor').to(self.device)  # 转换为LongTensor
        query_entities = torch.from_numpy(query_entities).type('torch.FloatTensor').to(self.device)  # 转换为FloatTensor
        answer_dist = torch.from_numpy(answer_dist).type('torch.FloatTensor').to(self.device)  # 转换为FloatTensor
        seed_dist = torch.from_numpy(seed_dist).type('torch.FloatTensor').to(self.device)  # 转换为FloatTensor
        current_dist = Variable(seed_dist, requires_grad=True)  # 设置变量

        q_input= torch.from_numpy(query_text).type('torch.LongTensor').to(self.device)  # 转换为LongTensor
        if self.lm != 'lstm':
            pad_val = self.instruction.pad_val
            query_mask = (q_input != pad_val).float()  # 创建查询掩码
            
        else:
            query_mask = (q_input != self.num_word).float()

        
        """
        指令生成
        """
        # KGE集成：准备距离特征
        distance_features = None
        if self.use_complex_kge and hasattr(self, '_get_distance_features'):
            # 获取当前批次的距离特征（如果有的话）
            distance_features = self._get_distance_features(true_batch_id, local_entity.size(1))
            
        self.init_reason(curr_dist=current_dist, local_entity=local_entity,
                         kb_adj_mat=kb_adj_mat, q_input=q_input, query_entities=query_entities,
                         distance_features=distance_features)  # 初始化推理
        self.instruction.init_reason(q_input)
        for i in range(self.num_ins):
            relational_ins, attn_weight = self.instruction.get_instruction(self.instruction.relational_ins, step=i) 
            self.instruction.instructions.append(relational_ins.unsqueeze(1))  # 添加指令
            self.instruction.relational_ins = relational_ins
        self.dist_history.append(self.curr_dist)

        """
        BFS + GNN推理 + AMHR反思机制
        """

        for t in range(self.num_iter):
            relation_ins = torch.cat(self.instruction.instructions, dim=1)  # 拼接指令
            self.curr_dist = current_dist            
            for j in range(self.num_gnn):
                self.curr_dist, global_rep = self.reasoning(self.curr_dist, relation_ins, step=j)  # 执行GNN推理
            self.dist_history.append(self.curr_dist)
            qs = []

            """
            指令更新 + AMHR微观反思
            """
            for j in range(self.num_ins):
                reform = getattr(self, 'reform' + str(j))
                if self.enable_amhr:
                    # AMHR微观反思：基于当前迭代深度进行局部调整
                    q = reform(
                        self.instruction.instructions[j].squeeze(1), 
                        global_rep, 
                        query_entities, 
                        local_entity,
                        depth=t * self.num_gnn + j,  # 当前推理深度
                        is_macro=False  # 微观反思
                    )
                else:
                    # 原有的查询更新逻辑
                    q = reform(self.instruction.instructions[j].squeeze(1), global_rep, query_entities, local_entity)
                qs.append(q.unsqueeze(1))
                self.instruction.instructions[j] = q.unsqueeze(1)
            
            # AMHR宏观反思：在完整迭代轮次后进行全局重构
            if self.enable_amhr and t == self.num_iter - 1:  # 在最后一轮迭代后执行宏观反思
                # 计算路径统计信息
                path_stats = self._compute_path_statistics(global_rep, self.curr_dist)
                
                # 对每个指令进行宏观反思
                for macro_round in range(self.amhr_max_macro_rounds):
                    for j in range(self.num_ins):
                        reform = getattr(self, 'reform' + str(j))
                        q = reform(
                            self.instruction.instructions[j].squeeze(1),
                            global_rep,
                            query_entities,
                            local_entity,
                            depth=0,  # 宏观反思不受深度限制
                            is_macro=True,  # 宏观反思
                            path_stats=path_stats
                        )
                self.instruction.instructions[j] = q.unsqueeze(1)
        
        
        """
        回答预测
        """
        pred_dist = self.dist_history[-1]
        answer_number = torch.sum(answer_dist, dim=1, keepdim=True)
        case_valid = (answer_number > 0).float()  # 创建有效案例掩码
        loss = self.calc_loss_label(curr_dist=pred_dist, teacher_dist=answer_dist, label_valid=case_valid)  # 计算损失

        pred_dist = self.dist_history[-1]
        pred = torch.max(pred_dist, dim=1)[1]  # 获取预测结果
        if training:
            h1, f1 = self.get_eval_metric(pred_dist, answer_dist)  # 获取评估指标
            tp_list = [h1.tolist(), f1.tolist()]
        else:
            tp_list = None
        return loss, pred, pred_dist, tp_list