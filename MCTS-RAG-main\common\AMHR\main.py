'''
argparse：用于解析命令行参数。
create_logger：从 utils 模块中导入，用于创建日志记录器。
torch 和 numpy：深度学习和科学计算库。
os 和 time：操作系统接口和时间操作。
Trainer_KBQA：从 train_model 模块中导入，用于训练模型。
add_parse_args：从 parsing 模块中导入，用于添加命令行参数。
'''

import argparse
from utils import create_logger
import torch
import numpy as np
import os
import time
#from Models.ReaRev.rearev import 
from train_model import Trainer_KBQA
from parsing import add_parse_args

'''
命令行参数解析：
创建 ArgumentParser 对象。
调用 add_parse_args 添加自定义参数。
解析命令行参数并存储在 args 中。
检查是否支持 CUDA 并设置 use_cuda。
'''

parser = argparse.ArgumentParser()
add_parse_args(parser)

# 添加TensorBoard相关参数
parser.add_argument('--use_tensorboard', default=True, type=bool, help='是否使用TensorBoard记录训练过程')
parser.add_argument('--tensorboard_dir', default='runs', type=str, help='TensorBoard日志保存目录')


args = parser.parse_args()
args.use_cuda = torch.cuda.is_available()


'''
随机种子设置：
设置 NumPy 和 PyTorch 的随机种子以保证实验可复现。
如果未指定实验名称，则生成一个基于时间戳的默认名称。
'''

np.random.seed(args.seed)
torch.manual_seed(args.seed)
if args.experiment_name == None:
    timestamp = str(int(time.time()))
    args.experiment_name = "{}-{}-{}".format(
        args.dataset,
        args.model_name,
        timestamp,
    )

'''
主函数：
检查检查点目录是否存在，若不存在则创建。
创建日志记录器。
初始化 Trainer_KBQA 训练器。

训练或评估：
如果不是评估模式，则调用 trainer.train 进行训练。
如果是评估模式，则加载预训练模型并调用 trainer.evaluate_single 进行单次评估。
'''
def main():
    if not os.path.exists(args.checkpoint_dir):
        os.mkdir(args.checkpoint_dir)
    logger = create_logger(args)
    trainer = Trainer_KBQA(args=vars(args), model_name=args.model_name, logger=logger)
    if not args.is_eval:
        trainer.train(0, args.num_epoch - 1)
    else:
        assert args.load_experiment is not None
        if args.load_experiment is not None:
            ckpt_path = os.path.join(args.checkpoint_dir, args.load_experiment)
            print("Loading pre trained model from {}".format(ckpt_path))
        else:
            ckpt_path = None
        trainer.evaluate_single(ckpt_path)


if __name__ == '__main__':
    main()
