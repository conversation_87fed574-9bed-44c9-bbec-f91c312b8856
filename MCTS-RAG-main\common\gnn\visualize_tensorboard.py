import os
import argparse
import pandas as pd
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
import seaborn as sns


def extract_tensorboard_data(log_dir):
    """从TensorBoard日志中提取数据"""
    event_acc = EventAccumulator(log_dir)
    event_acc.Reload()

    data = {}
    for tag in event_acc.Tags()['scalars']:
        data[tag] = {
            'value': [e.value for e in event_acc.Scalars(tag)],
            'step': [e.step for e in event_acc.Scalars(tag)],
            'wall_time': [e.wall_time for e in event_acc.Scalars(tag)]
        }
    return data, event_acc.Tags()['scalars']


def plot_curves(data, tags, output_dir):
    """绘制曲线并保存到文件"""
    os.makedirs(output_dir, exist_ok=True)

    # 设置全局样式
    sns.set_style("whitegrid")
    plt.rcParams.update({
        'figure.figsize': (12, 8),
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12
    })

    # 按指标类型分组（Loss, H1, F1）
    metric_groups = {
        'Loss': [tag for tag in tags if 'Loss/' in tag],
        'H1': [tag for tag in tags if 'H1/' in tag],
        'F1': [tag for tag in tags if 'F1/' in tag],
        'Extras': [tag for tag in tags if 'Extras/' in tag]
    }

    # 为每个指标组绘制子图
    for group, group_tags in metric_groups.items():
        if not group_tags:
            continue  # 跳过空组

        # 创建子图
        phases = ['train', 'eval', 'test']
        fig, axes = plt.subplots(len(phases), 1, figsize=(12, 4 * len(phases)))
        if len(phases) == 1:
            axes = [axes]

        for ax, phase in zip(axes, phases):
            phase_tags = [t for t in group_tags if f'/{phase}' in t]
            if not phase_tags:
                continue

            # 提取数据并绘制
            for tag in phase_tags:
                df = pd.DataFrame({'step': data[tag]['step'], 'value': data[tag]['value']})
                sns.lineplot(data=df, x='step', y='value', ax=ax, label=tag.split('/')[-1])

            ax.set_title(f"{group} - {phase.capitalize()}")
            ax.set_xlabel('Step')
            ax.set_ylabel(group)
            ax.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{group}_metrics.png"), dpi=300, bbox_inches='tight')
        plt.close()

    # 绘制训练损失与验证指标的对比图
    if 'Loss/train' in data and 'F1/eval' in data and 'H1/eval' in data:
        fig, ax1 = plt.subplots(figsize=(12, 8))
        ax2 = ax1.twinx()

        # 训练损失
        loss_df = pd.DataFrame({'step': data['Loss/train']['step'], 'value': data['Loss/train']['value']})
        sns.lineplot(data=loss_df, x='step', y='value', ax=ax1, color='blue', label='Train Loss')

        # 验证指标
        f1_df = pd.DataFrame({'step': data['F1/eval']['step'], 'value': data['F1/eval']['value']})
        h1_df = pd.DataFrame({'step': data['H1/eval']['step'], 'value': data['H1/eval']['value']})
        sns.lineplot(data=f1_df, x='step', y='value', ax=ax2, color='green', label='Eval F1')
        sns.lineplot(data=h1_df, x='step', y='value', ax=ax2, color='red', label='Eval H1')

        ax1.set_xlabel('Step')
        ax1.set_ylabel('Loss', color='blue')
        ax2.set_ylabel('Metric Value', color='green')
        ax1.tick_params(axis='y', colors='blue')
        ax2.tick_params(axis='y', colors='green')
        plt.title('Training Loss vs. Validation Metrics')
        fig.legend(loc='upper right', bbox_to_anchor=(0.9, 0.9))
        plt.savefig(os.path.join(output_dir, "loss_vs_metrics.png"), dpi=300, bbox_inches='tight')
        plt.close()


def main():
    parser = argparse.ArgumentParser(description='生成TensorBoard可视化图表')
    parser.add_argument('--log_dir', type=str, required=True, help='TensorBoard日志目录')
    parser.add_argument('--output_dir', type=str, default='tensorboard_plots', help='输出图像目录')
    args = parser.parse_args()

    # 提取数据并绘图
    data, tags = extract_tensorboard_data(args.log_dir)
    plot_curves(data, tags, args.output_dir)
    print("可视化完成！图表保存在：", args.output_dir)


if __name__ == '__main__':
    main()