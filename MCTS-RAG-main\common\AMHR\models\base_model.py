import torch
import numpy as np
import torch.nn as nn

# 导入必要的库
import numpy as np

# 定义一个非常小的数值，用于避免除零错误
VERY_SMALL_NUMBER = 1e-10

'''功能：该文件定义了一个基础模型类 BaseModel，用于创建嵌入、存储关系、计算 F1 和 H1 分数等功能。
主要方法：
__init__：初始化模型参数和嵌入。
embedding_def：定义实体、关系和单词的嵌入。
load_relation_file：加载关系嵌入文件。
calc_h1 和 get_eval_metric：计算命中率@1和F1分数。
特点：
支持多种语言模型（如BERT）。
提供灵活的嵌入初始化方式（从文件加载或随机初始化）。
包含多种损失函数（如KLDivLoss、BCEWithLogitsLoss等）。'''

# 定义BaseModel类，继承自torch.nn.Module
class BaseModel(torch.nn.Module):
    """
    Base model functions: create embeddings, store relations, compute f1/h1 scores, etc.
    """

    def __init__(self, args, num_entity, num_relation, num_word):
        super(BaseModel, self).__init__()
        # 初始化实体、关系和单词的数量
        self.num_relation = num_relation
        self.num_entity = num_entity
        self.num_word = num_word
        print('Num Word', self.num_word)

        # 从args中获取kge_frozen参数
        self.kge_frozen = args['kge_frozen']
        self.kg_dim = args['kg_dim']

        # 加载实体、关系和单词的嵌入文件路径
        self.entity_emb_file = args['entity_emb_file']
        self.relation_emb_file = args['relation_emb_file']
        self.relation_word_emb = args['relation_word_emb']
        self.word_emb_file = args['word_emb_file']
        self.entity_dim = args['entity_dim']

        # 获取语言模型类型（如bert）
        self.lm = args['lm']
        if self.lm in ['bert']:
            # 如果使用bert，设置单词维度为768
            args['word_dim'] = 768

        self.word_dim = args['word_dim']

        self.rel_texts = None

        # 设置设备为GPU或CPU
        self.device = torch.device('cuda' if args['use_cuda'] else 'cpu')

        print("Entity: {}, Relation: {}, Word: {}".format(num_entity, num_relation, num_word))

        # 定义损失函数
        self.kld_loss = nn.KLDivLoss(reduction='none')
        self.bce_loss = nn.BCEWithLogitsLoss(reduction='none')
        self.mse_loss = torch.nn.MSELoss()

        # 遍历args，初始化维度和嵌入文件路径
        for k, v in args.items():
            if k.endswith('dim'):
                setattr(self, k, v)
            if k.endswith('emb_file') or k.endswith('kge_file'):
                if v is None:
                    setattr(self, k, None)
                else:
                    setattr(self, k, args['data_folder'] + v)

        self.reset_time = 0

        # 初始化是否使用逆关系和自环
        if 'use_inverse_relation' in args:
            self.use_inverse_relation = args['use_inverse_relation']
        if 'use_self_loop' in args:
            self.use_self_loop = args['use_self_loop']
        self.eps = args['eps']

        # 调用embedding_def方法初始化嵌入
        self.embedding_def()
        args['word_dim'] = self.word_dim

        # KGE集成：初始化KGE嵌入
        self._initialize_kge_embeddings(args)

    def embedding_def(self):
        # 获取实体、关系和单词的数量
        num_entity = self.num_entity
        num_relation = self.num_relation
        num_word = self.num_word

        if self.lm != 'lstm':
            # 如果语言模型不是lstm，设置单词维度为768
            self.word_dim = 768
            self.word_embedding = nn.Embedding(num_embeddings=num_word + 1, embedding_dim=self.word_dim,
                                           padding_idx=num_word)
        elif self.word_emb_file is not None:
            # 如果存在单词嵌入文件，加载并初始化嵌入
            word_emb = np.load(self.word_emb_file)
            _ , self.word_dim = word_emb.shape
            print('Word emb dim', self.word_dim)
            self.word_embedding = nn.Embedding(num_embeddings=num_word + 1, embedding_dim=self.word_dim,
                                           padding_idx=num_word)
            self.word_embedding.weight = nn.Parameter(
                torch.from_numpy(
                    np.pad(np.load(self.word_emb_file), ((0, 1), (0, 0)), 'constant')).type(
                    'torch.FloatTensor'))
            self.word_embedding.weight.requires_grad = False
        else:
            # 否则，初始化默认的单词嵌入
            self.word_embedding = nn.Embedding(num_embeddings=num_word + 1, embedding_dim=self.word_dim,
                                           padding_idx=num_word)

        # 初始化实体嵌入
        if self.entity_emb_file is not None:
            self.encode_type = False
            emb = np.load(self.entity_emb_file)
            ent_num , self.ent_dim = emb.shape
            self.entity_embedding = nn.Embedding(num_embeddings=num_entity + 1, embedding_dim=self.ent_dim,
                                                padding_idx=num_entity)
            if ent_num != num_entity:
                print('Number of entities in KG embeddings do not match: Random Init.')
            else:
                self.entity_embedding.weight = nn.Parameter(
                    torch.from_numpy(np.pad(emb, ((0, 1), (0, 0)), 'constant')).type(
                        'torch.FloatTensor'))
            if self.kge_frozen:
                self.entity_embedding.weight.requires_grad = False
            else:
                self.entity_embedding.weight.requires_grad = True
        else:
            self.ent_dim = self.kg_dim
            self.encode_type = True

        # 初始化关系嵌入
        if self.relation_emb_file is not None:
            np_tensor = self.load_relation_file(self.relation_emb_file)
            rel_num, self.rel_dim = np_tensor.shape
            self.relation_embedding = nn.Embedding(num_embeddings=num_relation+1, embedding_dim=self.rel_dim)
            if rel_num != num_relation:
                 print('Number of relations in KG embeddings do not match: Random Init.')
            else:
                self.relation_embedding.weight = nn.Parameter(torch.from_numpy(np_tensor).type('torch.FloatTensor'))
            if self.kge_frozen:
                self.relation_embedding.weight.requires_grad = False
            else:
                self.relation_embedding.weight.requires_grad = True

        elif self.relation_word_emb:
            self.rel_dim = self.entity_dim
            self.relation_embedding = nn.Embedding(num_embeddings=num_relation+1, embedding_dim=self.rel_dim)
            self.relation_embedding.weight.requires_grad = True
            self.relation_embedding_inv = nn.Embedding(num_embeddings=num_relation+1, embedding_dim=self.rel_dim)
            self.relation_embedding_inv.weight.requires_grad = True
        else:
            self.rel_dim = 2*self.kg_dim
            self.relation_embedding = nn.Embedding(num_embeddings=num_relation+1, embedding_dim=self.rel_dim)
            self.relation_embedding_inv = nn.Embedding(num_embeddings=num_relation+1, embedding_dim=self.rel_dim)

    def load_relation_file(self, filename):
        # 加载关系嵌入文件
        half_tensor = np.load(filename)
        num_pad = 0
        if self.use_self_loop:
            num_pad = 2
        if self.use_inverse_relation:
            load_tensor = np.concatenate([half_tensor, half_tensor])
        else:
            load_tensor = half_tensor
        return np.pad(load_tensor, ((0, num_pad), (0, 0)), 'constant')

    def use_rel_texts(self, rel_texts, rel_texts_inv):
        self.rel_texts = torch.from_numpy(rel_texts).type('torch.LongTensor').to(self.device)
        self.rel_texts_inv = torch.from_numpy(rel_texts_inv).type('torch.LongTensor').to(self.device)

    def encode_rel_texts(self, rel_texts, rel_texts_inv):
        self.rel_texts = torch.from_numpy(rel_texts).type('torch.LongTensor').to(self.device)
        self.rel_texts_inv = torch.from_numpy(rel_texts_inv).type('torch.LongTensor').to(self.device)
        self.instruction.eval()
        with torch.no_grad():
            self.rel_features = self.instruction.encode_question(self.rel_texts, store=False)
            self.rel_features_inv = self.instruction.encode_question(self.rel_texts_inv, store=False)
        self.rel_features.requires_grad = False
        self.rel_features_inv.requires_grad = False

    def init_hidden(self, num_layer, batch_size, hidden_size):
        return self.instruction.init_hidden(num_layer, batch_size, hidden_size)

    def encode_question(self, q_input):
        return self.instruction.encode_question(q_input)

    def get_instruction(self, query_hidden_emb, query_mask, states):
        return self.instruction.get_instruction(query_hidden_emb, query_mask, states)

    def get_loss_bce(self, pred_dist_score, answer_dist):
        answer_dist = (answer_dist > 0).float() * 0.9   # label smooth
        # answer_dist = answer_dist * 0.9  # label smooth
        loss = self.bce_loss_logits(pred_dist_score, answer_dist)
        return loss

    def get_loss_kl(self, pred_dist, answer_dist):
        answer_len = torch.sum(answer_dist, dim=1, keepdim=True)
        answer_len[answer_len == 0] = 1.0
        answer_prob = answer_dist.div(answer_len)
        log_prob = torch.log(pred_dist + 1e-8)
        loss = self.kld_loss(log_prob, answer_prob)
        return loss

    def get_loss(self, pred_dist, answer_dist, reduction='mean'):
        if self.loss_type == "bce":
            tp_loss = self.get_loss_bce(pred_dist, answer_dist)
            if reduction == 'none':
                return tp_loss
            else:
                # mean
                return torch.mean(tp_loss)
        else:
            tp_loss = self.get_loss_kl(pred_dist, answer_dist)
            if reduction == 'none':
                return tp_loss
            else:
                # batchmean
                return torch.sum(tp_loss) / pred_dist.size(0)

    def f1_and_hits(self, answers, candidate2prob, eps=0.5):
        retrieved = []
        correct = 0
        cand_list = sorted(candidate2prob, key=lambda x:x[1], reverse=True)
        if len(cand_list) == 0:
            best_ans = -1
        else:
            best_ans = cand_list[0][0]
        # max_prob = cand_list[0][1]
        tp_prob = 0.0
        for c, prob in cand_list:
            retrieved.append((c, prob))
            tp_prob += prob
            if c in answers:
                correct += 1
            if tp_prob > eps:
                break
        if len(answers) == 0:
            if len(retrieved) == 0:
                return 1.0, 1.0, 1.0, 1.0  # precision, recall, f1, hits
            else:
                return 0.0, 1.0, 0.0, 1.0  # precision, recall, f1, hits
        else:
            hits = float(best_ans in answers)
            if len(retrieved) == 0:
                return 1.0, 0.0, 0.0, hits  # precision, recall, f1, hits
            else:
                p, r = correct / len(retrieved), correct / len(answers)
                f1 = 2.0 / (1.0 / p + 1.0 / r) if p != 0 and r != 0 else 0.0
                return p, r, f1, hits


    def calc_f1_new(self, curr_dist, dist_ans, h1_vec):
        batch_size = curr_dist.size(0)
        max_local_entity = curr_dist.size(1)
        seed_dist = self.seed_entities #self.dist_history[0]
        local_entity = self.local_entity
        ignore_prob = (1 - self.eps) / max_local_entity
        pad_ent_id = self.num_entity
        # hits_list = []
        f1_list = []
        for batch_id in range(batch_size):
            if h1_vec[batch_id].item() == 0.0:
                f1_list.append(0.0)
                # we consider cases which own hit@1 as prior to reduce computation time
                continue
            candidates = local_entity[batch_id, :].tolist()
            probs = curr_dist[batch_id, :].tolist()
            answer_prob = dist_ans[batch_id, :].tolist()
            seed_entities = seed_dist[batch_id, :].tolist()
            answer_list = []
            candidate2prob = []
            for c, p, p_a, s in zip(candidates, probs, answer_prob, seed_entities):
                if s > 0:
                    # ignore seed entities
                    continue
                if c == pad_ent_id:
                    continue
                if p_a > 0:
                    answer_list.append(c)
                if p < ignore_prob:
                    continue
                candidate2prob.append((c, p))
            precision, recall, f1, hits = self.f1_and_hits(answer_list, candidate2prob, self.eps)
            # hits_list.append(hits)
            f1_list.append(f1)
        # hits_vec = torch.FloatTensor(hits_list).to(self.device)
        f1_vec = torch.FloatTensor(f1_list).to(self.device)
        return f1_vec

    def calc_h1(self, curr_dist, dist_ans, eps=0.01):
        # 计算命中率@1
        greedy_option = curr_dist.argmax(dim=-1, keepdim=True)
        dist_top1 = torch.zeros_like(curr_dist).scatter_(1, greedy_option, 1.0)
        dist_ans = (dist_ans > eps).float()
        h1 = torch.sum(dist_top1 * dist_ans, dim=-1)
        return (h1 > 0).float()

    def get_eval_metric(self, pred_dist, answer_dist):
        # 计算评估指标（命中率@1和F1分数）
        with torch.no_grad():
            h1 = self.calc_h1(curr_dist=pred_dist, dist_ans=answer_dist, eps=VERY_SMALL_NUMBER)
            f1 = self.calc_f1_new(pred_dist, answer_dist, h1)
        return h1, f1

    def _initialize_kge_embeddings(self, args):
        """
        初始化KGE嵌入模块
        
        Args:
            args (dict): 包含KGE配置的参数字典
        """
        import logging
        logger = logging.getLogger(__name__)
        
        # 检查是否启用KGE
        self.use_complex_kge = args.get('use_complex_kge', False)
        if not self.use_complex_kge:
            self.kge_embedding = None
            self.kge_fusion_layer = None
            return
        
        logger.info("🔧 初始化KGE嵌入模块...")
        
        # KGE参数
        self.complex_hidden_dim = args.get('complex_hidden_dim', 50)
        self.kge_fusion_method = args.get('kge_fusion_method', 'concat')
        self.complex_checkpoint_path = args.get('complex_checkpoint_path', '')
        self.max_distance = args.get('max_distance', 5)
        self.freeze_kge_embeddings = args.get('freeze_kge_embeddings', True)
        self.kge_model_type = args.get('kge_model_type', 'ComplX')
        
        # 动态导入KGE模块
        try:
            from ..kge import create_kge_embedding
            
            # 创建KGE嵌入实例
            self.kge_embedding = create_kge_embedding(
                model_type=self.kge_model_type,
                num_entities=self.num_entity,
                num_relations=self.num_relation,
                hidden_dim=self.complex_hidden_dim,
                device=self.device
            )
            
            # 加载预训练权重（如果提供）
            if self.complex_checkpoint_path and self.complex_checkpoint_path.strip():
                if self.kge_model_type == 'ComplX':
                    self.kge_embedding.load_grail_checkpoint(self.complex_checkpoint_path)
                else:
                    self.kge_embedding.load_pretrained_weights(self.complex_checkpoint_path)
            
            # 设置参数冻结状态
            if self.freeze_kge_embeddings:
                self.kge_embedding.freeze_parameters()
            else:
                self.kge_embedding.unfreeze_parameters()
            
            # 计算融合后的实体维度
            kge_embedding_dim = self.complex_hidden_dim * 2  # ComplX使用复数嵌入
            distance_feature_dim = self.max_distance + 1
            
            if self.kge_fusion_method == 'concat':
                self.fused_entity_dim = self.entity_dim + kge_embedding_dim + distance_feature_dim
            elif self.kge_fusion_method == 'add':
                if self.entity_dim != kge_embedding_dim:
                    raise ValueError(f"Add融合要求维度匹配: entity_dim={self.entity_dim}, kge_dim={kge_embedding_dim}")
                self.fused_entity_dim = self.entity_dim
            elif self.kge_fusion_method == 'mlp':
                input_dim = self.entity_dim + kge_embedding_dim + distance_feature_dim
                self.fused_entity_dim = self.entity_dim  # 保持原始维度
                self.kge_fusion_layer = nn.Sequential(
                    nn.Linear(input_dim, self.entity_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(self.entity_dim * 2, self.entity_dim)
                )
            else:
                raise ValueError(f"不支持的融合方式: {self.kge_fusion_method}")
            
            logger.info(f"✅ KGE嵌入初始化完成:")
            logger.info(f"   模型类型: {self.kge_model_type}")
            logger.info(f"   嵌入维度: {kge_embedding_dim}")
            logger.info(f"   融合方式: {self.kge_fusion_method}")
            logger.info(f"   融合后维度: {self.fused_entity_dim}")
            logger.info(f"   参数冻结: {self.freeze_kge_embeddings}")
            
        except ImportError as e:
            logger.error(f"❌ KGE模块导入失败: {e}")
            self.use_complex_kge = False
            self.kge_embedding = None
            self.kge_fusion_layer = None
        except Exception as e:
            logger.error(f"❌ KGE嵌入初始化失败: {e}")
            self.use_complex_kge = False
            self.kge_embedding = None  
            self.kge_fusion_layer = None
    
    def get_enhanced_entity_embeddings(self, entity_ids, distance_features=None):
        """
        获取增强的实体嵌入（融合原始嵌入、KGE嵌入和距离特征）
        
        Args:
            entity_ids (torch.Tensor): 实体ID张量, shape=[batch_size, num_entities]
            distance_features (torch.Tensor, optional): 距离特征, shape=[batch_size, num_entities, max_distance+1]
            
        Returns:
            torch.Tensor: 融合后的实体嵌入, shape=[batch_size, num_entities, fused_entity_dim]
        """
        # 获取原始实体嵌入
        if hasattr(self, 'entity_embedding') and self.entity_embedding is not None:
            original_emb = self.entity_embedding(entity_ids)  # [batch_size, num_entities, entity_dim]
        else:
            # 如果没有预训练嵌入，使用随机初始化
            batch_size, num_entities = entity_ids.shape
            original_emb = torch.randn(batch_size, num_entities, self.entity_dim, device=self.device)
        
        # 如果未启用KGE，直接返回原始嵌入
        if not self.use_complex_kge or self.kge_embedding is None:
            return original_emb
        
        # 获取KGE嵌入
        kge_emb = self.kge_embedding.get_entity_embedding(entity_ids)  # [batch_size, num_entities, kge_dim]
        
        # 准备融合特征列表
        features_to_fuse = [original_emb, kge_emb]
        
        # 添加距离特征（如果提供）
        if distance_features is not None:
            features_to_fuse.append(distance_features)
        else:
            # 如果没有提供距离特征，使用零填充
            batch_size, num_entities = entity_ids.shape
            zero_distance = torch.zeros(batch_size, num_entities, self.max_distance + 1, device=self.device)
            features_to_fuse.append(zero_distance)
        
        # 根据融合方式处理
        if self.kge_fusion_method == 'concat':
            fused_emb = torch.cat(features_to_fuse, dim=-1)
        elif self.kge_fusion_method == 'add':
            fused_emb = original_emb + kge_emb  # 距离特征在add模式下被忽略
        elif self.kge_fusion_method == 'mlp':
            concat_features = torch.cat(features_to_fuse, dim=-1)
            fused_emb = self.kge_fusion_layer(concat_features)
        
        return fused_emb