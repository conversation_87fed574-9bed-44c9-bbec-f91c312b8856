import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.nn import Dropout

class Fusion(nn.Module):
    """现有门控融合模块 (保持不变)"""
    def __init__(self, d_hid):
        super(Fusion, self).__init__()
        self.r = nn.Linear(d_hid*3, d_hid, bias=False)
        self.g = nn.Linear(d_hid*3, d_hid, bias=False)

    def forward(self, x, y):
        r_ = self.r(torch.cat([x,y,x-y], dim=-1))
        g_ = torch.sigmoid(self.g(torch.cat([x,y,x-y], dim=-1)))
        return g_ * r_ + (1 - g_) * x

class AttnEncoder(nn.Module):
    """现有注意力编码器 (保持不变)"""
    def __init__(self, d_hid):
        super(AttnEncoder, self).__init__()
        self.attn_linear = nn.Linear(d_hid, 1, bias=False)

    def forward(self, x, x_mask):
        """
        x: (B, len, d_hid)
        x_mask: (B, len)
        return: (B, d_hid)
        """
        x_attn = self.attn_linear(x)
        x_attn = x_attn - (1 - x_mask.unsqueeze(2))*1e8
        x_attn = F.softmax(x_attn, dim=1)
        return (x*x_attn).sum(1)

class Attention(nn.Module):
    """现有注意力模块 (保持不变)"""

    def __init__(self, dimensions, attention_type='general'):
        super(Attention, self).__init__()

        if attention_type not in ['dot', 'general']:
            raise ValueError('Invalid attention type selected.')

        self.attention_type = attention_type
        if self.attention_type == 'general':
            self.linear_in = nn.Linear(dimensions, dimensions, bias=False)

        self.linear_out = nn.Linear(dimensions * 2, dimensions, bias=False)
        self.softmax = nn.Softmax(dim=-1)
        self.tanh = nn.Tanh()

    def forward(self, query, context):
        """
        Args:
            query (:class:`torch.FloatTensor` [batch size, output length, dimensions]): Sequence of
                queries to query the context.
            context (:class:`torch.FloatTensor` [batch size, query length, dimensions]): Data
                overwhich to apply the attention mechanism.

        Returns:
            :class:`tuple` with `output` and `weights`:
            * **output** (:class:`torch.LongTensor` [batch size, output length, dimensions]):
              Tensor containing the attended features.
            * **weights** (:class:`torch.FloatTensor` [batch size, output length, query length]):
              Tensor containing attention weights.
        """
        batch_size, output_len, dimensions = query.size()
        query_len = context.size(1)

        if self.attention_type == "general":
            query = query.reshape(batch_size * output_len, dimensions)
            query = self.linear_in(query)
            query = query.reshape(batch_size, output_len, dimensions)

        # (batch_size, output_len, dimensions) * (batch_size, query_len, dimensions) ->
        # (batch_size, output_len, query_len)
        attention_scores = torch.bmm(query, context.transpose(1, 2).contiguous())

        # Compute weights across every context sequence
        attention_scores = attention_scores.view(batch_size * output_len, query_len)
        attention_weights = self.softmax(attention_scores)
        attention_weights = attention_weights.view(batch_size, output_len, query_len)

        # (batch_size, output_len, query_len) * (batch_size, query_len, dimensions) ->
        # (batch_size, output_len, dimensions)
        mix = torch.bmm(attention_weights, context)

        # concat -> (batch_size * output_len, 2*dimensions)
        combined = torch.cat((mix, query), dim=2)
        combined = combined.view(batch_size * output_len, 2 * dimensions)

        # Apply linear_out on every 2nd dimension of concat
        # output -> (batch_size, output_len, dimensions)
        output = self.linear_out(combined).view(batch_size, output_len, dimensions)
        output = self.tanh(output)

        return output, attention_weights

class QueryReform(nn.Module):
    """扩展后的查询改革模块，支持AMHR机制"""
    def __init__(self, h_dim, max_depth=5, max_macro_rounds=3, enable_amhr=True, dropout_rate=0.1, **kwargs):
        super(QueryReform, self).__init__()
        # 保留原有组件以确保向后兼容
        self.fusion = Fusion(h_dim)
        self.q_ent_attn = nn.Linear(h_dim, h_dim)
        
        # 新增: AMHR组件
        self.h_dim = h_dim
        self.enable_amhr = enable_amhr
        self.max_depth = max_depth
        self.max_macro_rounds = max_macro_rounds
        
        # 效率优化参数（从kwargs获取，设置默认值）
        self.macro_trigger_threshold = kwargs.get('amhr_macro_trigger_threshold', 0.1)
        self.micro_min_strength = kwargs.get('amhr_micro_min_strength', 0.05)
        self.adaptive_scaling = kwargs.get('amhr_adaptive_scaling', True)
        self.efficiency_mode = kwargs.get('amhr_efficiency_mode', True)
        
        # 性能监控
        self.macro_skip_count = 0
        self.micro_enhance_count = 0
        self.last_validation_loss = float('inf')
        
        if enable_amhr:
            # 多模态反思: 向量 + 伪语言生成器
            self.micro_net = nn.Sequential(
                nn.Linear(h_dim * 3, h_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            self.verbal_gen = nn.Sequential(
                nn.Linear(h_dim, h_dim),
                nn.Tanh()
            )
            self.macro_net = nn.Sequential(
                nn.Linear(h_dim * 4, h_dim),  # 减少输入维度以适应实际使用
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            
            # 前瞻预测器: 预测更新效果
            self.foresee_predictor = nn.Sequential(
                nn.Linear(h_dim * 2, h_dim // 2),
                nn.ReLU(),
                nn.Linear(h_dim // 2, 1),
                nn.Sigmoid()
            )
            
            # 不确定性量化
            self.dropout = Dropout(dropout_rate)
            
            # 历史跟踪 (使用EMA)
            self.micro_ema = 0.0
            self.macro_ema = 0.0
            self.ema_alpha = 0.9
            
            # 跨层蒸馏权重
            self.distill_weight = nn.Parameter(torch.tensor(0.5))
            
            # 统计计数器
            self.micro_count = 0
            self.macro_count = 0

    def forward(self, q_node, ent_emb, seed_info, ent_mask, depth=0, is_macro=False, path_stats=None):
        """
        扩展的前向传播方法，支持AMHR
        
        Args:
            q_node: 查询节点嵌入 (B, h_dim)
            ent_emb: 实体嵌入 (B, C, h_dim)  
            seed_info: 种子信息 (B, C)
            ent_mask: 实体掩码 (B, C)
            depth: 当前推理深度 (用于微观衰减)
            is_macro: 是否执行宏观反思
            path_stats: 路径统计信息 (用于宏观反思)
        """
        # 原有的基本逻辑 (确保向后兼容)
        q_ent_attn = (self.q_ent_attn(q_node).unsqueeze(1) * ent_emb).sum(2, keepdim=True)
        q_ent_attn = F.softmax(q_ent_attn - (1 - ent_mask.unsqueeze(2)) * 1e8, dim=1)
        attn_retrieve = (q_ent_attn * ent_emb).sum(1)
        seed_retrieve = torch.bmm(seed_info.unsqueeze(1), ent_emb).squeeze(1)
        base_update = self.fusion(q_node, seed_retrieve)
        
        if not self.enable_amhr:
            return base_update
        
        # 执行AMHR反思机制
        if not is_macro:
            return self._micro_reflection(q_node, attn_retrieve, seed_retrieve, base_update, depth)
        else:
            return self._macro_reflection(q_node, attn_retrieve, seed_retrieve, base_update, path_stats)

    def _micro_reflection(self, q_node, attn_retrieve, seed_retrieve, base_update, depth):
        """微观反思：局部迭代调整（优化版本）"""
        
        if depth > self.max_depth:
            return base_update
            
        # 1. 多模态输入构建
        input_tensor = torch.cat([q_node, attn_retrieve, seed_retrieve], dim=-1)
        
        # 2. 向量调整
        vector_adjust = self.micro_net(input_tensor)
        
        # 3. 伪语言嵌入生成（条件生成，提高效率）
        if depth < 3:  # 只在前3步生成verbal反馈
            verbal_adjust = self.verbal_gen(vector_adjust)
        else:
            verbal_adjust = torch.zeros_like(vector_adjust)
        
        # 4. 多模态融合
        adjustment = (vector_adjust + verbal_adjust) / 2
        
        # 5. 不确定性量化（效率优化：减少采样次数）
        num_samples = 1 if self.efficiency_mode else 2
        uncertainty = self._estimate_uncertainty(input_tensor, self.micro_net, num_samples=num_samples)
        
        # 6. 前瞻协调：预测更新效果
        foresee_input = torch.cat([q_node, adjustment], dim=-1)
        foresee_score = torch.sigmoid(self.foresee_predictor(foresee_input))
        
        # 7. 增强的自适应强度计算
        if self.adaptive_scaling:
            # 深度衰减：随深度增加而减弱
            depth_factor = max(0.3, 1.0 - depth * 0.12)
            # 不确定性调节：不确定性高时减少强度
            confidence_factor = (1 - uncertainty)
            # 前瞻调节：预期效果好时增加强度
            foresee_factor = foresee_score
            # 历史调节：基于之前微观反思的成功率
            history_factor = max(0.5, min(1.5, self.micro_ema * 5))
            
            strength = (self.micro_strength * depth_factor * confidence_factor * 
                       foresee_factor * history_factor)
        else:
            strength = self.micro_strength * foresee_score * (1 - uncertainty)
        
        # 8. 应用最小强度下界
        strength = torch.clamp(strength, min=self.micro_min_strength)
        
        # 9. 执行更新
        updated_query = base_update + strength * adjustment
        
        # 10. 历史EMA更新和统计
        current_strength = strength.mean().item()
        self.micro_ema = self.ema_alpha * self.micro_ema + (1 - self.ema_alpha) * current_strength
        self.micro_count += 1
        
        # 记录有效的微观增强
        if current_strength > self.micro_min_strength * 1.5:
            self.micro_enhance_count += 1
        
        return updated_query

    def _macro_reflection(self, q_node, attn_retrieve, seed_retrieve, base_update, path_stats):
        """宏观反思：全局重构（优化版本）"""
        
        # 效率优化：检查是否应该跳过宏观反思
        if self.efficiency_mode and self._should_skip_macro_reflection():
            self.macro_skip_count += 1
            return base_update
        
        # 1. 跨层蒸馏（使用正则化的权重）
        clamped_distill_weight = torch.clamp(self.distill_weight, 0.2, 0.8)
        distilled_micro = clamped_distill_weight * base_update + (1 - clamped_distill_weight) * attn_retrieve
        
        # 2. 构建宏观输入（简化版本）
        if path_stats is None:
            path_stats = torch.mean(torch.cat([q_node, attn_retrieve, seed_retrieve], dim=-1), dim=-1, keepdim=True)
        
        input_tensor = torch.cat([q_node, distilled_micro, seed_retrieve, path_stats], dim=-1)
        
        # 3. 向量调整
        vector_adjust = self.macro_net(input_tensor)
        
        # 4. 伪语言嵌入（条件生成，仅第一轮）
        if self.macro_count == 0:  # 只在第一次宏观反思时生成
            verbal_adjust = self.verbal_gen(vector_adjust)
        else:
            verbal_adjust = torch.zeros_like(vector_adjust)  # 后续轮次跳过
        
        # 5. 多模态融合
        adjustment = (vector_adjust + verbal_adjust) / 2
        
        # 6. 前瞻预测 + 不确定性（减少采样）
        foresee_input = torch.cat([q_node, adjustment], dim=-1)
        foresee_score = torch.sigmoid(self.foresee_predictor(foresee_input))
        
        num_samples = 1 if self.efficiency_mode else 2
        uncertainty = self._estimate_uncertainty(input_tensor, self.macro_net, num_samples=num_samples)
        
        # 7. 增强的协调机制
        # 饱和度检测：宏观反思次数增加时快速衰减
        saturation_factor = max(0.1, 1.0 / (1.0 + self.macro_count * 0.5))
        
        # 微观-宏观平衡：理想比例是微观:宏观 = 4:1
        ideal_ratio = 4.0
        current_ratio = max(0.1, self.micro_count / max(1, self.macro_count + 1))
        balance_factor = min(1.0, current_ratio / ideal_ratio)
        
        # 微观成功率：如果微观反思很成功，减少宏观干预
        micro_success_factor = max(0.3, 1.0 - self.micro_ema * 3) if self.micro_ema > 0.5 else 1.0
        
        # 8. 自适应强度计算（大幅降低基础强度）
        base_macro_strength = self.macro_strength * 0.6  # 降低基础强度
        confidence_factor = (1 - uncertainty)
        
        strength = (base_macro_strength * foresee_score * confidence_factor * 
                   saturation_factor * balance_factor * micro_success_factor)
        
        # 9. 执行更新
        updated_query = base_update + strength * adjustment
        
        # 10. 历史EMA更新
        self.macro_ema = self.ema_alpha * self.macro_ema + (1 - self.ema_alpha) * strength.mean().item()
        self.macro_count += 1
        
        return updated_query
    
    def _should_skip_macro_reflection(self):
        """
        判断是否应该跳过宏观反思（效率优化核心方法）
        
        Returns:
            bool: True if should skip, False otherwise
        """
        # 条件1：已达到最大宏观反思轮数
        if self.macro_count >= self.max_macro_rounds:
            return True
        
        # 条件2：微观反思效果已经很好，无需宏观干预
        if self.micro_ema > 0.7 and self.micro_count > 5:
            return True
        
        # 条件3：微观-宏观比例已经达到理想状态
        if self.micro_count >= 4 * (self.macro_count + 1):
            return True
        
        # 条件4：近期宏观反思效果很差
        if self.macro_count > 0 and self.macro_ema < 0.03:
            return True
        
        # 条件5：计算资源紧张时的概率性跳过
        if self.efficiency_mode and self.macro_count > 0:
            # 基于历史效果的概率跳过
            skip_probability = 1.0 - min(0.8, self.macro_ema * 10)
            import random
            if random.random() < skip_probability:
                return True
        
        return False

    def _estimate_uncertainty(self, input_tensor, network, num_samples=3):
        """使用dropout变异估计不确定性"""
        network.train()  # 启用dropout
        outputs = []
        
        with torch.no_grad():
            for _ in range(num_samples):
                output = network(input_tensor)
                outputs.append(output)
        
        # 计算标准差作为不确定性度量
        outputs_stack = torch.stack(outputs, dim=0)
        uncertainty = torch.std(outputs_stack, dim=0).mean().item()
        
        network.eval()  # 恢复评估模式
        return min(uncertainty, 1.0)  # 限制在[0,1]范围内

    def get_amhr_summary(self):
        """获取AMHR优化统计摘要"""
        if not self.enable_amhr:
            return {'amhr_enabled': False}
            
        total_count = self.micro_count + self.macro_count
        ratio = self.micro_count / max(1, total_count)
        
        # 效率指标
        efficiency_score = self.micro_enhance_count / max(1, self.micro_count)
        skip_ratio = self.macro_skip_count / max(1, self.macro_skip_count + self.macro_count)
        
        # 质量评估（更细致的分级）
        if ratio >= 0.75:
            quality = '优秀'
            balance_status = 'Micro主导(理想)'
        elif ratio >= 0.5:
            quality = '良好'
            balance_status = '平衡良好'
        elif ratio >= 0.3:
            quality = '一般'
            balance_status = '需轻微调整'
        else:
            quality = '需优化'
            balance_status = 'Macro过多(低效)'
        
        return {
            'amhr_enabled': True,
            'micro_count': self.micro_count,
            'macro_count': self.macro_count,
            'micro_ema': self.micro_ema,
            'macro_ema': self.macro_ema,
            'ratio': ratio,
            'quality': quality,
            'efficiency_score': efficiency_score,
            'macro_skip_count': self.macro_skip_count,
            'macro_skip_ratio': skip_ratio,
            'micro_enhance_count': self.micro_enhance_count,
            'balance_status': balance_status,
            'optimization_suggestions': self._get_optimization_suggestions(ratio, efficiency_score, skip_ratio)
        }
    
    def _get_optimization_suggestions(self, ratio, efficiency_score, skip_ratio):
        """基于统计数据生成优化建议"""
        suggestions = []
        
        if ratio < 0.3:
            suggestions.append("宏观反思过多，建议减少amhr_max_macro_rounds或增加amhr_max_depth")
        elif ratio > 0.9:
            suggestions.append("微观反思过多，建议适当增加宏观反思强度")
        
        if efficiency_score < 0.3:
            suggestions.append("微观反思效果低，建议调整amhr_micro_strength或启用自适应缩放")
        
        if skip_ratio < 0.2:
            suggestions.append("宏观反思跳过率低，建议启用efficiency_mode优化")
        elif skip_ratio > 0.8:
            suggestions.append("宏观反思跳过率过高，检查macro_trigger_threshold设置")
        
        return suggestions if suggestions else ["当前配置良好，无需调整"]

    def reset_amhr_stats(self):
        """重置AMHR统计信息（用于新的训练epoch）"""
        if self.enable_amhr:
            self.micro_ema = 0.0
            self.macro_ema = 0.0
            self.micro_count = 0
            self.macro_count = 0
            self.macro_skip_count = 0
            self.micro_enhance_count = 0
            self.last_validation_loss = float('inf')

# 向后兼容的工厂函数
def create_query_reform(h_dim, enable_amhr=False, **kwargs):
    """
    工厂函数：创建QueryReform实例
    用于确保向后兼容性
    """
    return QueryReform(h_dim, enable_amhr=enable_amhr, **kwargs)