import torch.nn.functional as F
import torch.nn as nn
VERY_SMALL_NUMBER = 1e-10
VERY_NEG_NUMBER = -100000000000
import torch

from transformers import AutoModel, AutoTokenizer
from torch.nn import LayerNorm
import warnings
warnings.filterwarnings("ignore")

import os
os.environ['TRANSFORMERS_CACHE'] = '/export/scratch/costas/home/<USER>/.cache'

from .base_encoder import BaseInstruction
from utils import get_dict


class BERTLSTMInstruction(BaseInstruction):
    """并行混合指令编码：BERT使用BERT词表，BiLSTM使用LSTM词表"""

    def __init__(self, args, word_embedding, num_word, constraint=False):
        super(BERTLSTMInstruction, self).__init__(args, constraint)
        
        # 基础参数
        self.word_embedding = word_embedding  # LSTM词嵌入
        self.num_word = num_word  # LSTM词表大小
        self.constraint = constraint
        self.word2id = get_dict(args['data_folder'], args['word2id'])  # LSTM词表映射
        
        # 实体维度
        entity_dim = self.entity_dim
        
        # ----- BERT编码器配置 -----
        self.bert_tokenizer = AutoTokenizer.from_pretrained('pretrained_lms/sr-simbert/')
        self.bert_model = AutoModel.from_pretrained('pretrained_lms/sr-simbert/')
        self.bert_pad_val = self.bert_tokenizer.convert_tokens_to_ids(self.bert_tokenizer.pad_token)
        self.bert_hidden_dim = 768  # BERT输出维度
        
        # ----- BiLSTM编码器配置 -----
        self.lstm_word_dim = self.word_dim  # LSTM词嵌入维度
        self.lstm_hidden_dim = entity_dim // 2  # BiLSTM隐层维度（输出将是entity_dim）
        
        # 网络层定义
        self.cq_linear = nn.Linear(in_features=4 * entity_dim, out_features=entity_dim)
        self.ca_linear = nn.Linear(in_features=entity_dim, out_features=1)
        
        # 动态添加多个问题变换层
        for i in range(self.num_ins):
            self.add_module('question_linear' + str(i), nn.Linear(in_features=entity_dim, out_features=entity_dim))
        
        # ----- 并行编码器 -----
        # BERT编码器
        self.bert_encoder = self.bert_model
        self.bert_proj = nn.Linear(in_features=self.bert_hidden_dim, out_features=entity_dim)
        
        # BiLSTM编码器
        self.lstm_encoder = nn.LSTM(
            input_size=self.lstm_word_dim, 
            hidden_size=self.lstm_hidden_dim,
            num_layers=1,
            bidirectional=True,
            batch_first=True
        )
        self.lstm_proj = nn.Linear(in_features=entity_dim, out_features=entity_dim)
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(in_features=entity_dim * 2, out_features=entity_dim),
            nn.ReLU(),
            nn.Dropout(self.linear_dropout)
        )
        
        # Token级融合
        self.token_fusion = nn.Linear(in_features=self.bert_hidden_dim + entity_dim, out_features=entity_dim)
        
        # 控制LM冻结
        if self.lm_frozen == 1:
            print('Freezing BERT params')
            for param in self.bert_encoder.parameters():
                param.requires_grad = False
        else:
            print('Unfrozen BERT params')
            for param in self.bert_encoder.parameters():
                param.requires_grad = True

    def _encode_with_bert(self, query_text):
        """使用BERT词表进行编码"""
        batch_size = query_text.size(0)
        
        # 确保token ID在BERT词表范围内
        vocab_size = self.bert_encoder.embeddings.word_embeddings.weight.size(0)
        safe_query_text = torch.where(
            query_text >= vocab_size,
            torch.full_like(query_text, self.bert_pad_val),
            query_text
        )
        
        # BERT编码
        bert_output = self.bert_encoder(safe_query_text)[0]  # B, L, 768
        bert_cls = bert_output[:, 0, :]  # B, 768
        bert_projected = self.bert_proj(bert_cls)  # B, entity_dim
        
        return bert_output, bert_projected

    def _encode_with_lstm(self, query_text):
        """使用LSTM词表进行编码"""
        batch_size = query_text.size(0)
        
        # 使用LSTM词嵌入
        query_word_emb = self.word_embedding(query_text)  # B, L, word_dim
        
        # BiLSTM编码
        lstm_output, (h_n, c_n) = self.lstm_encoder(
            self.lstm_drop(query_word_emb),
            self.init_hidden(2, batch_size, self.lstm_hidden_dim)  # 2 = bidirectional
        )
        
        # 拼接双向LSTM的最终隐状态
        lstm_final = torch.cat([h_n[0], h_n[1]], dim=-1)  # B, entity_dim
        lstm_projected = self.lstm_proj(lstm_final)  # B, entity_dim
        
        return lstm_output, lstm_projected

    def _attn_pool(self, seq_h, mask):
        """注意力池化"""
        attn = torch.bmm(seq_h, seq_h.transpose(1, 2))  # B,L,L
        attn = attn.sum(-1)  # B,L
        attn = attn - (1 - mask) * 1e8
        weight = F.softmax(attn, dim=-1).unsqueeze(-1)  # B,L,1
        return (weight * seq_h).sum(1)  # B,H

    def encode_question(self, query_text, store=True):
        """并行编码：BERT和BiLSTM使用各自词表"""
        batch_size = query_text.size(0)
        
        # 检查输入类型，支持两种模式：
        # 1. 单个tensor：使用BERT tokenization，LSTM部分使用word_embedding
        # 2. 元组：(bert_tokens, lstm_tokens)：分别使用不同的tokenization
        
        if isinstance(query_text, tuple) and len(query_text) == 2:
            # 模式2：分别提供BERT和LSTM的tokenization
            bert_tokens, lstm_tokens = query_text
            bert_batch_size = bert_tokens.size(0)
            lstm_batch_size = lstm_tokens.size(0)
            
            if bert_batch_size != lstm_batch_size:
                raise ValueError(f"BERT和LSTM的batch size不匹配: {bert_batch_size} vs {lstm_batch_size}")
            
            # BERT编码（使用BERT词表）
            bert_output, bert_projected = self._encode_with_bert(bert_tokens)
            
            # BiLSTM编码（使用LSTM词表）
            lstm_output, lstm_projected = self._encode_with_lstm(lstm_tokens)
            
        else:
            # 模式1：使用统一的BERT tokenization
            # BERT编码（使用BERT词表）
            bert_output, bert_projected = self._encode_with_bert(query_text)
            
            # BiLSTM编码（使用LSTM词表）- 这里需要将BERT tokens转换为LSTM tokens
            # 由于没有直接的映射，我们使用word_embedding的默认处理
            lstm_output, lstm_projected = self._encode_with_lstm(query_text)
        
        # ----- 融合策略 -----
        # 句子级融合：拼接后通过融合层
        fused_node_emb = self.fusion_layer(torch.cat([bert_projected, lstm_projected], dim=-1))
        
        # Token级融合：BERT输出 + LSTM输出
        # 注意：这里需要处理长度不匹配的问题
        bert_len = bert_output.size(1)
        lstm_len = lstm_output.size(1)
        
        if bert_len != lstm_len:
            # 如果长度不匹配，使用较短的作为基准
            min_len = min(bert_len, lstm_len)
            bert_output = bert_output[:, :min_len, :]
            lstm_output = lstm_output[:, :min_len, :]
        
        # Token级融合
        token_fused = self.token_fusion(torch.cat([bert_output, lstm_output], dim=-1))
        
        if store:
            # 存储中间结果
            self.query_hidden_emb = token_fused  # B, L, entity_dim
            self.query_node_emb = fused_node_emb.unsqueeze(1)  # B, 1, entity_dim
            
            # 使用BERT的mask（因为BERT处理更准确）
            if isinstance(query_text, tuple):
                self.query_mask = (bert_tokens != self.bert_pad_val).float()
            else:
                self.query_mask = (query_text != self.bert_pad_val).float()
            
            return bert_output, self.query_node_emb
        else:
            return bert_output 

    def init_reason(self, q_input):
        """初始化推理过程，处理hybrid模式的两种tokenization"""
        if isinstance(q_input, tuple):
            # Hybrid模式：分别处理BERT和LSTM的tokenization
            bert_tokens, lstm_tokens = q_input
            self.encode_question((bert_tokens, lstm_tokens), store=True)
        else:
            # 其他模式：使用单一tokenization
            self.encode_question(q_input, store=True)

    def get_instruction(self, relational_ins, step=0, query_node_emb=None):
        """获取指令，与BaseInstruction基类保持一致"""
        # 使用存储的query_hidden_emb和query_node_emb
        if not hasattr(self, 'query_hidden_emb') or not hasattr(self, 'query_node_emb'):
            raise ValueError("必须先调用encode_question或init_reason")
        
        query_hidden_emb = self.query_hidden_emb
        query_mask = self.query_mask
        if query_node_emb is None:
            query_node_emb = self.query_node_emb
        
        relational_ins = relational_ins.unsqueeze(1)
        question_linear = getattr(self, 'question_linear' + str(step))
        q_i = question_linear(self.linear_drop(query_node_emb))
        cq = self.cq_linear(self.linear_drop(torch.cat((relational_ins, q_i, q_i-relational_ins, q_i*relational_ins), dim=-1)))
        # batch_size, 1, entity_dim
        ca = self.ca_linear(self.linear_drop(cq * query_hidden_emb))
        # batch_size, max_local_entity, 1
        attn_weight = F.softmax(ca + (1 - query_mask.unsqueeze(2)) * VERY_NEG_NUMBER, dim=1)
        # batch_size, max_local_entity, 1
        relational_ins = torch.sum(attn_weight * query_hidden_emb, dim=1)
        
        return relational_ins, attn_weight.squeeze(-1) 