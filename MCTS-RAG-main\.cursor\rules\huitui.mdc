---
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
## 代码健壮性和错误处理规范
严禁添加任何回退代码、兜底逻辑或者容错机制。当理想的代码实现可能存在风险或不确定性时，必须直接抛出明确的错误信息，而不是使用默认值、备用逻辑或者静默处理。这样做的目的是：
- 避免掩盖潜在问题，确保错误能够被及时发现和修复
- 防止训练过程中使用不正确的数据或逻辑，避免模型学习到错误的模式
- 确保系统行为的可预测性和一致性
- 所有异常都必须包含详细的上下文信息，包括错误发生的位置、参数值、期望值等

## 参数管理和代码复用规范
在添加任何新参数之前，必须执行以下完整的检查流程：
- 逐行阅读parsing.py文件的全部内容，理解现有参数的定义、用途和作用域
- 检查所有相关配置文件，确保新参数不与现有参数冲突或重复
- 如果发现功能相似或相同的参数，必须优先复用现有参数，不得重复定义
- 新参数的命名必须遵循现有的命名规范，保持一致性
- 参数添加后必须更新相关文档和注释，说明参数的用途、默认值、取值范围等
- 对于复杂参数，需要提供使用示例和最佳实践说明

## 问题分析和解决流程规范
对于每个开发任务或问题，必须严格按照以下标准化流程执行：
1. **需求分解阶段**：将复杂问题拆解为具体的、可操作的子任务，明确每个子任务的输入、输出和依赖关系
2. **技术方案设计**：基于需求分解结果，设计详细的技术实现方案，包括架构设计、算法选择、数据流程等
3. **风险评估**：识别潜在的技术风险、性能瓶颈和兼容性问题，制定相应的预防措施
4. **用户交互确认**：与用户充分沟通，确认需求理解的准确性和解决方案的可行性
5. **实施执行**：在确认方案后进行代码实现，确保实现过程中严格按照设计方案执行
6. **验证测试**：完成实现后进行功能验证和测试，确保解决方案的正确性和完整性

## 代码清理和维护规范  
在删除任何代码时，必须执行全面的依赖清理和影响分析：
- 使用代码搜索工具全面检查被删除代码的所有引用和依赖关系
- 删除所有相关的导入语句、配置项、测试用例和文档说明
- 检查并清理相关的数据结构、枚举值、常量定义等
- 确保删除操作不会影响其他模块的正常功能
- 删除后进行完整的回归测试，验证系统功能的完整性
- 更新相关的API文档、用户手册和开发文档
- 对于大规模的代码删除，需要制定详细的清理计划和回滚方案，确保操作的安全性和可逆性
- 只用改代码不用启动测试

## 运行环境测试
运行环境要优先确保使用rag环境进行后续的脚本运行，确保在MCTS-RAG-main/run_src下再执行以上目标

## 代码要求
你是一名顶尖高效的ai博士，目前做的研究希望在相关领域达到SOTA，请你在进行策略设计，代码编写，思路扩散时尽可能实现SOTA
-编写代码，严格按照提供的代码库来，确保行一级别的准确性，要求完全复现内部细节，并且禁止添加注释，任何对于代码的理解只能通过分析源码来得到信息，禁止含糊其辞，有任何疑问必须通过代码来分析。
-不引入额外脚本，生成代码后只给出运行命令。以及对应的含义。
-严格注意与后续的代码参数等各方面有严谨的适配