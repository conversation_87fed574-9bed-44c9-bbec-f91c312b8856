from utils import create_logger
import time
import numpy as np
import os, math

import torch
from torch.optim.lr_scheduler import ExponentialLR
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

# 设置 tqdm 的监控间隔
tqdm.monitor_iterval = 0

# 从自定义模块导入数据加载和模型定义
from dataset_load import load_data
from dataset_load_graft import load_data_graft
from models.ReaRev.rearev import ReaRev
from models.NSM.nsm import NSM
from models.GraftNet.graftnet import GraftNet
from evaluate import Evaluator

# 定义 Trainer_KBQA 类，用于训练 KBQA 模型
class Trainer_KBQA(object):
    # 初始化方法，设置参数和加载数据
    def __init__(self, args, model_name, logger=None):
        self.args = args
        self.logger = logger
        self.best_dev_performance = 0.0
        self.best_h1 = 0.0
        self.best_f1 = 0.0
        self.best_h1b = 0.0
        self.best_f1b = 0.0
        self.eps = args['eps']
        self.current_epoch = 0
        self.warmup_epoch = args['warmup_epoch']
        self.learning_rate = self.args['lr']
        self.test_batch_size = args['test_batch_size']
        self.device = torch.device('cuda' if args['use_cuda'] else 'cpu')
        self.reset_time = 0
        self.load_data(args, args['lm'])
        self.writer = SummaryWriter('runs/CWQ')

        # TensorBoard 相关设置
        self.use_tensorboard = args.get('use_tensorboard', True)
        if self.use_tensorboard:
            self.tensorboard_dir = os.path.join(
                args.get('tensorboard_dir', 'runs'),
                args.get('experiment_name', model_name + '_' + str(int(time.time())))
            )
            os.makedirs(self.tensorboard_dir, exist_ok=True)
            self.writer = SummaryWriter('runs/CWQ')
            logger.info(f"TensorBoard日志将保存到: 'runs/cwq'")



        if 'decay_rate' in args:
            self.decay_rate = args['decay_rate']
        else:
            self.decay_rate = 0.98

        # 根据模型名称初始化相应的模型
        if model_name == 'ReaRev':
            self.model = ReaRev(self.args, len(self.entity2id), self.num_kb_relation,
                                self.num_word)
        elif model_name == 'NSM':
            self.model = NSM(self.args, len(self.entity2id), self.num_kb_relation,
                             self.num_word)
        elif model_name == 'GraftNet':
            self.model = GraftNet(self.args, len(self.entity2id), self.num_kb_relation,
                                  self.num_word)
        # elif model_name == 'NuTrea':
        #     self.model = NuTrea(self.args,  len(self.entity2id), self.num_kb_relation,
        #                           self.num_word)

        # 如果使用关系词嵌入，则编码关系文本
        if args['relation_word_emb']:
            self.model.encode_rel_texts(self.rel_texts, self.rel_texts_inv)

        # 将模型移动到指定设备（CPU 或 GPU）
        self.model.to(self.device)
        # 初始化评估器
        self.evaluator = Evaluator(args=args, model=self.model, entity2id=self.entity2id,
                                   relation2id=self.relation2id, device=self.device)
        # 加载预训练模型
        self.load_pretrain()

        # 修改 __init__ 方法中的加载逻辑
        if args['resume_training'] and args['load_experiment']:
            self.load_ckpt(os.path.join(args['checkpoint_dir'], args['load_experiment']))
            self.start_epoch = self.current_epoch + 1  # 从断点后开始
        else:
            self.start_epoch = 0

        # 定义优化器和学习率调度器
        self.optim_def()

        # 设置模型的参数数量
        self.num_relation = self.num_kb_relation
        self.num_entity = len(self.entity2id)
        self.num_word = len(self.word2id)

        # 打印实体、关系和单词的数量
        print("Entity: {}, Relation: {}, Word: {}".format(self.num_entity, self.num_relation, self.num_word))

        # 设置模型参数维度和文件路径
        for k, v in args.items():
            if k.endswith('dim'):
                setattr(self, k, v)
            if k.endswith('emb_file') or k.endswith('kge_file'):
                if v is None:
                    setattr(self, k, None)
                else:
                    setattr(self, k, args['data_folder'] + v)

    # 定义优化器和学习率调度器
    def optim_def(self):
        # 获取需要训练的参数
        trainable = filter(lambda p: p.requires_grad, self.model.parameters())
        # 定义 Adam 优化器
        self.optim_model = optim.Adam(trainable, lr=self.learning_rate)
        # 如果衰减率大于 0，则定义指数学习率调度器
        if self.decay_rate > 0:
            self.scheduler = ExponentialLR(self.optim_model, self.decay_rate)

    # 加载数据集
    def load_data(self, args, tokenize):
        # 根据模型名称加载不同的数据集
        if args["model_name"] == "GraftNet":
            dataset = load_data_graft(args, tokenize)
        else:
            dataset = load_data(args, tokenize)
        # 设置训练、验证和测试数据集
        self.train_data = dataset["train"]
        self.valid_data = dataset["valid"]
        self.test_data = dataset["test"]
        # 设置实体、关系和单词的映射
        self.entity2id = dataset["entity2id"]
        self.relation2id = dataset["relation2id"]
        self.word2id = dataset["word2id"]
        self.num_word = dataset["num_word"]
        self.num_kb_relation = self.test_data.num_kb_relation
        self.num_entity = len(self.entity2id)
        # 设置关系文本
        self.rel_texts = dataset["rel_texts"]
        self.rel_texts_inv = dataset["rel_texts_inv"]

    # 加载预训练模型
    def load_pretrain(self):
        args = self.args
        # 如果指定了加载的实验，则加载相应的检查点
        if args['load_experiment'] is not None:
            ckpt_path = os.path.join(args['checkpoint_dir'], args['load_experiment'])
            print("Load ckpt from", ckpt_path)
            self.load_ckpt(ckpt_path)

    # 评估模型性能
    def evaluate(self, data, test_batch_size=20, write_info=False):
        eval_f1, eval_h1, eval_em = self.evaluator.evaluate(data, test_batch_size, write_info)

        return self.evaluator.evaluate(data, test_batch_size, write_info)

    # 训练模型
    # def train(self, start_epoch, end_epoch):
    #     eval_every = self.args['eval_every']
    #     print("Start Training------------------")
    #     global_step = 0
    #
    #     for epoch in range(self.start_epoch, end_epoch + 1):
    #         st = time.time()
    #         # 训练一个 epoch 并获取损失和评估指标
    #         loss, extras, h1_list_all, f1_list_all = self.train_epoch()
    #
    #         # ========== 修改部分开始 ==========
    #         # 计算指标平均值
    #         avg_h1 = np.mean(h1_list_all) if h1_list_all else 0.0
    #         avg_f1 = np.mean(f1_list_all) if f1_list_all else 0.0
    #
    #         self.writer.add_scalar('Loss/train', loss, epoch)
    #         # self.writer.add_scalar('Extras/train', extras, epoch)
    #         self.writer.add_scalar('H1/train', avg_h1, epoch)
    #         self.writer.add_scalar('F1/train', avg_f1, epoch)
    #
    #
    #         # 如果衰减率大于 0，则更新学习率
    #         if self.decay_rate > 0:
    #             self.scheduler.step()
    #
    #         # 记录训练日志
    #         self.logger.info("Epoch: {}, loss : {:.4f}, time: {}".format(epoch + 1, loss, time.time() - st))
    #         self.logger.info("Training h1 : {:.4f}, f1 : {:.4f}".format(np.mean(h1_list_all), np.mean(f1_list_all)))
    #
    #         # 每 eval_every 个 epoch 进行一次验证和测试
    #         if (epoch + 1) % eval_every == 0:
    #             eval_f1, eval_h1, eval_em = self.evaluate(self.valid_data, self.test_batch_size)
    #             self.logger.info("EVAL F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_h1, eval_em))
    #
    #             self.writer.add_scalar('Loss/eval', loss, epoch)
    #             self.writer.add_scalar('eval_em/eval', eval_em, epoch)
    #             self.writer.add_scalar('H1/eval', eval_h1, epoch)
    #             self.writer.add_scalar('F1/eval', eval_f1, epoch)
    #
    #             eval_f1, eval_h1, eval_em = self.evaluate(self.test_data, self.test_batch_size)
    #             self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_h1, eval_em))
    #             do_test = False
    #
    #             self.writer.add_scalar('Loss/test', loss, epoch)
    #             self.writer.add_scalar('eval_em/test', eval_em, epoch)
    #             self.writer.add_scalar('H1/test', eval_h1, epoch)
    #             self.writer.add_scalar('F1/test', eval_f1, epoch)
    #
    #             # 如果当前 epoch 大于 warmup_epoch，则保存最佳模型
    #             if epoch > self.warmup_epoch:
    #                 if eval_h1 > self.best_h1:
    #                     self.best_h1 = eval_h1
    #                     self.save_ckpt("h1")
    #                     self.logger.info("BEST EVAL H1: {:.4f}".format(eval_h1))
    #                     do_test = True
    #                 if eval_f1 > self.best_f1:
    #                     self.best_f1 = eval_f1
    #                     self.save_ckpt("f1")
    #                     self.logger.info("BEST EVAL F1: {:.4f}".format(eval_f1))
    #                     do_test = True
    #
    #     # 保存最终模型
    #     self.writer.close()
    #
    #     self.save_ckpt("final")
    #     self.logger.info('Train Done! Evaluate on testset with saved model')
    #     print("End Training------------------")
    #     # 在测试集上评估最佳模型
    #     self.evaluate_best()
    #
    # def train_epoch(self):
    #     # 设置模型为训练模式
    #     self.model.train()
    #     # 重置训练数据批次
    #     self.train_data.reset_batches(is_sequential=False)
    #     # 初始化损失列表
    #     losses = []
    #     actor_losses = []
    #     ent_losses = []
    #     # 计算 epoch 的迭代次数
    #     num_epoch = math.ceil(self.train_data.num_data / self.args['batch_size'])
    #     h1_list_all = []
    #     f1_list_all = []
    #     for iteration in tqdm(range(num_epoch)):
    #         # 获取当前批次的数据
    #         batch = self.train_data.get_batch(iteration, self.args['batch_size'], self.args['fact_drop'])
    #
    #         # 清零梯度
    #         self.optim_model.zero_grad()
    #         # 前向传播并计算损失
    #         loss, _, _, tp_list = self.model(batch, training=True)
    #         h1_list, f1_list = tp_list
    #         h1_list_all.extend(h1_list)
    #         f1_list_all.extend(f1_list)
    #         # 反向传播
    #         loss.backward()
    #         # 梯度裁剪
    #         torch.nn.utils.clip_grad_norm_([param for name, param in self.model.named_parameters()],
    #                                        self.args['gradient_clip'])
    #         # 更新参数
    #         self.optim_model.step()
    #         # 记录损失
    #         losses.append(loss.item())
    #     extras = [0, 0]
    #     return np.mean(losses), extras, h1_list_all, f1_list_all

    # 在测试集上评估最佳模型

    def train(self, start_epoch, end_epoch):
        # 修改：每个epoch都进行评估
        eval_every = 1  # 强制设置为1，每个epoch都评估
        print("Start Training------------------")
        print(f"✅ 评估频率设置为每个epoch一次")
        global_step = 0
        
        # 新增：跟踪最佳验证结果对应的测试结果
        self.best_val_test_h1 = 0.0  # 最佳H1验证结果对应的测试H1
        self.best_val_test_f1 = 0.0  # 最佳F1验证结果对应的测试F1
        self.best_val_test_em = 0.0  # 最佳验证结果对应的测试EM
        self.best_val_epoch_h1 = 0   # 最佳H1验证结果的epoch
        self.best_val_epoch_f1 = 0   # 最佳F1验证结果的epoch

        # 使用 self.start_epoch 而不是 start_epoch
        for epoch in range(self.start_epoch, end_epoch + 1):
            self.current_epoch = epoch  # 更新当前 epoch
            st = time.time()

            # 训练一个 epoch 并获取损失和评估指标
            loss, extras, h1_list_all, f1_list_all = self.train_epoch(global_step)
            global_step += len(h1_list_all) if h1_list_all else 0  # 更新全局步数

            # 计算指标平均值并处理空列表情况
            avg_h1 = np.mean(h1_list_all) if h1_list_all else 0.0
            avg_f1 = np.mean(f1_list_all) if f1_list_all else 0.0

            # 记录训练指标到 TensorBoard
            self.writer.add_scalar('Loss/train', loss, epoch)
            self.writer.add_scalar('H1/train', avg_h1, epoch)
            self.writer.add_scalar('F1/train', avg_f1, epoch)

            # 处理extras（可能是列表或标量）
            if isinstance(extras, (list, tuple)):
                for i, extra in enumerate(extras):
                    if isinstance(extra, (int, float, np.number)):
                        self.writer.add_scalar(f'Extra/train_{i}', extra, epoch)

            # 如果衰减率大于 0，则更新学习率
            if self.decay_rate > 0:
                self.scheduler.step()

            # 记录训练日志
            self.logger.info("Epoch: {}, loss : {:.4f}, time: {}".format(epoch + 1, loss, time.time() - st))
            self.logger.info("Training h1 : {:.4f}, f1 : {:.4f}".format(avg_h1, avg_f1))

            # 保存最新检查点以便恢复训练
            self.save_ckpt("latest")

            # 修改：每个epoch都进行验证和测试
                # 验证集评估
            val_f1, val_h1, val_em = self.evaluate(self.valid_data, self.test_batch_size, write_info=False)
            self.logger.info("EVAL F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(val_f1, val_h1, val_em))

                # 记录验证集指标
            self.writer.add_scalar('Loss/eval', loss, epoch)
            self.writer.add_scalar('EM/eval', val_em, epoch)
            self.writer.add_scalar('H1/eval', val_h1, epoch)
            self.writer.add_scalar('F1/eval', val_f1, epoch)

                # 测试集评估
            test_f1, test_h1, test_em = self.evaluate(self.test_data, self.test_batch_size, write_info=False)
            self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(test_f1, test_h1, test_em))

                # 记录测试集指标
            self.writer.add_scalar('Loss/test', loss, epoch)
            self.writer.add_scalar('EM/test', test_em, epoch)
            self.writer.add_scalar('H1/test', test_h1, epoch)
            self.writer.add_scalar('F1/test', test_f1, epoch)

            # 修改：跟踪最佳验证结果及对应的测试结果
            if epoch > self.warmup_epoch:
                # 更新最佳H1
                if val_h1 > self.best_h1:
                    self.best_h1 = val_h1
                    self.best_val_test_h1 = test_h1  # 记录对应的测试结果
                    self.best_val_test_em = test_em   # 记录对应的测试EM
                    self.best_val_epoch_h1 = epoch + 1
                    self.save_ckpt("h1")
                    self.logger.info("🏆 NEW BEST H1: Val={:.4f}, Test={:.4f} at Epoch {}".format(val_h1, test_h1, epoch + 1))
                
                # 更新最佳F1
                if val_f1 > self.best_f1:
                    self.best_f1 = val_f1
                    self.best_val_test_f1 = test_f1  # 记录对应的测试结果
                    self.best_val_epoch_f1 = epoch + 1
                    self.save_ckpt("f1")
                    self.logger.info("🏆 NEW BEST F1: Val={:.4f}, Test={:.4f} at Epoch {}".format(val_f1, test_f1, epoch + 1))

        # 训练结束后的总结报告
        print("\n" + "="*80)
        print("🎉 训练完成！最终结果总结:")
        print("="*80)
        
        print(f"📊 最佳验证H1: {self.best_h1:.4f} (Epoch {self.best_val_epoch_h1})")
        print(f"   对应测试H1: {self.best_val_test_h1:.4f}")
        print(f"   对应测试EM: {self.best_val_test_em:.4f}")
        
        print(f"📊 最佳验证F1: {self.best_f1:.4f} (Epoch {self.best_val_epoch_f1})")
        print(f"   对应测试F1: {self.best_val_test_f1:.4f}")
        
        # 记录到日志
        self.logger.info("="*50 + " 训练完成总结 " + "="*50)
        self.logger.info(f"最佳验证H1: {self.best_h1:.4f} (Epoch {self.best_val_epoch_h1}) -> 测试H1: {self.best_val_test_h1:.4f}, 测试EM: {self.best_val_test_em:.4f}")
        self.logger.info(f"最佳验证F1: {self.best_f1:.4f} (Epoch {self.best_val_epoch_f1}) -> 测试F1: {self.best_val_test_f1:.4f}")
        
        # AMHR统计总结（如果启用）
        if hasattr(self.model, 'get_amhr_summary'):
            try:
                final_amhr_summary = self.model.get_amhr_summary()
                if final_amhr_summary.get('amhr_enabled', False):
                    global_stats = final_amhr_summary.get('global_stats', {})
                    print(f"\n🧠 AMHR反思机制总结:")
                    print(f"   总体质量: {global_stats.get('overall_quality', '未知')}")
                    print(f"   微观反思: {global_stats.get('total_micro_count', 0)} 次")
                    print(f"   宏观反思: {global_stats.get('total_macro_count', 0)} 次")
                    print(f"   平衡比例: {global_stats.get('avg_ratio', 0):.3f}")
                    self.logger.info(f"AMHR最终统计: {final_amhr_summary}")
            except Exception as e:
                self.logger.warning(f"获取AMHR统计失败: {e}")
        
        print("="*80)

        # 训练结束，保存最终模型
        self.writer.close()
        self.save_ckpt("final")
        self.logger.info('训练完成！最佳模型已保存')
        print("训练完成！检查点已保存到:", self.args['checkpoint_dir'])

    def train_epoch(self, global_step=0):
        # 设置模型为训练模式
        self.model.train()
        
        # 重置AMHR统计（如果启用）
        if hasattr(self.model, 'reset_amhr_stats') and hasattr(self.args, 'enable_amhr') and self.args.get('enable_amhr', False):
            self.model.reset_amhr_stats()
        
        # 重置训练数据批次
        self.train_data.reset_batches(is_sequential=False)
        # 初始化损失列表
        losses = []
        reflection_losses = []  # 新增：反思损失列表
        actor_losses = []
        ent_losses = []
        # 计算 epoch 的迭代次数
        num_epoch = math.ceil(self.train_data.num_data / self.args['batch_size'])
        h1_list_all = []
        f1_list_all = []

        for iteration in tqdm(range(num_epoch)):
            # 获取当前批次的数据
            batch = self.train_data.get_batch(iteration, self.args['batch_size'], self.args['fact_drop'])

            # 清零梯度
            self.optim_model.zero_grad()
            
            # 前向传播并计算损失
            loss, _, _, tp_list = self.model(batch, training=True)
            
            # AMHR反思损失计算
            reflection_loss = 0.0
            if hasattr(self.model, 'enable_amhr') and self.model.enable_amhr:
                reflection_loss = self._compute_reflection_loss()
                reflection_loss_weight = self.args.get('amhr_reflection_loss_weight', 0.1)
                loss = loss + reflection_loss_weight * reflection_loss
                reflection_losses.append(reflection_loss.item())

            # 处理返回的指标
            if tp_list is not None and len(tp_list) == 2:
                h1_list, f1_list = tp_list

                # 确保列表中的值都是有效的数值
                h1_list = [h for h in h1_list if isinstance(h, (int, float, np.number))]
                f1_list = [f for f in f1_list if isinstance(f, (int, float, np.number))]

                h1_list_all.extend(h1_list)
                f1_list_all.extend(f1_list)

                # 记录批次级别的指标
                batch_h1 = np.mean(h1_list) if h1_list else 0.0
                batch_f1 = np.mean(f1_list) if f1_list else 0.0

                current_step = global_step + iteration
                self.writer.add_scalar('H1/batch', batch_h1, current_step)
                self.writer.add_scalar('F1/batch', batch_f1, current_step)
                
                # 记录AMHR反思损失
                if reflection_losses:
                    self.writer.add_scalar('ReflectionLoss/batch', reflection_loss.item(), current_step)

            # 反向传播
            loss.backward()
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                [param for name, param in self.model.named_parameters()],
                self.args['gradient_clip']
            )
            # 更新参数
            self.optim_model.step()
            # 记录损失
            losses.append(loss.item())

        avg_loss = np.mean(losses) if losses else 0.0
        avg_reflection_loss = np.mean(reflection_losses) if reflection_losses else 0.0
        
        # 记录AMHR统计信息到TensorBoard
        self._log_amhr_stats(self.current_epoch)
        
        # 扩展extras以包含反思损失
        extras = [avg_reflection_loss, 0.0]  # [reflection_loss, reserved]

        return avg_loss, extras, h1_list_all, f1_list_all

    def _compute_reflection_loss(self):
        """
        计算AMHR反思损失
        这是一个正则化损失，鼓励查询更新的一致性和稳定性
        """
        reflection_loss = torch.tensor(0.0, device=self.device)
        
        if not hasattr(self.model, 'enable_amhr') or not self.model.enable_amhr:
            return reflection_loss
        
        # 检查模型是否有QueryReform模块
        if hasattr(self.model, 'num_ins'):
            consistency_penalties = []
            
            for i in range(self.model.num_ins):
                reform = getattr(self.model, 'reform' + str(i), None)
                if reform is not None and hasattr(reform, 'distill_weight'):
                    # 跨层蒸馏权重的正则化：鼓励平衡的知识转移
                    distill_penalty = torch.abs(reform.distill_weight - 0.5)
                    consistency_penalties.append(distill_penalty)
            
            if consistency_penalties:
                reflection_loss = torch.mean(torch.stack(consistency_penalties))
        
        return reflection_loss

    def _log_amhr_stats(self, epoch):
        """
        记录AMHR统计信息到TensorBoard和日志
        """
        if not hasattr(self.model, 'get_amhr_summary'):
            return
        
        try:
            amhr_summary = self.model.get_amhr_summary()
            
            if amhr_summary.get('amhr_enabled', False):
                # 记录全局统计
                global_stats = amhr_summary.get('global_stats', {})
                if global_stats:
                    self.writer.add_scalar('AMHR/total_micro_count', global_stats.get('total_micro_count', 0), epoch)
                    self.writer.add_scalar('AMHR/total_macro_count', global_stats.get('total_macro_count', 0), epoch)
                    self.writer.add_scalar('AMHR/avg_ratio', global_stats.get('avg_ratio', 0), epoch)
                
                # 记录每个reform模块的统计
                reforms_stats = amhr_summary.get('reforms', {})
                for reform_name, stats in reforms_stats.items():
                    self.writer.add_scalar(f'AMHR/{reform_name}/micro_ema', stats.get('micro_ema', 0), epoch)
                    self.writer.add_scalar(f'AMHR/{reform_name}/macro_ema', stats.get('macro_ema', 0), epoch)
                    self.writer.add_scalar(f'AMHR/{reform_name}/ratio', stats.get('ratio', 0), epoch)
                
                # 记录到日志
                if epoch % 10 == 0:  # 每10个epoch记录一次详细信息
                    self.logger.info(f"AMHR统计 [Epoch {epoch}]: {global_stats.get('overall_quality', '未知')}")
                    self.logger.info(f"微观/宏观比例: {global_stats.get('avg_ratio', 0):.3f}")
                    
        except Exception as e:
            self.logger.warning(f"记录AMHR统计时出错: {e}")

    def evaluate_best(self):
        # 加载最佳 h1 模型并评估
        filename = os.path.join(self.args['checkpoint_dir'], "{}-h1.ckpt".format(self.args['experiment_name']))
        self.load_ckpt(filename)
        eval_f1, eval_h1, eval_em = self.evaluate(self.test_data, self.test_batch_size, write_info=False)
        self.logger.info("Best h1 evaluation")
        self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_h1, eval_em))

        # 加载最佳 f1 模型并评估
        filename = os.path.join(self.args['checkpoint_dir'], "{}-f1.ckpt".format(self.args['experiment_name']))
        self.load_ckpt(filename)
        eval_f1, eval_h1, eval_em = self.evaluate(self.test_data, self.test_batch_size, write_info=False)
        self.logger.info("Best f1 evaluation")
        self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_h1, eval_em))

        # 加载最终模型并评估
        filename = os.path.join(self.args['checkpoint_dir'], "{}-final.ckpt".format(self.args['experiment_name']))
        self.load_ckpt(filename)
        eval_f1, eval_h1, eval_em = self.evaluate(self.test_data, self.test_batch_size, write_info=False)
        self.logger.info("Final evaluation")
        self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_h1, eval_em))

    # 单独评估模型
    def evaluate_single(self, filename):
        # 如果指定了文件名，则加载相应的检查点
        if filename is not None:
            self.load_ckpt(filename)
        # 在验证集上评估
        eval_f1, eval_hits, eval_ems = self.evaluate(self.valid_data, self.test_batch_size, write_info=False)
        self.logger.info("EVAL F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(eval_f1, eval_hits, eval_ems))
        # 在测试集上评估并写入信息
        test_f1, test_hits, test_ems = self.evaluate(self.test_data, self.test_batch_size, write_info=True)
        self.logger.info("TEST F1: {:.4f}, H1: {:.4f}, EM {:.4f}".format(test_f1, test_hits, test_ems))

    # 训练一个 epoch

    # 保存模型检查点
    def save_ckpt(self, reason="h1"):
        model = self.model
        # 创建检查点字典
        # checkpoint = {
        #     'model_state_dict': model.state_dict()
        # }
        checkpoint = {
            'epoch': self.current_epoch,  # 新增当前epoch记录
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optim_model.state_dict(),  # 新增优化器状态
            'scheduler_state_dict': self.scheduler.state_dict() if self.decay_rate > 0 else None,  # 新增学习率调度器
            'best_h1': self.best_h1,  # 保存当前最佳指标
            'best_f1': self.best_f1
        }
        # 保存检查点文件
        model_name = os.path.join(self.args['checkpoint_dir'], "{}-{}.ckpt".format(self.args['experiment_name'],
                                                                                   reason))
        torch.save(checkpoint, model_name)
        print("Best %s, save model as %s" % (reason, model_name))

    # 加载模型检查点
    def load_ckpt(self, filename):
        # 加载检查点文件
        checkpoint = torch.load(filename)
        self.model.load_state_dict(checkpoint["model_state_dict"], strict=False)

        # 新增优化器状态加载
        if 'optimizer_state_dict' in checkpoint:
            self.optim_model.load_state_dict(checkpoint['optimizer_state_dict'])

        # 新增学习率调度器加载
        if self.decay_rate > 0 and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 恢复训练状态
        self.current_epoch = checkpoint.get('epoch', 0)
        self.best_h1 = checkpoint.get('best_h1', 0)
        self.best_f1 = checkpoint.get('best_f1', 0)
