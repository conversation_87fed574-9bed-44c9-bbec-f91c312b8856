"""
GraphMAE风格的掩码策略模块
基于GraphMAE/edcoder.py的精确实现

核心特性:
1. 多种掩码策略: token替换、噪声替换、边dropout
2. SCE损失函数 (Scaled Cosine Error)
3. 可学习掩码token
4. 动态掩码比例调整
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any, Union, List
from functools import partial
import logging
import math

logger = logging.getLogger(__name__)


def sce_loss(x: torch.Tensor, y: torch.Tensor, alpha: float = 3) -> torch.Tensor:
    """
    SCE (Scaled Cosine Error) 损失函数
    基于GraphMAE/loss_func.py的实现
    
    Args:
        x: 预测特征 [N, D]
        y: 目标特征 [N, D]  
        alpha: 缩放指数
    
    Returns:
        SCE损失值
    """
    x = F.normalize(x, p=2, dim=-1)
    y = F.normalize(y, p=2, dim=-1)
    
    # loss = (1 - cos_sim)^alpha
    loss = (1 - (x * y).sum(dim=-1)).pow_(alpha)
    return loss.mean()


def sig_loss(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
    """
    Sigmoid损失函数
    基于GraphMAE/loss_func.py的实现
    """
    x = F.normalize(x, p=2, dim=-1)
    y = F.normalize(y, p=2, dim=-1)
    
    loss = (x * y).sum(1)
    loss = torch.sigmoid(-loss)
    return loss.mean()


class MaskingStrategy(nn.Module):
    """
    GraphMAE风格的掩码策略
    基于GraphMAE/edcoder.py的encoding_mask_noise方法
    """
    
    def __init__(self,
                 embedding_dim: int,
                 mask_rate: float = 0.3,
                 replace_rate: float = 0.1,
                 drop_edge_rate: float = 0.0,
                 mask_token_rate: float = 0.9,
                 loss_fn: str = "sce",
                 alpha_l: float = 3,
                 concat_hidden: bool = False,
                 device: Optional[torch.device] = None):
        super().__init__()
        
        # 智能设备检测
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.embedding_dim = embedding_dim
        self.mask_rate = mask_rate
        self.replace_rate = replace_rate
        self.drop_edge_rate = drop_edge_rate
        self.mask_token_rate = mask_token_rate  # 使用配置传入的值
        self.loss_fn = loss_fn
        self.alpha_l = alpha_l
        self.concat_hidden = concat_hidden
        self.device = device
        
        # 可学习的掩码token (GraphMAE核心创新)
        self.mask_token = nn.Parameter(torch.zeros(1, embedding_dim))
        nn.init.normal_(self.mask_token, std=0.02)
        
        # 设置损失函数
        self.criterion = self._setup_loss_fn(loss_fn, alpha_l)
        
        logger.info(f"掩码策略初始化: mask_rate={mask_rate}, replace_rate={replace_rate}, loss_fn={loss_fn}")
    
    def _setup_loss_fn(self, loss_fn: str, alpha_l: float):
        """
        设置损失函数
        基于GraphMAE/edcoder.py的setup_loss_fn方法
        """
        if loss_fn == "mse":
            criterion = nn.MSELoss()
        elif loss_fn == "sce":
            criterion = partial(sce_loss, alpha=alpha_l)
        elif loss_fn == "sig":
            criterion = sig_loss
        else:
            raise NotImplementedError(f"未支持的损失函数: {loss_fn}")
        return criterion
    
    def apply_node_masking(self, 
                          node_features: torch.Tensor,
                          num_nodes: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        应用节点掩码
        基于GraphMAE/edcoder.py的encoding_mask_noise方法 (L196-223)
        
        Args:
            node_features: 节点特征 [num_nodes, embedding_dim]
            num_nodes: 节点数量
        
        Returns:
            masked_features: 掩码后的特征
            mask_nodes: 被掩码的节点索引
            keep_nodes: 保留的节点索引
        """
        # 随机排列节点索引
        perm = torch.randperm(num_nodes, device=node_features.device)
        num_mask_nodes = int(self.mask_rate * num_nodes)
        
        # 选择掩码和保留的节点
        mask_nodes = perm[:num_mask_nodes]
        keep_nodes = perm[num_mask_nodes:]
        
        # 克隆原始特征
        masked_features = node_features.clone()
        
        if self.replace_rate > 0:
            # 混合掩码策略: token替换 + 噪声替换
            num_noise_nodes = int(self.replace_rate * num_mask_nodes)
            perm_mask = torch.randperm(num_mask_nodes, device=node_features.device)
            
            # token节点: 用mask token替换
            token_nodes = mask_nodes[perm_mask[:int(self.mask_token_rate * num_mask_nodes)]]
            # 噪声节点: 用随机特征替换  
            noise_nodes = mask_nodes[perm_mask[-num_noise_nodes:]]
            noise_to_be_chosen = torch.randperm(num_nodes, device=node_features.device)[:num_noise_nodes]
            
            # 应用掩码
            masked_features[token_nodes] = 0.0
            masked_features[noise_nodes] = node_features[noise_to_be_chosen]
        else:
            # 简单掩码策略: 只用mask token
            token_nodes = mask_nodes
            masked_features[mask_nodes] = 0.0
        
        # 添加可学习的mask token
        masked_features[token_nodes] += self.mask_token
        
        return masked_features, mask_nodes, keep_nodes
    
    def apply_triple_masking(self,
                           head_ids: torch.Tensor,
                           relation_ids: torch.Tensor, 
                           tail_ids: torch.Tensor,
                           num_entities: int,
                           num_relations: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, Dict]:
        """
        应用三元组掩码 (针对KG的特殊掩码策略)
        
        Args:
            head_ids: 头实体ID [batch_size]
            relation_ids: 关系ID [batch_size]
            tail_ids: 尾实体ID [batch_size]
            num_entities: 实体总数
            num_relations: 关系总数
        
        Returns:
            masked_heads, masked_relations, masked_tails, mask_info
        """
        batch_size = head_ids.size(0)
        
        # 为每个位置独立生成掩码
        head_mask = torch.rand(batch_size, device=head_ids.device) < self.mask_rate
        relation_mask = torch.rand(batch_size, device=head_ids.device) < self.mask_rate
        tail_mask = torch.rand(batch_size, device=head_ids.device) < self.mask_rate
        
        # 不修改原始ID，保持原始值用于embedding查找
        # 掩码信息将在get_complex_embeddings中处理
        mask_info = {
            'head_mask': head_mask,
            'relation_mask': relation_mask,
            'tail_mask': tail_mask,
            'entity_mask_id': num_entities,  # 使用实体数量作为mask token ID
            'relation_mask_id': num_relations  # 使用关系数量作为mask token ID
        }
        
        return head_ids, relation_ids, tail_ids, mask_info
    
    def compute_reconstruction_loss(self,
                                  reconstructed: torch.Tensor,
                                  original: torch.Tensor,
                                  mask_indices: torch.Tensor) -> torch.Tensor:
        """
        计算重建损失 (只对被掩码的部分)
        基于GraphMAE/edcoder.py的mask_attr_prediction方法 (L253-260)
        
        Args:
            reconstructed: 重建的特征 [num_nodes, embedding_dim]
            original: 原始特征 [num_nodes, embedding_dim]
            mask_indices: 被掩码的节点索引
        
        Returns:
            重建损失
        """
        if len(mask_indices) == 0:
            return torch.tensor(0.0, device=reconstructed.device)
        
        # 只计算被掩码节点的重建损失
        recon_masked = reconstructed[mask_indices]
        orig_masked = original[mask_indices]
        
        loss = self.criterion(recon_masked, orig_masked)
        return loss
    
    def drop_edges(self, 
                  edge_index: torch.Tensor,
                  edge_attr: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        边dropout
        基于GraphMAE的drop_edge机制
        
        Args:
            edge_index: 边索引 [2, num_edges]
            edge_attr: 边属性 [num_edges, edge_dim]
        
        Returns:
            dropped_edge_index, dropped_edge_attr
        """
        if self.drop_edge_rate <= 0:
            return edge_index, edge_attr
        
        num_edges = edge_index.size(1)
        keep_prob = 1 - self.drop_edge_rate
        keep_mask = torch.rand(num_edges, device=edge_index.device) < keep_prob
        
        dropped_edge_index = edge_index[:, keep_mask]
        dropped_edge_attr = edge_attr[keep_mask] if edge_attr is not None else None
        
        return dropped_edge_index, dropped_edge_attr


class MaskDecoder(nn.Module):
    """
    掩码重建解码器
    基于GraphMAE/edcoder.py的decoder设计
    """
    
    def __init__(self,
                 input_dim: int,
                 hidden_dim: int,
                 output_dim: int,
                 num_layers: int = 1,
                 dropout: float = 0.1,
                 activation: str = "relu",
                 concat_hidden: bool = True,
                 transformer_layers: int = 4):
        super().__init__()
        
        self.num_layers = num_layers
        self.concat_hidden = concat_hidden
        
        if num_layers == 1:
            self.decoder = nn.Linear(input_dim, output_dim)
        else:
            layers = []
            dims = [input_dim] + [hidden_dim] * (num_layers - 1) + [output_dim]
            
            for i in range(num_layers):
                layers.append(nn.Linear(dims[i], dims[i + 1]))
                if i < num_layers - 1:  # 不在最后一层添加激活和dropout
                    if activation == "relu":
                        layers.append(nn.ReLU())
                    elif activation == "gelu":
                        layers.append(nn.GELU())
                    elif activation == "prelu":
                        layers.append(nn.PReLU())
                    
                    if dropout > 0:
                        layers.append(nn.Dropout(dropout))
            
            self.decoder = nn.Sequential(*layers)
        
        # GraphMAE concat_hidden功能：编码器到解码器的映射层
        # 基于GraphMAE/edcoder.py L174-177
        if concat_hidden:
            # 如果连接所有隐藏层，输入维度需要乘以层数
            concat_input_dim = input_dim * transformer_layers
            self.encoder_to_decoder = nn.Linear(concat_input_dim, input_dim, bias=False)
        else:
            self.encoder_to_decoder = nn.Linear(input_dim, input_dim, bias=False)
        
        logger.info(f"掩码解码器初始化: {num_layers}层, {input_dim}->{output_dim}")
    
    def forward(self, 
               encoded_features: torch.Tensor,
               mask_indices: Optional[torch.Tensor] = None,
               all_hidden_states: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        """
        解码器前向传播
        
        Args:
            encoded_features: 编码后的特征 [num_nodes, input_dim] 或最后一层特征
            mask_indices: 被掩码的节点索引 (用于re-masking)  
            all_hidden_states: 所有隐藏层状态列表 (用于concat_hidden)
        
        Returns:
            重建的特征 [num_nodes, output_dim]
        """
        # GraphMAE concat_hidden功能
        # 基于GraphMAE/edcoder.py L241-243
        if self.concat_hidden and all_hidden_states is not None:
            # 连接所有隐藏层: torch.cat(all_hidden, dim=1)
            concat_features = torch.cat(all_hidden_states, dim=1)
            rep = self.encoder_to_decoder(concat_features)
        else:
            # 使用最后一层特征
            rep = self.encoder_to_decoder(encoded_features)
        
        # re-masking: 对被掩码的节点重新置零 (GraphMAE策略)
        # 基于GraphMAE/edcoder.py L247-248
        if mask_indices is not None:
            rep[mask_indices] = 0
        
        # 通过解码器重建
        reconstructed = self.decoder(rep)
        
        return reconstructed


class AdaptiveMaskingStrategy(MaskingStrategy):
    """
    自适应掩码策略
    根据训练进度动态调整掩码比例
    """
    
    def __init__(self, 
                 embedding_dim: int,
                 initial_mask_rate: float = 0.3,
                 final_mask_rate: float = 0.5,
                 **kwargs):
        super().__init__(embedding_dim, mask_rate=initial_mask_rate, **kwargs)
        
        self.initial_mask_rate = initial_mask_rate
        self.final_mask_rate = final_mask_rate
        self.current_epoch = 0
        self.total_epochs = 1
    
    def update_mask_rate(self, current_epoch: int, total_epochs: int):
        """更新掩码比例"""
        self.current_epoch = current_epoch
        self.total_epochs = total_epochs
        
        # 线性插值
        progress = current_epoch / max(total_epochs, 1)
        self.mask_rate = self.initial_mask_rate + (self.final_mask_rate - self.initial_mask_rate) * progress
        
        logger.debug(f"掩码比例更新: epoch={current_epoch}, mask_rate={self.mask_rate:.3f}")


class MultiScaleMaskingStrategy(MaskingStrategy):
    """
    多尺度掩码策略
    同时应用不同尺度的掩码
    """
    
    def __init__(self,
                 embedding_dim: int,
                 mask_rates: Tuple[float, ...] = (0.1, 0.3, 0.5),
                 **kwargs):
        super().__init__(embedding_dim, mask_rate=mask_rates[0], **kwargs)
        self.mask_rates = mask_rates
    
    def apply_multiscale_masking(self, 
                               node_features: torch.Tensor,
                               num_nodes: int) -> Dict[str, Tuple[torch.Tensor, torch.Tensor, torch.Tensor]]:
        """应用多尺度掩码"""
        results = {}
        
        for i, mask_rate in enumerate(self.mask_rates):
            original_mask_rate = self.mask_rate
            self.mask_rate = mask_rate
            
            masked_features, mask_nodes, keep_nodes = self.apply_node_masking(node_features, num_nodes)
            results[f'scale_{i}'] = (masked_features, mask_nodes, keep_nodes)
            
            self.mask_rate = original_mask_rate
        
        return results 