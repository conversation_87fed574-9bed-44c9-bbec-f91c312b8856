# coding=utf-8
"""
ComplEx Knowledge Graph Embedding Implementation
Extended to CCMGT: Complex Contrastive Masked Graph Transformer

Integrates:
- ComplEx: Complex-valued embeddings for asymmetric relations
- SimKGC: Contrastive learning with InfoNCE loss
- GraphMAE: Masked autoencoder for graph structure learning  
- KGTransformer: Transformer-based sequential KG modeling
"""

import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, Dict, Any
from .base import BaseKGEEmbedding

logger = logging.getLogger(__name__)


class TransformerEncoderLayer(nn.Module):
    """Transformer Encoder Layer for KG sequence modeling"""
    
    def __init__(self, d_model, nhead, dim_feedforward=2048, dropout=0.1):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
    def forward(self, src, src_mask=None):
        # Self-attention
        src2 = self.self_attn(src, src, src, attn_mask=src_mask)[0]
        src = src + self.dropout1(src2)
        src = self.norm1(src)
        
        # Feed forward
        src2 = self.linear2(self.dropout(F.relu(self.linear1(src))))
        src = src + self.dropout2(src2)
        src = self.norm2(src)
        
        return src


class ComplExEmbedding(BaseKGEEmbedding):
    """
    CCMGT: Complex Contrastive Masked Graph Transformer
    
    Extends ComplX with:
    1. Transformer encoder for sequential KG modeling
    2. Masked autoencoder for structure learning
    3. Contrastive learning for robust representations
    4. Multi-task learning framework
    """
    
    def __init__(self,
                 num_entities: int,
                 num_relations: int,
                 hidden_dim: int = 50,
                 device: Optional[torch.device] = None,
                 init_scale: float = 1e-3,
                 # 🔥 新增CCMGT参数
                 enable_ccmgt: bool = True,
                 transformer_layers: int = 2,
                 transformer_heads: int = 4,
                 contrastive_dim: int = 128,
                 mask_ratio: float = 0.15,
                 temperature: float = 0.07):
        """
        初始化CCMGT模型
        
        Args:
            enable_ccmgt (bool): 是否启用CCMGT功能
            transformer_layers (int): Transformer层数
            transformer_heads (int): 注意力头数
            contrastive_dim (int): 对比学习投影维度
            mask_ratio (float): 掩码比例
            temperature (float): 对比学习温度
        """
        # 智能设备检测
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # ComplX的最终嵌入维度是hidden_dim的2倍（实部+虚部）
        super().__init__(num_entities, num_relations, hidden_dim * 2, device)
        
        self.hidden_dim = hidden_dim
        self.init_scale = init_scale
        self.enable_ccmgt = enable_ccmgt
        self.mask_ratio = mask_ratio
        self.temperature = temperature
        
        # 初始化基础ComplX嵌入
        self._initialize_embeddings()
        
        if self.enable_ccmgt:
            self._initialize_ccmgt_components(transformer_layers, transformer_heads, contrastive_dim)
        
        logger.info(f"✅ CCMGT模型初始化完成: hidden_dim={hidden_dim}, final_dim={self.embedding_dim}, ccmgt_enabled={enable_ccmgt}")
    
    def _initialize_embeddings(self) -> None:
        """初始化实体和关系嵌入矩阵"""
        # 实体嵌入: [num_entities, hidden_dim * 2] (实部 + 虚部)
        self.entity_embeddings = nn.Embedding(self.num_entities + 1, self.embedding_dim)  # +1 for mask token
        nn.init.uniform_(self.entity_embeddings.weight, -self.init_scale, self.init_scale)
        
        # 关系嵌入: [num_relations, hidden_dim * 2] (实部 + 虚部) 
        self.relation_embeddings = nn.Embedding(self.num_relations + 1, self.embedding_dim)  # +1 for mask token
        nn.init.uniform_(self.relation_embeddings.weight, -self.init_scale, self.init_scale)
        
        # 🔥 特殊token索引
        self.entity_mask_token_id = self.num_entities
        self.relation_mask_token_id = self.num_relations
        
        logger.debug(f"嵌入矩阵初始化: 实体{self.entity_embeddings.weight.shape}, "
                    f"关系{self.relation_embeddings.weight.shape}")
    
    def _initialize_ccmgt_components(self, transformer_layers: int, transformer_heads: int, contrastive_dim: int):
        """初始化CCMGT组件"""
        # 🔥 Transformer编码器 (KGTransformer inspired)
        self.transformer_layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=self.embedding_dim,
                nhead=transformer_heads,
                dim_feedforward=self.embedding_dim * 4,
                dropout=0.1
            ) for _ in range(transformer_layers)
        ])
        
        # 🔥 掩码重建解码器 (GraphMAE inspired)
        self.mask_decoder = nn.Sequential(
            nn.Linear(self.embedding_dim, self.embedding_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.embedding_dim, self.embedding_dim)
        )
        
        # 🔥 对比学习投影头 (SimKGC inspired)
        self.contrastive_projector = nn.Sequential(
            nn.Linear(self.embedding_dim, self.embedding_dim),
            nn.ReLU(),
            nn.Linear(self.embedding_dim, contrastive_dim)
        )
        
        # 🔥 可学习温度参数 (SimKGC style)
        self.log_temperature = nn.Parameter(torch.tensor(math.log(self.temperature)), requires_grad=True)
        
        # 🔥 序列位置编码 (KGTransformer style)
        self.position_embeddings = nn.Embedding(5, self.embedding_dim)  # [CLS] h r t [SEP]
        
        logger.info(f"CCMGT组件初始化: Transformer({transformer_layers}层), 对比维度({contrastive_dim})")
    
    def get_entity_embedding(self, entity_ids: torch.Tensor) -> torch.Tensor:
        """获取实体嵌入"""
        self.validate_inputs(entity_ids)
        return self.entity_embeddings(entity_ids)
    
    def get_relation_embedding(self, relation_ids: torch.Tensor) -> torch.Tensor:
        """获取关系嵌入"""
        self.validate_inputs(entity_ids=torch.tensor([0]), relation_ids=relation_ids)
        return self.relation_embeddings(relation_ids)
    
    def _split_complex(self, embeddings: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """将复数嵌入分离为实部和虚部"""
        real, imag = torch.chunk(embeddings, 2, dim=-1)
        return real, imag
    
    def score_triples(self,
                     head_ids: torch.Tensor,
                     relation_ids: torch.Tensor,
                     tail_ids: torch.Tensor) -> torch.Tensor:
        """
        计算ComplX三元组评分
        支持掩码token处理
        """
        # 获取嵌入
        head_emb = self.get_entity_embedding(head_ids)
        rel_emb = self.get_relation_embedding(relation_ids)
        tail_emb = self.get_entity_embedding(tail_ids)
        
        # 🔥 CCMGT增强: 如果启用，通过Transformer处理
        if self.enable_ccmgt:
            # 序列化: [CLS] head relation tail [SEP]
            batch_size = head_ids.size(0)
            cls_emb = torch.zeros(batch_size, 1, self.embedding_dim, device=self.device)
            sep_emb = torch.zeros(batch_size, 1, self.embedding_dim, device=self.device)
            
            # 构建序列 [batch_size, 5, embedding_dim]
            sequence = torch.cat([
                cls_emb,  # [CLS]
                head_emb.unsqueeze(1),  # head
                rel_emb.unsqueeze(1),   # relation
                tail_emb.unsqueeze(1),  # tail
                sep_emb   # [SEP]
            ], dim=1)
            
            # 添加位置编码
            pos_ids = torch.arange(5, device=self.device).unsqueeze(0).repeat(batch_size, 1)
            sequence = sequence + self.position_embeddings(pos_ids)
            
            # Transformer编码
            for layer in self.transformer_layers:
                sequence = layer(sequence)
            
            # 提取增强后的嵌入
            head_emb = sequence[:, 1]  # enhanced head
            rel_emb = sequence[:, 2]   # enhanced relation  
            tail_emb = sequence[:, 3]  # enhanced tail
        
        # 分离实部和虚部
        head_real, head_imag = self._split_complex(head_emb)
        rel_real, rel_imag = self._split_complex(rel_emb)
        tail_real, tail_imag = self._split_complex(tail_emb)
        
        # ComplX评分计算: Re(<h, r, t̄>)
        score = (head_real * rel_real * tail_real +
                head_real * rel_imag * (-tail_imag) +
                head_imag * rel_real * (-tail_imag) +
                head_imag * rel_imag * tail_real)
        
        score = score.sum(dim=-1)
        return score
    
    def masked_reconstruction_loss(self, 
                                  head_ids: torch.Tensor,
                                  relation_ids: torch.Tensor, 
                                  tail_ids: torch.Tensor) -> torch.Tensor:
        """
        🔥 GraphMAE风格的掩码重建损失
        随机掩码实体/关系，训练模型重建原始表示
        """
        if not self.enable_ccmgt:
            return torch.tensor(0.0, device=self.device)
        
        batch_size = head_ids.size(0)
        
        # 创建掩码
        head_mask = torch.rand(batch_size, device=self.device) < self.mask_ratio
        rel_mask = torch.rand(batch_size, device=self.device) < self.mask_ratio  
        tail_mask = torch.rand(batch_size, device=self.device) < self.mask_ratio
        
        # 应用掩码
        masked_heads = head_ids.clone()
        masked_relations = relation_ids.clone()
        masked_tails = tail_ids.clone()
        
        masked_heads[head_mask] = self.entity_mask_token_id
        masked_relations[rel_mask] = self.relation_mask_token_id
        masked_tails[tail_mask] = self.entity_mask_token_id
        
        # 获取掩码后的表示
        masked_head_emb = self.get_entity_embedding(masked_heads)
        masked_rel_emb = self.get_relation_embedding(masked_relations)
        masked_tail_emb = self.get_entity_embedding(masked_tails)
        
        # 通过解码器重建
        reconstructed_heads = self.mask_decoder(masked_head_emb)
        reconstructed_relations = self.mask_decoder(masked_rel_emb)
        reconstructed_tails = self.mask_decoder(masked_tail_emb)
        
        # 原始表示
        original_head_emb = self.get_entity_embedding(head_ids)
        original_rel_emb = self.get_relation_embedding(relation_ids)
        original_tail_emb = self.get_entity_embedding(tail_ids)
        
        # 计算重建损失（仅对被掩码的token）
        head_loss = F.mse_loss(reconstructed_heads[head_mask], original_head_emb[head_mask]) if head_mask.any() else 0
        rel_loss = F.mse_loss(reconstructed_relations[rel_mask], original_rel_emb[rel_mask]) if rel_mask.any() else 0
        tail_loss = F.mse_loss(reconstructed_tails[tail_mask], original_tail_emb[tail_mask]) if tail_mask.any() else 0
        
        return (head_loss + rel_loss + tail_loss) / 3.0
    
    def contrastive_loss(self,
                        anchor_heads: torch.Tensor,
                        anchor_relations: torch.Tensor,
                        anchor_tails: torch.Tensor,
                        negative_heads: torch.Tensor = None,
                        negative_tails: torch.Tensor = None) -> torch.Tensor:
        """
        🔥 SimKGC风格的对比学习损失
        使用InfoNCE损失增强三元组表示的区分性
        """
        if not self.enable_ccmgt:
            return torch.tensor(0.0, device=self.device)
        
        batch_size = anchor_heads.size(0)
        
        # 获取锚点三元组的增强表示
        anchor_scores = self.score_triples(anchor_heads, anchor_relations, anchor_tails)
        anchor_head_emb = self.get_entity_embedding(anchor_heads)
        anchor_tail_emb = self.get_entity_embedding(anchor_tails)
        
        # 投影到对比空间
        anchor_head_proj = self.contrastive_projector(anchor_head_emb)
        anchor_tail_proj = self.contrastive_projector(anchor_tail_emb)
        
        # 正样本相似度 (head-tail pair)
        positive_sim = F.cosine_similarity(anchor_head_proj, anchor_tail_proj, dim=-1)
        
        # 负样本相似度 (使用batch内其他样本作为负样本)
        # 这是SimKGC的简化版本，实际可以使用更复杂的硬负采样
        negative_sim_list = []
        for i in range(batch_size):
            neg_indices = torch.cat([torch.arange(i), torch.arange(i+1, batch_size)])
            if len(neg_indices) > 0:
                neg_head_proj = anchor_head_proj[neg_indices]
                neg_sim = F.cosine_similarity(anchor_tail_proj[i:i+1], neg_head_proj, dim=-1)
                negative_sim_list.append(neg_sim)
        
        if negative_sim_list:
            negative_sim = torch.cat(negative_sim_list, dim=0)
            
            # InfoNCE损失
            temperature = torch.exp(self.log_temperature)
            pos_exp = torch.exp(positive_sim / temperature)
            neg_exp = torch.exp(negative_sim / temperature).view(batch_size, -1).sum(dim=1)
            
            loss = -torch.log(pos_exp / (pos_exp + neg_exp)).mean()
            return loss
        
        return torch.tensor(0.0, device=self.device)
    
    def score_heads(self,
                    relation_ids: torch.Tensor,
                    tail_ids: torch.Tensor,
                    head_ids: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算所有可能头实体的评分 (链接预测中的head-batch模式)
        
        Args:
            relation_ids (torch.Tensor): 关系ID, shape=[batch_size]
            tail_ids (torch.Tensor): 尾实体ID, shape=[batch_size]
            head_ids (Optional[torch.Tensor]): 候选头实体ID, 为None时评分所有实体
            
        Returns:
            torch.Tensor: 头实体评分, shape=[batch_size, num_candidates]
        """
        if head_ids is None:
            head_ids = torch.arange(self.num_entities, device=self.device)  # [num_entities]
        
        # 获取嵌入
        rel_emb = self.get_relation_embedding(relation_ids)  # [batch_size, embedding_dim]
        tail_emb = self.get_entity_embedding(tail_ids)       # [batch_size, embedding_dim]
        head_emb = self.get_entity_embedding(head_ids)       # [num_candidates, embedding_dim]
        
        # 分离实部和虚部
        rel_real, rel_imag = self._split_complex(rel_emb)
        tail_real, tail_imag = self._split_complex(tail_emb)
        head_real, head_imag = self._split_complex(head_emb)
        
        # 广播计算: [batch_size, 1, hidden_dim] x [1, num_candidates, hidden_dim]
        rel_real = rel_real.unsqueeze(1)    # [batch_size, 1, hidden_dim]
        rel_imag = rel_imag.unsqueeze(1)
        tail_real = tail_real.unsqueeze(1)
        tail_imag = tail_imag.unsqueeze(1)
        
        head_real = head_real.unsqueeze(0)  # [1, num_candidates, hidden_dim]
        head_imag = head_imag.unsqueeze(0)
        
        # ComplX评分计算
        score = (head_real * rel_real * tail_real +
                head_real * rel_imag * (-tail_imag) +
                head_imag * rel_real * (-tail_imag) +
                head_imag * rel_imag * tail_real)
        
        score = score.sum(dim=-1)  # [batch_size, num_candidates]
        
        return score
    
    def regularization_loss(self, 
                          head_ids: torch.Tensor,
                          relation_ids: torch.Tensor,
                          tail_ids: torch.Tensor,
                          reg_weight: float = 1e-3) -> torch.Tensor:
        """
        计算L2正则化损失
        
        Args:
            head_ids (torch.Tensor): 头实体ID
            relation_ids (torch.Tensor): 关系ID
            tail_ids (torch.Tensor): 尾实体ID
            reg_weight (float): 正则化权重
            
        Returns:
            torch.Tensor: 正则化损失
        """
        head_emb = self.get_entity_embedding(head_ids)
        rel_emb = self.get_relation_embedding(relation_ids)
        tail_emb = self.get_entity_embedding(tail_ids)
        
        reg_loss = (head_emb.pow(2).sum() + rel_emb.pow(2).sum() + tail_emb.pow(2).sum()) / head_ids.size(0)
        
        return reg_weight * reg_loss
    
    def load_grail_checkpoint(self, checkpoint_path: str) -> None:
        """
        加载来自grail-master训练的ComplX检查点
        
        Args:
            checkpoint_path (str): grail-master格式的检查点路径
            
        Raises:
            RuntimeError: 检查点格式不兼容或加载失败
        """
        import os
        import pickle
        
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
        
        try:
            # 尝试加载不同格式的检查点
            if checkpoint_path.endswith('.pkl'):
                with open(checkpoint_path, 'rb') as f:
                    checkpoint = pickle.load(f)
            else:
                checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            # 适配grail-master的权重格式
            if 'entity_embedding' in checkpoint and 'relation_embedding' in checkpoint:
                entity_weights = checkpoint['entity_embedding']
                relation_weights = checkpoint['relation_embedding']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
                entity_weights = state_dict['entity_embedding.weight']
                relation_weights = state_dict['relation_embedding.weight']
            else:
                raise RuntimeError("无法识别的检查点格式")
            
            # 验证维度兼容性
            if entity_weights.size(1) != self.embedding_dim:
                raise RuntimeError(f"实体嵌入维度不匹配: 期望{self.embedding_dim}, 得到{entity_weights.size(1)}")
            if relation_weights.size(1) != self.embedding_dim:
                raise RuntimeError(f"关系嵌入维度不匹配: 期望{self.embedding_dim}, 得到{relation_weights.size(1)}")
            
            # 加载权重
            self.entity_embeddings.weight.data = entity_weights.to(self.device)
            self.relation_embeddings.weight.data = relation_weights.to(self.device)
            
            logger.info(f"✅ 成功加载grail-master ComplX检查点: {checkpoint_path}")
            logger.info(f"   实体嵌入: {entity_weights.shape}")
            logger.info(f"   关系嵌入: {relation_weights.shape}")
            
        except Exception as e:
            raise RuntimeError(f"加载grail-master检查点失败: {e}") 