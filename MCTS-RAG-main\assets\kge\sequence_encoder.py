"""
KGTransformer风格的序列编码器
基于KGTransformer/kg_bert.py的精确实现

核心特性:
1. 扩展词汇表: token + entity + relation
2. 多token类型: head、relation、tail、special
3. 多任务分类头: 图分类、关系预测、实体对比学习
4. 序列化三元组表示
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
import math
import logging
from collections import namedtuple

logger = logging.getLogger(__name__)


class KGBertConfig:
    """
    KG-BERT配置类
    基于KGTransformer/kg_bert.py的configuration设置
    """
    
    def __init__(self,
                 vocab_size_token: int,
                 vocab_size_entity: int,
                 vocab_size_relation: int,
                 hidden_size: int = 768,
                 num_hidden_layers: int = 4,
                 num_attention_heads: int = 12,
                 intermediate_size: Optional[int] = None,
                 hidden_dropout_prob: float = 0.1,
                 attention_probs_dropout_prob: float = 0.1,
                 token_types: int = 4,
                 max_position_embeddings: int = 512,
                 layer_norm_eps: float = 1e-12,
                 pad_token_id: int = 0):
        
        # 基于KGTransformer/kg_bert.py L20-25
        self.vocab_size = vocab_size_token + vocab_size_entity + vocab_size_relation
        self.vocab_size_token = vocab_size_token  # token embedding需要fine-tune
        self.vocab_size_ent = vocab_size_token + vocab_size_entity  # entity边界
        self.vocab_size_rel = vocab_size_relation
        
        self.hidden_size = hidden_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.intermediate_size = intermediate_size or hidden_size * 4
        self.hidden_dropout_prob = hidden_dropout_prob
        self.attention_probs_dropout_prob = attention_probs_dropout_prob
        self.token_types = token_types  # 0=head, 1=relation, 2=tail, 3=special
        self.max_position_embeddings = max_position_embeddings
        self.layer_norm_eps = layer_norm_eps
        self.pad_token_id = pad_token_id
        
        # 验证参数
        assert hidden_size % num_attention_heads == 0, "hidden_size必须被num_attention_heads整除"


class KGEmbeddings(nn.Module):
    """
    KG嵌入层: 支持token、entity、relation的联合嵌入
    基于KGTransformer的扩展词汇表设计
    """
    
    def __init__(self, config: KGBertConfig):
        super().__init__()
        self.config = config
        
        # 主词嵌入: 包含token + entity + relation
        self.word_embeddings = nn.Embedding(config.vocab_size, config.hidden_size, padding_idx=config.pad_token_id)
        
        # 位置嵌入
        self.position_embeddings = nn.Embedding(config.max_position_embeddings, config.hidden_size)
        
        # Token类型嵌入: 区分head、relation、tail、special
        self.token_type_embeddings = nn.Embedding(config.token_types, config.hidden_size)
        
        # LayerNorm和Dropout
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)
        
        # 注册位置ID缓冲区
        self.register_buffer("position_ids", torch.arange(config.max_position_embeddings).expand((1, -1)))
        
        logger.info(f"KG嵌入层初始化: vocab_size={config.vocab_size}, hidden_size={config.hidden_size}")
    
    def forward(self,
               input_ids: torch.Tensor,
               token_type_ids: torch.Tensor,
               position_ids: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input_ids: 输入ID [batch_size, seq_len]
            token_type_ids: token类型ID [batch_size, seq_len]  
            position_ids: 位置ID [batch_size, seq_len]
        
        Returns:
            嵌入表示 [batch_size, seq_len, hidden_size]
        """
        seq_length = input_ids.size(1)
        
        if position_ids is None:
            position_ids = self.position_ids[:, :seq_length]
        
        # 获取各种嵌入
        words_embeddings = self.word_embeddings(input_ids)
        position_embeddings = self.position_embeddings(position_ids)
        token_type_embeddings = self.token_type_embeddings(token_type_ids)
        
        # 组合嵌入
        embeddings = words_embeddings + position_embeddings + token_type_embeddings
        embeddings = self.LayerNorm(embeddings)
        embeddings = self.dropout(embeddings)
        
        return embeddings


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    """
    
    def __init__(self, config: KGBertConfig):
        super().__init__()
        assert config.hidden_size % config.num_attention_heads == 0
        
        self.num_attention_heads = config.num_attention_heads
        self.attention_head_size = config.hidden_size // config.num_attention_heads
        self.all_head_size = self.num_attention_heads * self.attention_head_size
        
        self.query = nn.Linear(config.hidden_size, self.all_head_size)
        self.key = nn.Linear(config.hidden_size, self.all_head_size)
        self.value = nn.Linear(config.hidden_size, self.all_head_size)
        
        self.dropout = nn.Dropout(config.attention_probs_dropout_prob)
    
    def transpose_for_scores(self, x: torch.Tensor) -> torch.Tensor:
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)
    
    def forward(self,
               hidden_states: torch.Tensor,
               attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        
        mixed_query_layer = self.query(hidden_states)
        mixed_key_layer = self.key(hidden_states)
        mixed_value_layer = self.value(hidden_states)
        
        query_layer = self.transpose_for_scores(mixed_query_layer)
        key_layer = self.transpose_for_scores(mixed_key_layer)
        value_layer = self.transpose_for_scores(mixed_value_layer)
        
        # 计算注意力分数
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / math.sqrt(self.attention_head_size)
        
        if attention_mask is not None:
            attention_scores = attention_scores + attention_mask
        
        attention_probs = F.softmax(attention_scores, dim=-1)
        attention_probs = self.dropout(attention_probs)
        
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(*new_context_layer_shape)
        
        return context_layer


class TransformerLayer(nn.Module):
    """
    Transformer层
    """
    
    def __init__(self, config: KGBertConfig):
        super().__init__()
        self.attention = MultiHeadAttention(config)
        self.attention_output = nn.Linear(config.hidden_size, config.hidden_size)
        self.attention_dropout = nn.Dropout(config.hidden_dropout_prob)
        self.attention_layer_norm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        
        self.intermediate = nn.Linear(config.hidden_size, config.intermediate_size)
        self.intermediate_act_fn = F.gelu
        self.output = nn.Linear(config.intermediate_size, config.hidden_size)
        self.output_dropout = nn.Dropout(config.hidden_dropout_prob)
        self.output_layer_norm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
    
    def forward(self,
               hidden_states: torch.Tensor,
               attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        
        # Self-attention
        attention_output = self.attention(hidden_states, attention_mask)
        attention_output = self.attention_output(attention_output)
        attention_output = self.attention_dropout(attention_output)
        attention_output = self.attention_layer_norm(attention_output + hidden_states)
        
        # Feed forward
        intermediate_output = self.intermediate(attention_output)
        intermediate_output = self.intermediate_act_fn(intermediate_output)
        layer_output = self.output(intermediate_output)
        layer_output = self.output_dropout(layer_output)
        layer_output = self.output_layer_norm(layer_output + attention_output)
        
        return layer_output


class KGTransformerEncoder(nn.Module):
    """
    KG-Transformer编码器
    基于KGTransformer/kg_bert.py的encoder设计
    """
    
    def __init__(self, config: KGBertConfig):
        super().__init__()
        self.config = config
        
        # 嵌入层
        self.embeddings = KGEmbeddings(config)
        
        # Transformer层
        self.layers = nn.ModuleList([TransformerLayer(config) for _ in range(config.num_hidden_layers)])
        
        # Pooler (用于CLS token)
        self.pooler = nn.Linear(config.hidden_size, config.hidden_size)
        self.pooler_activation = nn.Tanh()
        
        logger.info(f"KG-Transformer编码器初始化: {config.num_hidden_layers}层")
    
    def forward(self,
               input_ids: torch.Tensor,
               token_type_ids: torch.Tensor,
               attention_mask: Optional[torch.Tensor] = None,
               position_ids: Optional[torch.Tensor] = None,
               return_all_hidden_states: bool = False) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            input_ids: 输入序列 [batch_size, seq_len]
            token_type_ids: token类型 [batch_size, seq_len]
            attention_mask: 注意力mask [batch_size, seq_len]
            position_ids: 位置ID [batch_size, seq_len]
            return_all_hidden_states: 是否返回所有隐藏状态
        
        Returns:
            包含last_hidden_state、pooler_output等的字典
        """
        # 处理attention mask
        if attention_mask is not None:
            extended_attention_mask = attention_mask[:, None, None, :]
            extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0
        else:
            extended_attention_mask = None
        
        # 嵌入
        embedding_output = self.embeddings(input_ids, token_type_ids, position_ids)
        
        # Transformer层
        hidden_states = embedding_output
        all_hidden_states = [hidden_states] if return_all_hidden_states else None
        
        for layer in self.layers:
            hidden_states = layer(hidden_states, extended_attention_mask)
            if return_all_hidden_states:
                all_hidden_states.append(hidden_states)
        
        # Pooler (CLS token)
        pooled_output = self.pooler(hidden_states[:, 0])
        pooled_output = self.pooler_activation(pooled_output)
        
        result = {
            'last_hidden_state': hidden_states,
            'pooler_output': pooled_output
        }
        
        if return_all_hidden_states:
            result['all_hidden_states'] = all_hidden_states
        
        return result


class CLSBasedClassifier(nn.Module):
    """
    基于CLS token的分类器
    基于KGTransformer/kg_bert.py的CLS_based_calssifier
    """
    
    def __init__(self, input_dim: int, num_classes: int):
        super().__init__()
        self.linear = nn.Linear(input_dim, num_classes)
        self.log_softmax = nn.LogSoftmax(dim=-1)
    
    def forward(self, cls_features: torch.Tensor) -> torch.Tensor:
        logits = self.linear(cls_features)
        return self.log_softmax(logits)


class MaskBasedClassifier(nn.Module):
    """
    基于MASK token的分类器  
    基于KGTransformer/kg_bert.py的MASK_based_calssifier
    """
    
    def __init__(self, input_dim: int, num_classes: int):
        super().__init__()
        self.linear = nn.Linear(input_dim, num_classes)
        self.log_softmax = nn.LogSoftmax(dim=-1)
    
    def forward(self, mask_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            mask_features: MASK位置的特征 [batch_size, seq_len, input_dim]
        Returns:
            分类logits [batch_size, seq_len, num_classes]
        """
        logits = self.linear(mask_features)
        return self.log_softmax(logits)


class KGSequenceEncoder(nn.Module):
    """
    KG序列编码器主模型
    基于KGTransformer/kg_bert.py的KGBert类
    """
    
    def __init__(self, config: KGBertConfig):
        super().__init__()
        self.config = config
        
        # 主编码器
        self.encoder = KGTransformerEncoder(config)
        
        # 多任务分类头
        # Task 0: 图分类 (基于CLS + 特殊位置token)
        self.cls0 = CLSBasedClassifier(config.hidden_size * 2, 2)  # 连接CLS和特殊位置
        
        # Task 1: 关系掩码预测
        self.cls1 = MaskBasedClassifier(config.hidden_size, config.vocab_size_rel)
        
        # Task 2: 实体对比学习的映射层
        self.map_mem = nn.Linear(config.hidden_size, config.hidden_size)
        
        # 损失函数
        self.nll = nn.NLLLoss(ignore_index=-1)
        self.crossentropy = nn.CrossEntropyLoss(ignore_index=-1)
        self.mse = nn.MSELoss(reduction='mean')
        self.bce = nn.BCELoss(reduction='mean')
        
        logger.info(f"KG序列编码器初始化完成")
    
    def forward(self,
               input_dict: Dict[str, torch.Tensor],
               task_type: torch.Tensor,
               labels: torch.Tensor,
               special_indices: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, 
                                                                      torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        多任务前向传播
        基于KGTransformer/kg_bert.py的forward方法 (L47-60)
        
        Args:
            input_dict: 包含input_ids、token_type_ids、attention_mask的字典
            task_type: 任务类型 [batch_size] (0=图分类, 1=关系预测, 2=实体对比)
            labels: 标签 [batch_size, label_dim]
            special_indices: 特殊位置索引 [batch_size] (用于task 0)
        
        Returns:
            loss0, loss1, loss2, acc0, acc1, acc2
        """
        # 编码
        outputs = self.encoder(**input_dict)
        last_hidden_states = outputs['last_hidden_state']
        pooler_output = outputs['pooler_output']
        
        # 按任务类型分组
        task_indices = []
        for i in range(3):
            task_indices.append(task_type == i)
        
        # 计算各任务损失
        loss0, acc0 = self._compute_task0_loss(last_hidden_states[task_indices[0]], 
                                              labels[task_indices[0]], 
                                              special_indices)
        loss1, acc1 = self._compute_task1_loss(last_hidden_states[task_indices[1]], 
                                              labels[task_indices[1]])
        loss2, acc2 = self._compute_task2_loss(last_hidden_states[task_indices[2]], 
                                              labels[task_indices[2]])
        
        return loss0, loss1, loss2, acc0, acc1, acc2
    
    def _compute_task0_loss(self, 
                           last_hidden: torch.Tensor,
                           labels: torch.Tensor,
                           special_indices: Optional[torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Task 0: 图分类损失
        基于KGTransformer/kg_bert.py的loss_task0方法 (L62-78)
        """
        if len(labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        # CLS token特征
        cls_emb = last_hidden[:, 0]  # [batch_size, hidden_size]
        
        if special_indices is not None:
            # 特殊位置特征
            batch_indices = torch.arange(len(special_indices))
            special_emb = last_hidden[batch_indices, special_indices]  # [batch_size, hidden_size]
            # 连接CLS和特殊位置特征
            combined_features = torch.cat([cls_emb, special_emb], dim=1)  # [batch_size, hidden_size*2]
        else:
            combined_features = torch.cat([cls_emb, cls_emb], dim=1)  # fallback
        
        # 分类
        output = self.cls0(combined_features)
        target_labels = labels[:, 0] if labels.dim() > 1 else labels
        loss = self.nll(output, target_labels)
        
        # 计算准确率
        pred_labels = output.detach().argmax(dim=1)
        acc = (pred_labels == target_labels).float().mean()
        
        return loss, acc
    
    def _compute_task1_loss(self, 
                           last_hidden: torch.Tensor,
                           labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Task 1: 关系掩码预测损失
        基于KGTransformer/kg_bert.py的loss_task1方法 (L80-95)
        """
        # 过滤有效标签
        valid_mask = labels != -1
        valid_labels = labels[valid_mask]
        
        if len(valid_labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        # 对所有位置进行关系预测
        output = self.cls1(last_hidden)  # [batch_size, seq_len, num_relations]
        
        # 计算损失（只对有效标签位置）
        loss = self.nll(output.transpose(1, 2), labels)  # 需要转置以匹配NLL输入格式
        
        # 计算准确率
        pred_labels = output.detach().argmax(dim=2)[valid_mask]
        acc = (pred_labels == valid_labels).float().mean()
        
        return loss, acc
    
    def _compute_task2_loss(self, 
                           last_hidden: torch.Tensor,
                           labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Task 2: 实体对比学习损失
        基于KGTransformer/kg_bert.py的loss_task2方法 (L97-143)
        """
        # 过滤有效位置
        valid_mask = labels != -1
        valid_labels = labels[valid_mask]
        
        if len(valid_labels) == 0:
            return torch.tensor(0.0), torch.tensor(0.0)
        
        # 映射隐藏状态
        mapped_hidden = self.map_mem(last_hidden[valid_mask])  # [num_valid, hidden_size]
        
        # 获取实体嵌入 (这里需要访问word_embeddings)
        entity_embeddings = self.encoder.embeddings.word_embeddings(valid_labels)  # [num_valid, hidden_size]
        
        # 计算相似度矩阵
        similarity_matrix = torch.mm(mapped_hidden, entity_embeddings.t())  # [num_valid, num_valid]
        similarity_matrix = F.sigmoid(similarity_matrix)
        
        # 构建标签矩阵
        label_matrix = (valid_labels.unsqueeze(1) == valid_labels.unsqueeze(0)).float()
        
        # 分离正负样本
        positive_mask = label_matrix == 1
        negative_mask = label_matrix == 0
        
        # 计算损失
        positive_similarities = similarity_matrix[positive_mask]
        negative_similarities = similarity_matrix[negative_mask]
        
        loss_pos = self.bce(positive_similarities, label_matrix[positive_mask])
        loss_neg = self.bce(negative_similarities, label_matrix[negative_mask])
        loss = (loss_pos + loss_neg) / 2
        
        # 计算准确率
        acc_pos = (positive_similarities > 0.5).float().mean()
        acc_neg = (negative_similarities < 0.5).float().mean()
        acc = (acc_pos + acc_neg) / 2
        
        return loss, acc


def create_kg_sequence(head_id: int, 
                      relation_id: int, 
                      tail_id: int,
                      vocab_offset_entity: int,
                      vocab_offset_relation: int,
                      cls_token_id: int = 101,
                      sep_token_id: int = 102,
                      max_length: int = 126) -> Dict[str, torch.Tensor]:
    """
    创建KG序列 
    基于KGTransformer的序列化策略
    
    格式: [CLS] head_entity relation tail_entity [SEP]
    """
    # 转换ID到词汇表空间
    head_vocab_id = head_id + vocab_offset_entity
    relation_vocab_id = relation_id + vocab_offset_relation  
    tail_vocab_id = tail_id + vocab_offset_entity
    
    # 构建序列
    input_ids = [cls_token_id, head_vocab_id, relation_vocab_id, tail_vocab_id, sep_token_id]
    token_type_ids = [3, 0, 1, 2, 3]  # special, head, relation, tail, special
    attention_mask = [1] * 5
    
    # 填充到最大长度
    while len(input_ids) < max_length:
        input_ids.append(0)  # PAD
        token_type_ids.append(3)
        attention_mask.append(0)
    
    return {
        'input_ids': torch.tensor(input_ids).unsqueeze(0),
        'token_type_ids': torch.tensor(token_type_ids).unsqueeze(0),
        'attention_mask': torch.tensor(attention_mask).unsqueeze(0)
    } 