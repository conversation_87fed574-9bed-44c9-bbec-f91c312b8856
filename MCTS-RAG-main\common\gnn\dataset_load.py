import json
import numpy as np
import re

from sklearn import get_config
from tqdm import tqdm
import torch
from collections import Counter
import random
import warnings
import pickle
warnings.filterwarnings("ignore")
from modules.question_encoding.tokenizers import LSTMTokenizer#, BERTTokenizer
from transformers import AutoTokenizer
import time

import os


class BasicDataLoader(object):
    """ 
    Basic Dataloader contains all the functions to read questions and KGs from json files and
    create mappings between global entity ids and local ids that are used during GNN updates.
    """

    def __init__(self, config, word2id, relation2id, entity2id, tokenize, data_type="train"):
        self.tokenize = tokenize  # 分词方式，如 'lstm' 或 'bert'
        self._parse_args(config, word2id, relation2id, entity2id)  # 解析配置和字典
        self._load_file(config, data_type)  # 加载数据文件
        self._load_data()  # 加载数据并创建映射

    def _load_file(self, config, data_type="train"):
        """
        Loads lines (questions + KG subgraphs) from json files.
        """
        data_file = config['data_folder'] + data_type + ".json"  # 数据文件路径
        self.data_file = data_file  # 存储数据文件路径
        print('loading data from', data_file)  # 打印加载的数据文件路径
        self.data_type = data_type  # 数据类型，如 'train' 或 'test'
        self.data = []  # 存储加载的数据
        skip_index = set()  # 存储需要跳过的索引
        index = 0  # 当前处理的索引

        with open(data_file) as f_in:  # 打开数据文件
            for line in tqdm(f_in):  # 逐行读取数据文件
                if index == config['max_train'] and data_type == "train": break  # 如果达到最大训练样本数，则停止
                line = json.loads(line)  # 解析 JSON 行
                
                if len(line['entities']) == 0:  # 如果实体列表为空
                    skip_index.add(index)  # 添加到跳过索引集合
                    continue
                self.data.append(line)  # 将数据添加到列表中
                self.max_facts = max(self.max_facts, 2 * len(line['subgraph']['tuples']))  # 更新最大事实数
                index += 1  # 增加索引

        print("skip", skip_index)  # 打印跳过的索引
        print('max_facts: ', self.max_facts)  # 打印最大事实数
        self.num_data = len(self.data)  # 存储数据总数
        self.batches = np.arange(self.num_data)  # 创建批次索引数组

    def _load_data(self):
        """
        Creates mappings between global entity ids and local entity ids that are used during GNN updates.
        """
        print('converting global to local entity index ...')  # 打印转换全局到局部实体索引的信息
        self.global2local_entity_maps = self._build_global2local_entity_maps()  # 构建全局到局部实体的映射

        if self.use_self_loop:  # 如果使用自环
            self.max_facts = self.max_facts + self.max_local_entity  # 更新最大事实数

        self.question_id = []  # 存储问题 ID
        self.candidate_entities = np.full((self.num_data, self.max_local_entity), len(self.entity2id), dtype=int)  # 候选实体数组
        self.kb_adj_mats = np.empty(self.num_data, dtype=object)  # 知识库邻接矩阵
        self.q_adj_mats = np.empty(self.num_data, dtype=object)  # 问题邻接矩阵
        self.kb_fact_rels = np.full((self.num_data, self.max_facts), self.num_kb_relation, dtype=int)  # 知识库事实关系
        self.query_entities = np.zeros((self.num_data, self.max_local_entity), dtype=float)  # 查询实体
        self.seed_list = np.empty(self.num_data, dtype=object)  # 种子列表
        self.seed_distribution = np.zeros((self.num_data, self.max_local_entity), dtype=float)  # 种子分布
        self.answer_dists = np.zeros((self.num_data, self.max_local_entity), dtype=float)  # 答案分布
        self.answer_lists = np.empty(self.num_data, dtype=object)  # 答案列表

        self._prepare_data()  # 准备数据

        # KGE集成：初始化距离特征计算 
        if hasattr(self, 'use_complex_kge') and self.use_complex_kge:
            self._initialize_kge_features()

    def _initialize_kge_features(self):
        """
        初始化KGE相关功能，包括距离特征计算
        """
        import logging
        logger = logging.getLogger(__name__)
        
        if not self.use_complex_kge:
            return
            
        logger.info("🔧 初始化KGE距离特征...")
        
        # 初始化距离特征存储
        self.distance_features = np.zeros((self.num_data, self.max_local_entity, self.max_distance + 1), dtype=float)
        
        # 为每个样本计算距离特征
        for sample_idx in range(self.num_data):
            g2l = self.global2local_entity_maps[sample_idx]
            if len(g2l) == 0:
                continue
                
            # 构建局部邻接矩阵用于距离计算
            local_adj_mat = self._build_local_adjacency_matrix(sample_idx)
            
            # 计算所有实体对之间的距离特征
            if local_adj_mat is not None:
                distance_matrix = self._compute_distance_matrix(local_adj_mat)
                
                # 转换为one-hot距离特征
                for i in range(len(g2l)):
                    for j in range(len(g2l)):
                        dist = min(distance_matrix[i, j], self.max_distance)
                        self.distance_features[sample_idx, i, dist] = 1.0
        
        logger.info(f"✅ 距离特征初始化完成: {self.distance_features.shape}")
    
    def _build_local_adjacency_matrix(self, sample_idx):
        """
        为指定样本构建局部邻接矩阵
        
        Args:
            sample_idx (int): 样本索引
            
        Returns:
            torch.Tensor: 局部邻接矩阵，如果样本无效则返回None
        """
        g2l = self.global2local_entity_maps[sample_idx]
        if len(g2l) == 0:
            return None
            
        num_local_entities = len(g2l)
        adj_matrix = np.zeros((num_local_entities, num_local_entities), dtype=float)
        
        # 从样本数据构建邻接矩阵
        sample = self.data[sample_idx]
        subgraph = sample['subgraph']
        
        for triple in subgraph['tuples']:
            head_global = triple[0]
            tail_global = triple[2]
            
            if head_global in g2l and tail_global in g2l:
                head_local = g2l[head_global]
                tail_local = g2l[tail_global]
                adj_matrix[head_local, tail_local] = 1.0
                adj_matrix[tail_local, head_local] = 1.0  # 无向图
        
        return torch.from_numpy(adj_matrix).float()
    
    def _compute_distance_matrix(self, adj_matrix):
        """
        使用Floyd-Warshall算法计算最短路径距离矩阵
        
        Args:
            adj_matrix (torch.Tensor): 邻接矩阵
            
        Returns:
            np.ndarray: 距离矩阵
        """
        num_nodes = adj_matrix.size(0)
        dist_matrix = np.full((num_nodes, num_nodes), self.max_distance, dtype=int)
        
        # 初始化距离矩阵
        for i in range(num_nodes):
            dist_matrix[i, i] = 0  # 自环距离为0
            for j in range(num_nodes):
                if adj_matrix[i, j] > 0:
                    dist_matrix[i, j] = 1  # 直接连接距离为1
        
        # Floyd-Warshall算法
        for k in range(num_nodes):
            for i in range(num_nodes):
                for j in range(num_nodes):
                    if dist_matrix[i, k] + dist_matrix[k, j] < dist_matrix[i, j]:
                        dist_matrix[i, j] = dist_matrix[i, k] + dist_matrix[k, j]
        
        return dist_matrix

    def _parse_args(self, config, word2id, relation2id, entity2id):
        """
        Builds necessary dictionaries and stores arguments.
        """
        self.data_eff = config['data_eff']  # 数据效率标志
        self.data_name = config['name']  # 数据名称

        if 'use_inverse_relation' in config:  # 如果配置中包含反向关系
            self.use_inverse_relation = config['use_inverse_relation']  # 设置使用反向关系
        else:
            self.use_inverse_relation = False  # 否则不使用反向关系
        if 'use_self_loop' in config:  # 如果配置中包含自环
            self.use_self_loop = config['use_self_loop']  # 设置使用自环
        else:
            self.use_self_loop = False  # 否则不使用自环

        self.rel_word_emb = config['relation_word_emb']  # 关系词嵌入标志
        # self.num_step = config['num_step']
        self.max_local_entity = 0  # 最大局部实体数
        self.max_relevant_doc = 0  # 最大相关文档数
        self.max_facts = 0  # 最大事实数
        
        # KGE相关参数
        self.use_complex_kge = config.get('use_complex_kge', False)
        self.complex_hidden_dim = config.get('complex_hidden_dim', 50)
        self.kge_fusion_method = config.get('kge_fusion_method', 'concat')
        self.complex_checkpoint_path = config.get('complex_checkpoint_path', '')
        self.max_distance = config.get('max_distance', 5)
        self.freeze_kge_embeddings = config.get('freeze_kge_embeddings', True)
        self.kge_model_type = config.get('kge_model_type', 'ComplX')

        print('building word index ...')  # 打印构建词索引的信息
        self.word2id = word2id  # 词到 ID 的映射
        self.id2word = {i: word for word, i in word2id.items()}  # ID 到词的映射
        self.relation2id = relation2id  # 关系到 ID 的映射
        self.entity2id = entity2id  # 实体到 ID 的映射
        self.id2entity = {i: entity for entity, i in entity2id.items()}  # ID 到实体的映射
        self.q_type = config['q_type']  # 问题类型

        if self.use_inverse_relation:  # 如果使用反向关系
            self.num_kb_relation = 2 * len(relation2id)  # 知识库关系数为关系数的两倍
        else:
            self.num_kb_relation = len(relation2id)  # 知识库关系数为关系数
        if self.use_self_loop:  # 如果使用自环
            self.num_kb_relation = self.num_kb_relation + 1  # 知识库关系数加一
        print("Entity: {}, Relation in KB: {}, Relation in use: {} ".format(len(entity2id),
                                                                            len(self.relation2id),
                                                                            self.num_kb_relation))  # 打印实体、关系和知识库关系数

    def get_quest(self, training=False):
        q_list = []  # 初始化问题列表，用于存储解码后的问题文本
        
        sample_ids = self.sample_ids  # 获取当前批次的样本ID列表
        for sample_id in sample_ids:  # 遍历每个样本ID
            tp_str = self.decode_text(self.query_texts[sample_id, :])  # 解码样本的查询文本
            # id2word = self.id2word
            # for i in range(self.max_query_word):
            #     if self.query_texts[sample_id, i] in id2word:
            #         tp_str += id2word[self.query_texts[sample_id, i]] + " "
            q_list.append(tp_str)  # 将解码后的文本添加到问题列表中
        return q_list  # 返回问题列表

    def decode_text(self, np_array_x):
        if self.tokenize == 'lstm':  # 如果分词方式是LSTM
            id2word = self.id2word  # 获取ID到单词的映射
            tp_str = ""  # 初始化解码后的文本字符串
            for i in range(self.max_query_word):  # 遍历最大查询单词数
                if np_array_x[i] in id2word:  # 如果当前ID在映射中
                    tp_str += id2word[np_array_x[i]] + " "  # 将对应的单词添加到字符串中
        else:  # 如果分词方式不是LSTM
            tp_str = ""  # 初始化解码后的文本字符串
            words = self.tokenizer.convert_ids_to_tokens(np_array_x)  # 将ID转换为单词列表
            for w in words:  # 遍历每个单词
                if w not in ['[CLS]', '[SEP]', '[PAD]']:  # 如果单词不是特殊标记
                    tp_str += w + " "  # 将单词添加到字符串中
        return tp_str  # 返回解码后的文本字符串

    def _prepare_data(self):
        """
        Tokenizes questions and builds various data structures.
        """
        # 初始化数据存储结构
        self.question_id = []
        self.query_entities = np.zeros((self.num_data, self.max_local_entity), dtype=np.float32)
        self.candidate_entities = np.zeros((self.num_data, self.max_local_entity), dtype=np.int64)
        self.seed_distribution = np.zeros((self.num_data, self.max_local_entity), dtype=np.float32)
        self.seed_list = [[] for _ in range(self.num_data)]
        self.kb_fact_rels = np.zeros((self.num_data, self.max_kb_fact), dtype=np.int64)

        # 计算最大查询词数
        max_count = 0
        for sample in self.data:
            question = sample['question']
            words = question.split()
            max_count = max(len(words), max_count)

        # 根据tokenize类型选择不同的处理方式
        if self.tokenize == 'lstm':
            # LSTM模式：使用word2id词典
            self.max_query_word = max_count + 2
            self.query_texts = np.full((self.num_data, self.max_query_word), len(self.word2id), dtype=int)
        elif self.tokenize == 'hybrid':
            # Hybrid模式：同时支持BERT和LSTM tokenization
            # 使用BERT tokenizer作为主要tokenizer（用于BERT部分）
            self.bert_tokenizer = AutoTokenizer.from_pretrained('pretrained_lms/sr-simbert/')
            self.max_query_word = max_count + 2
            self.bert_pad_val = self.bert_tokenizer.convert_tokens_to_ids(self.bert_tokenizer.pad_token)
            self.query_texts = np.full((self.num_data, self.max_query_word), self.bert_pad_val, dtype=int)
            
            # 同时准备LSTM的tokenization（用于BiLSTM部分）
            self.lstm_query_texts = np.full((self.num_data, self.max_query_word), len(self.word2id), dtype=int)
        else:
            # BERT系列模式：使用预训练tokenizer
            if self.tokenize == 'bert':
                tokenizer_name = 'bert-base-uncased'
            elif self.tokenize == 'roberta':
                tokenizer_name = 'roberta-base'
            elif self.tokenize == 'sbert':
                tokenizer_name = 'sentence-transformers/all-MiniLM-L6-v2'
            elif self.tokenize == 'sbert2':
                tokenizer_name = 'sentence-transformers/all-mpnet-base-v2'
            elif self.tokenize == 'simcse':
                tokenizer_name = 'princeton-nlp/sup-simcse-bert-base-uncased'
            elif self.tokenize == 't5':
                tokenizer_name = 't5-small'
            elif self.tokenize == 'relbert':
                tokenizer_name = 'pretrained_lms/sr-simbert/'
            elif self.tokenize == 'hybrid':  # 新增：Hybrid (BERT+BiLSTM) 使用relbert同一词表
                tokenizer_name = 'pretrained_lms/sr-simbert/'
            
            self.max_query_word = max_count + 2
            self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
            self.num_word = self.tokenizer.convert_tokens_to_ids(self.tokenizer.pad_token)
            self.query_texts = np.full((self.num_data, self.max_query_word), self.num_word, dtype=int)

        next_id = 0
        num_query_entity = {}
        for sample in tqdm(self.data):
            self.question_id.append(sample["id"])
            g2l = self.global2local_entity_maps[next_id]
            if len(g2l) == 0:
                continue
            
            # 构建问题和实体之间的连接
            tp_set = set()
            seed_list = []
            key_ent = 'entities_cid' if 'entities_cid' in sample else 'entities'
            for j, entity in enumerate(sample[key_ent]):
                try:
                    if isinstance(entity, dict) and 'text' in entity:
                        global_entity = self.entity2id[entity['text']]
                    else:
                        global_entity = self.entity2id[entity]
                    global_entity = self.entity2id[entity['text']]
                except:
                    global_entity = entity

                if global_entity not in g2l:
                    continue
                local_ent = g2l[global_entity]
                self.query_entities[next_id, local_ent] = 1.0
                seed_list.append(local_ent)
                tp_set.add(local_ent)
            
            self.seed_list[next_id] = seed_list
            num_query_entity[next_id] = len(tp_set)
            for global_entity, local_entity in g2l.items():
                if self.data_name != 'cwq':
                    if local_entity not in tp_set:
                        self.candidate_entities[next_id, local_entity] = global_entity
                elif self.data_name == 'cwq':
                    self.candidate_entities[next_id, local_entity] = global_entity

            # 知识库中的关系
            head_list = []
            rel_list = []
            tail_list = []
            for i, tpl in enumerate(sample['subgraph']['tuples']):
                sbj, rel, obj = tpl
                try:
                    if isinstance(sbj, dict) and 'text' in sbj:
                        head = g2l[self.entity2id[sbj['text']]]
                        rel = self.relation2id[rel['text']]
                        tail = g2l[self.entity2id[obj['text']]]
                    else:
                        head = g2l[self.entity2id[sbj]]
                        rel = self.relation2id[rel]
                        tail = g2l[self.entity2id[obj]]
                except:
                    head = g2l[sbj]
                    try:
                        rel = int(rel)
                    except:
                        rel = self.relation2id[rel]
                    tail = g2l[obj]
                head_list.append(head)
                rel_list.append(rel)
                tail_list.append(tail)
                self.kb_fact_rels[next_id, i] = rel
                if self.use_inverse_relation:
                    head_list.append(tail)
                    rel_list.append(rel + len(self.relation2id))
                    tail_list.append(head)
                    self.kb_fact_rels[next_id, i] = rel + len(self.relation2id)
                
            if len(tp_set) > 0:
                for local_ent in tp_set:
                    self.seed_distribution[next_id, local_ent] = 1.0 / len(tp_set)
            else:
                self.seed_distribution[next_id, 0] = 1.0

            # 问题tokenization处理
            question = sample['question']
            if self.tokenize == 'lstm':
                # LSTM模式：使用word2id
                words = question.split()
                for j, word in enumerate(words):
                    if j < self.max_query_word:
                        if word in self.word2id:
                            self.query_texts[next_id, j] = self.word2id[word]
                        else:
                            self.query_texts[next_id, j] = len(self.word2id)
            elif self.tokenize == 'hybrid':
                # Hybrid模式：同时进行BERT和LSTM tokenization
                # BERT tokenization
                bert_tokens = self.bert_tokenizer.encode_plus(
                    text=question, 
                    max_length=self.max_query_word,
                    pad_to_max_length=True, 
                    return_attention_mask=False, 
                    truncation=True
                )
                self.query_texts[next_id] = np.array(bert_tokens['input_ids'])
                
                # LSTM tokenization
                words = question.split()
                for j, word in enumerate(words):
                    if j < self.max_query_word:
                        if word in self.word2id:
                            self.lstm_query_texts[next_id, j] = self.word2id[word]
                        else:
                            self.lstm_query_texts[next_id, j] = len(self.word2id)
            else:
                # BERT系列模式
                tokens = self.tokenizer.encode_plus(
                    text=question, 
                    max_length=self.max_query_word,
                    pad_to_max_length=True, 
                    return_attention_mask=False, 
                    truncation=True
                )
                self.query_texts[next_id] = np.array(tokens['input_ids'])

            # 构建邻接矩阵
            if self.data_eff:
                self.create_kb_adj_mats(next_id)
            else:
                self.kb_adj_mats[next_id] = self._build_local_adjacency_matrix(next_id)

            next_id += 1

        # 打印统计信息
        print(f"Processed {next_id} samples")
        print(f"Average query entities: {np.mean(list(num_query_entity.values())):.2f}")
        print(f"Max query entities: {max(num_query_entity.values())}")
        print(f"Min query entities: {min(num_query_entity.values())}")

        # 构建关系词
        self.build_rel_words(self.tokenize)

    def build_rel_words(self, tokenize):
        """ 
        Tokenizes relation surface forms.
        """

        max_rel_words = 0  # 初始化最大关系词数为0
        rel_words = []  # 初始化关系词列表为空列表
        if 'metaqa' in self.data_file:  # 如果数据文件包含'metaqa'
            for rel in self.relation2id:  # 遍历关系ID字典
                words = rel.split('_')  # 将关系ID按'_'分割成单词列表
                max_rel_words = max(len(words), max_rel_words)  # 更新最大关系词数
                rel_words.append(words)  # 将单词列表添加到关系词列表中
            #print(rel_words)  # 打印关系词列表（注释掉的调试代码）
        else:  # 如果数据文件不包含'metaqa'
            for rel in self.relation2id:  # 遍历关系ID字典
                rel = rel.strip()  # 去除关系ID两端的空白字符
                fields = rel.split('.')  # 将关系ID按'.'分割成字段列表
                try:
                    words = fields[-2].split('_') + fields[-1].split('_')  # 将倒数第二个和最后一个字段按'_'分割并合并成单词列表
                    max_rel_words = max(len(words), max_rel_words)  # 更新最大关系词数
                    rel_words.append(words)  # 将单词列表添加到关系词列表中
                    #print(rel, words)  # 打印关系ID和单词列表（注释掉的调试代码）
                except:
                    words = ['UNK']  # 如果发生异常，将单词列表设为['UNK']
                    rel_words.append(words)  # 将单词列表添加到关系词列表中
                    pass  # 忽略异常
                #words = fields[-2].split('_') + fields[-1].split('_')  # 重复代码（注释掉）
            
        self.max_rel_words = max_rel_words  # 设置最大关系词数
        if tokenize == 'lstm':  # 如果分词方式为LSTM
            self.rel_texts = np.full((self.num_kb_relation + 1, self.max_rel_words), len(self.word2id), dtype=int)  # 初始化关系文本数组
            self.rel_texts_inv = np.full((self.num_kb_relation + 1, self.max_rel_words), len(self.word2id), dtype=int)  # 初始化反向关系文本数组
            for rel_id,tokens in enumerate(rel_words):  # 遍历关系词列表
                for j, word in enumerate(tokens):  # 遍历单词列表
                    if j < self.max_rel_words:  # 如果索引小于最大关系词数
                            if word in self.word2id:  # 如果单词在词ID字典中
                                self.rel_texts[rel_id, j] = self.word2id[word]  # 设置关系文本数组
                                self.rel_texts_inv[rel_id, j] = self.word2id[word]  # 设置反向关系文本数组
                            else:
                                self.rel_texts[rel_id, j] = len(self.word2id)  # 如果单词不在词ID字典中，设置为词ID字典的长度
                                self.rel_texts_inv[rel_id, j] = len(self.word2id)  # 设置反向关系文本数组
        else:  # 如果分词方式不是LSTM
            if tokenize == 'bert':  # 如果分词方式为BERT
                tokenizer_name = 'bert-base-uncased'  # 设置分词器名称
            elif tokenize == 'roberta':  # 如果分词方式为RoBERTa
                tokenizer_name = 'roberta-base'  # 设置分词器名称
            elif tokenize == 'sbert':  # 如果分词方式为Sentence-BERT
                tokenizer_name = 'sentence-transformers/all-MiniLM-L6-v2'  # 设置分词器名称
            elif tokenize == 'sbert2':  # 如果分词方式为Sentence-BERT2
                tokenizer_name = 'sentence-transformers/all-mpnet-base-v2'  # 设置分词器名称
            elif tokenize == 'simcse':  # 如果分词方式为SimCSE
                tokenizer_name = 'princeton-nlp/sup-simcse-bert-base-uncased'  # 设置分词器名称
            elif tokenize == 't5':  # 如果分词方式为T5
                tokenizer_name = 't5-small'  # 设置分词器名称
            elif tokenize  == 'relbert':  # 如果分词方式为RelBERT
                tokenizer_name = 'pretrained_lms/sr-simbert/'  # 设置分词器名称
            elif tokenize == 'hybrid':  # 新增：Hybrid (BERT+BiLSTM) 使用relbert同一词表
                tokenizer_name = 'pretrained_lms/sr-simbert/'
            
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)  # 从预训练模型加载分词器
            pad_val = tokenizer.convert_tokens_to_ids(tokenizer.pad_token)  # 获取填充词的ID
            self.rel_texts = np.full((self.num_kb_relation + 1, self.max_rel_words), pad_val, dtype=int)  # 初始化关系文本数组
            self.rel_texts_inv = np.full((self.num_kb_relation + 1, self.max_rel_words), pad_val, dtype=int)  # 初始化反向关系文本数组
            
            for rel_id,words in enumerate(rel_words):  # 遍历关系词列表

                tokens =  tokenizer.encode_plus(text=' '.join(words), max_length=self.max_rel_words, \
                    pad_to_max_length=True, return_attention_mask = False, truncation=True)  # 编码关系词列表
                tokens_inv =  tokenizer.encode_plus(text=' '.join(words[::-1]), max_length=self.max_rel_words, \
                    pad_to_max_length=True, return_attention_mask = False, truncation=True)  # 编码反向关系词列表
                self.rel_texts[rel_id] = np.array(tokens['input_ids'])  # 设置关系文本数组
                self.rel_texts_inv[rel_id] = np.array(tokens_inv['input_ids'])  # 设置反向关系文本数组


        
        #print(rel_words)  # 打印关系词列表（注释掉的调试代码）
        #print(len(rel_words), len(self.relation2id))  # 打印关系词列表长度和关系ID字典长度（注释掉的调试代码）
        assert len(rel_words) == len(self.relation2id)  # 断言关系词列表长度等于关系ID字典长度
        #print(self.rel_texts, self.max_rel_words)  # 打印关系文本数组和最大关系词数（注释掉的调试代码）

    def create_kb_adj_mats(self, sample_id):
        """
        Re-build local adj mats if we have data_eff == True (they are not pre-stored).
        """
        sample = self.data[sample_id]  # 获取指定样本的数据
        g2l = self.global2local_entity_maps[sample_id]  # 获取全局到局部实体的映射
    
        # build connection between question and entities in it
        head_list = []  # 初始化头实体列表
        rel_list = []  # 初始化关系列表
        tail_list = []  # 初始化尾实体列表
        for i, tpl in enumerate(sample['subgraph']['tuples']):  # 遍历子图中的三元组
            sbj, rel, obj = tpl  # 获取三元组中的主体、关系和客体
            try:
                if isinstance(sbj, dict) and 'text' in sbj:  # 如果主体是字典且包含'text'键
                    head = g2l[self.entity2id[sbj['text']]]  # 获取头实体ID
                    rel = self.relation2id[rel['text']]  # 获取关系ID
                    tail = g2l[self.entity2id[obj['text']]]  # 获取尾实体ID
                else:
                    head = g2l[self.entity2id[sbj]]  # 获取头实体ID
                    rel = self.relation2id[rel]  # 获取关系ID
                    tail = g2l[self.entity2id[obj]]  # 获取尾实体ID
            except:
                head = g2l[sbj]  # 如果异常则使用原始头实体ID
                try:
                    rel = int(rel)  # 尝试将关系转换为整数
                except:
                    rel = self.relation2id[rel]  # 如果异常则获取关系ID
                tail = g2l[obj]  # 获取尾实体ID
            head_list.append(head)  # 添加到头实体列表
            rel_list.append(rel)  # 添加到关系列表
            tail_list.append(tail)  # 添加到尾实体列表
            if self.use_inverse_relation:  # 如果使用反向关系
                head_list.append(tail)  # 添加尾实体到头实体列表
                rel_list.append(rel + len(self.relation2id))  # 添加反向关系到关系列表
                tail_list.append(head)  # 添加头实体到尾实体列表
    
        return np.array(head_list, dtype=int), np.array(rel_list, dtype=int), np.array(tail_list, dtype=int)  # 返回头实体、关系和尾实体的数组

    def _build_fact_mat(self, sample_ids, fact_dropout):
        """
        Creates local adj mats that contain entities, relations, and structure.
        """
        batch_heads = np.array([], dtype=int)  # 初始化批量头实体数组
        batch_rels = np.array([], dtype=int)  # 初始化批量关系数组
        batch_tails = np.array([], dtype=int)  # 初始化批量尾实体数组
        batch_ids = np.array([], dtype=int)  # 初始化批量ID数组
        #print(sample_ids)
        for i, sample_id in enumerate(sample_ids):  # 遍历样本ID列表
            index_bias = i * self.max_local_entity  # 计算索引偏移量
            if self.data_eff:  # 如果数据效率标志为True
                head_list, rel_list, tail_list = self.create_kb_adj_mats(sample_id)  # 调用create_kb_adj_mats方法获取头实体、关系和尾实体
            else:
                (head_list, rel_list, tail_list) = self.kb_adj_mats[sample_id]  # 从kb_adj_mats中获取头实体、关系和尾实体
            num_fact = len(head_list)  # 获取事实数量
            num_keep_fact = int(np.floor(num_fact * (1 - fact_dropout)))  # 计算保留的事实数量
            mask_index = np.random.permutation(num_fact)[: num_keep_fact]  # 随机选择保留的事实索引
    
            real_head_list = head_list[mask_index] + index_bias  # 添加索引偏移量到头实体
            real_tail_list = tail_list[mask_index] + index_bias  # 添加索引偏移量到尾实体
            real_rel_list = rel_list[mask_index]  # 获取保留的关系
            batch_heads = np.append(batch_heads, real_head_list)  # 将保留的头实体添加到批量头实体数组
            batch_rels = np.append(batch_rels, real_rel_list)  # 将保留的关系添加到批量关系数组
            batch_tails = np.append(batch_tails, real_tail_list)  # 将保留的尾实体添加到批量尾实体数组
            batch_ids = np.append(batch_ids, np.full(len(mask_index), i, dtype=int))  # 将样本ID添加到批量ID数组
            if self.use_self_loop:  # 如果使用自环
                num_ent_now = len(self.global2local_entity_maps[sample_id])  # 获取当前样本的实体数量
                ent_array = np.array(range(num_ent_now), dtype=int) + index_bias  # 创建实体数组并添加索引偏移量
                rel_array = np.array([self.num_kb_relation - 1] * num_ent_now, dtype=int)  # 创建关系数组
                batch_heads = np.append(batch_heads, ent_array)  # 将实体数组添加到批量头实体数组
                batch_tails = np.append(batch_tails, ent_array)  # 将实体数组添加到批量尾实体数组
                batch_rels = np.append(batch_rels, rel_array)  # 将关系数组添加到批量关系数组
                batch_ids = np.append(batch_ids, np.full(num_ent_now, i, dtype=int))  # 将样本ID添加到批量ID数组
        fact_ids = np.array(range(len(batch_heads)), dtype=int)  # 创建事实ID数组
        head_rels_ids = zip(batch_heads, batch_rels)  # 创建头实体和关系的组合
        head_count = Counter(batch_heads)  # 统计头实体出现次数
        # tail_count = Counter(batch_tails)
        weight_list = [1.0 / head_count[head] for head in batch_heads]  # 计算权重列表
    
        head_rels_batch = list(zip(batch_heads, batch_rels))  # 创建头实体和关系的组合列表
        #print(head_rels_batch)
        head_rels_count = Counter(head_rels_batch)  # 统计头实体和关系组合出现次数
        weight_rel_list = [1.0 / head_rels_count[(h, r)] for (h, r) in head_rels_batch]  # 计算权重列表
    
        #print(head_rels_count)
    
        # tail_count = Counter(batch_tails)
    
        # entity2fact_index = torch.LongTensor([batch_heads, fact_ids])
        # entity2fact_val = torch.FloatTensor(weight_list)
        # entity2fact_mat = torch.sparse.FloatTensor(entity2fact_index, entity2fact_val, torch.Size(
        #     [len(sample_ids) * self.max_local_entity, len(batch_heads)]))
        return batch_heads, batch_rels, batch_tails, batch_ids, fact_ids, weight_list, weight_rel_list  # 返回批量头实体、关系、尾实体、ID、事实ID、权重列表和关系权重列表

    def reset_batches(self, is_sequential=True):
        if is_sequential:
            self.batches = np.arange(self.num_data)  # 如果is_sequential为True，则按顺序排列批次
        else:
            self.batches = np.random.permutation(self.num_data)  # 否则随机排列批次

    def _build_global2local_entity_maps(self):
        """Create a map from global entity id to local entity of each sample"""
        global2local_entity_maps = [None] * self.num_data  # 初始化全局到局部实体映射列表
        total_local_entity = 0.0  # 初始化总局部实体数量
        next_id = 0  # 初始化下一个样本的ID
        for sample in tqdm(self.data):  # 遍历每个样本
            g2l = dict()  # 初始化当前样本的全局到局部实体映射字典
            if 'entities_cid' in sample:
                self._add_entity_to_map(self.entity2id, sample['entities_cid'], g2l)  # 如果样本中有'entities_cid'，则添加到映射
            else:
                self._add_entity_to_map(self.entity2id, sample['entities'], g2l)  # 否则添加'samples'中的实体到映射
            # self._add_entity_to_map(self.entity2id, sample['entities'], g2l)  # 备注：这条代码被注释掉了，功能与上面一行重复
            # construct a map from global entity id to local entity id
            self._add_entity_to_map(self.entity2id, sample['subgraph']['entities'], g2l)  # 添加子图中的实体到映射

            global2local_entity_maps[next_id] = g2l  # 将当前样本的映射添加到全局映射列表
            total_local_entity += len(g2l)  # 更新总局部实体数量
            self.max_local_entity = max(self.max_local_entity, len(g2l))  # 更新最大局部实体数量
            next_id += 1  # 增加下一个样本的ID
        print('avg local entity: ', total_local_entity / next_id)  # 打印平均局部实体数量
        print('max local entity: ', self.max_local_entity)  # 打印最大局部实体数量
        return global2local_entity_maps  # 返回全局到局部实体映射列表

    @staticmethod
    def _add_entity_to_map(entity2id, entities, g2l):
        # print(entities)  # 打印实体列表（调试代码）
        # print(entity2id)  # 打印实体到ID的映射（调试代码）
        for entity_global_id in entities:  # 遍历每个全局实体ID
            try:
                if isinstance(entity_global_id, dict) and 'text' in entity_global_id:
                    ent = entity2id[entity_global_id['text']]  # 如果实体ID是字典且包含'text'键，则获取其对应的ID
                else:
                    ent = entity2id[entity_global_id]  # 否则直接获取实体ID
                if ent not in g2l:
                    g2l[ent] = len(g2l)  # 如果实体ID不在局部映射中，则添加到映射
            except:
                if entity_global_id not in g2l:
                    g2l[entity_global_id] = len(g2l)  # 如果捕获异常且实体ID不在局部映射中，则添加到映射

    def deal_q_type(self, q_type=None):
        sample_ids = self.sample_ids  # 获取样本ID列表
        if q_type is None:
            q_type = self.q_type  # 如果未指定q_type，则使用默认值
        if q_type == "seq":
            q_input = self.query_texts[sample_ids]  # 如果q_type为'seq'，则获取查询文本
        else:
            raise NotImplementedError  # 否则抛出未实现错误

        return q_input  # 返回查询输入



class SingleDataLoader(BasicDataLoader):
    """
    Single Dataloader creates training/eval batches during KGQA.
    """
    def __init__(self, config, word2id, relation2id, entity2id, tokenize, data_type="train"):
        super(SingleDataLoader, self).__init__(config, word2id, relation2id, entity2id, tokenize, data_type)
        
    def get_batch(self, iteration, batch_size, fact_dropout, q_type=None, test=False):
        """
        Returns a batch of data for training or evaluation.
        """
        start = iteration * batch_size
        end = min(start + batch_size, self.num_data)
        
        # 获取批次数据
        batch_question_id = self.question_id[start:end]
        batch_query_entities = self.query_entities[start:end]
        batch_candidate_entities = self.candidate_entities[start:end]
        batch_seed_distribution = self.seed_distribution[start:end]
        batch_seed_list = self.seed_list[start:end]
        batch_kb_fact_rels = self.kb_fact_rels[start:end]
        
        # 根据tokenize类型处理查询文本
        if self.tokenize == 'hybrid':
            # Hybrid模式：返回BERT和LSTM两种tokenization
            batch_query_texts = (self.query_texts[start:end], self.lstm_query_texts[start:end])
        else:
            # 其他模式：返回单一tokenization
            batch_query_texts = self.query_texts[start:end]
        
        # 构建批次特定的数据结构
        batch_global2local_entity_maps = self.global2local_entity_maps[start:end]
        batch_kb_adj_mats = self.kb_adj_mats[start:end]
        
        # 计算批次的最大本地实体数
        max_local_entity = max(len(g2l) for g2l in batch_global2local_entity_maps)
        
        # 构建批次邻接矩阵
        batch_adj_mats = []
        for i, g2l in enumerate(batch_global2local_entity_maps):
            if self.data_eff:
                # 动态构建邻接矩阵
                adj_mat = self.create_kb_adj_mats(start + i)
            else:
                # 使用预构建的邻接矩阵
                adj_mat = batch_kb_adj_mats[i]
            batch_adj_mats.append(adj_mat)
        
        # 构建稀疏邻接矩阵
        if self.data_eff:
            batch_sparse_adj_mats = self._build_fact_mat(range(start, end), fact_dropout)
        else:
            batch_sparse_adj_mats = [self._build_fact_mat([i], fact_dropout) for i in range(start, end)]
        
        return {
            'question_id': batch_question_id,
            'query_texts': batch_query_texts,
            'query_entities': batch_query_entities,
            'candidate_entities': batch_candidate_entities,
            'seed_distribution': batch_seed_distribution,
            'seed_list': batch_seed_list,
            'kb_fact_rels': batch_kb_fact_rels,
            'global2local_entity_maps': batch_global2local_entity_maps,
            'kb_adj_mats': batch_adj_mats,
            'sparse_adj_mats': batch_sparse_adj_mats,
            'max_local_entity': max_local_entity
        }



def load_dict(filename):
    word2id = dict()  # 初始化一个空字典用于存储单词到ID的映射
    with open(filename, encoding='utf-8') as f_in:  # 以UTF-8编码打开文件
        for line in f_in:  # 逐行读取文件内容
            word = line.strip()  # 去除行首行尾的空白字符
            word2id[word] = len(word2id)  # 将单词添加到字典中，ID为当前字典长度
    return word2id  # 返回构建好的字典

def load_dict_int(filename):
    word2id = dict()  # 初始化一个空字典用于存储整数到ID的映射
    with open(filename, encoding='utf-8') as f_in:  # 以UTF-8编码打开文件
        for line in f_in:  # 逐行读取文件内容
            word = line.strip()  # 去除行首行尾的空白字符
            word2id[int(word)] = int(word)  # 将整数添加到字典中，ID为整数值
    return word2id  # 返回构建好的字典

def load_data(config, tokenize):

    """
    Creates train/val/test dataloaders (seperately).
    """
    '''
    检查 config['data_folder'] 中是否包含 'sr-cwq'。
    如果包含，则调用 load_dict_int 加载实体到 ID 的映射表；否则调用 load_dict。
    load_dict_int 和 load_dict 是两个不同的函数，分别用于加载整数键值对或字符串键值对的字典。
    '''
    if 'sr-cwq' in config['data_folder']:
        entity2id = load_dict_int(config['data_folder'] + config['entity2id'])
    else:
        entity2id = load_dict(config['data_folder'] + config['entity2id'])
    '''
    使用 load_dict 函数加载单词到 ID 的映射表 (word2id) 和关系到 ID 的映射表 (relation2id)。
    '''
    word2id = load_dict(config['data_folder'] + config['word2id'])
    relation2id = load_dict(config['data_folder'] + config['relation2id'])
    '''
    根据 config["is_eval"] 的值决定是否处于评估模式：
    如果是评估模式 (is_eval=True)，则不加载训练数据 (train_data=None)，仅加载验证集 (valid_data) 和测试集 (test_data)。
    如果不是评估模式，则加载训练集、验证集和测试集。SingleDataLoader 是一个类，用于加载单个数据集。
    num_word 是从测试数据或训练数据中获取的词汇数量。
    '''
    if config["is_eval"]:
        train_data = None
        valid_data = SingleDataLoader(config, word2id, relation2id, entity2id, tokenize, data_type="dev")
        test_data = SingleDataLoader(config, word2id, relation2id, entity2id, tokenize, data_type="test")
        num_word = test_data.num_word
    else:
        train_data = SingleDataLoader(config, word2id, relation2id, entity2id, tokenize, data_type="train")
        valid_data = SingleDataLoader(config, word2id, relation2id, entity2id, tokenize, data_type="dev")
        test_data = SingleDataLoader(config, word2id, relation2id, entity2id, tokenize, data_type="test")
        num_word = train_data.num_word
    '''
    从测试数据中提取关系文本 (relation_texts) 和反向关系文本 (relation_texts_inv)。
    entities_texts 被设置为 None，表示没有实体文本。
    '''
    relation_texts = test_data.rel_texts
    relation_texts_inv = test_data.rel_texts_inv
    entities_texts = None
    dataset = {
        "train": train_data,
        "valid": valid_data,
        "test": test_data, #test_data,
        "entity2id": entity2id,
        "relation2id": relation2id,
        "word2id": word2id,
        "num_word": num_word,
        "rel_texts": relation_texts,
        "rel_texts_inv": relation_texts_inv,
        "ent_texts": entities_texts
    }
    return dataset


if __name__ == "__main__":
    st = time.time()
    args = get_config()
    load_data(args)
