{"id": "WebQTrn-9", "dep": [["how", "WRB", "2", "advmod"], ["old", "J<PERSON>", "3", "dep"], ["is", "VBZ", "0", "ROOT"], ["sacha", "NN", "6", "compound"], ["baron", "NN", "6", "compound"], ["cohen", "NN", "3", "nsubj"]], "question": "how old is sacha baron cohen"}
{"id": "WebQTrn-11", "dep": [["what", "WDT", "2", "det"], ["time", "NN", "0", "ROOT"], ["zone", "NN", "2", "dep"], ["am", "VBP", "3", "dep"], ["i", "LS", "4", "root"], ["in", "IN", "8", "case"], ["cleveland", "NN", "8", "compound"], ["ohio", "NN", "5", "nmod"]], "question": "what time zone am i in cleveland ohio"}
{"id": "WebQTrn-15", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["nina", "NNP", "5", "compound"], ["dobrev", "NNP", "5", "compound"], ["nationality", "NN", "1", "nsubj"]], "question": "what is nina dobrev nationality"}
{"id": "WebQTrn-47", "dep": [["what", "WDT", "2", "det"], ["county", "NN", "3", "dobj"], ["is", "VBZ", "0", "ROOT"], ["heathrow", "NN", "5", "compound"], ["airport", "NN", "3", "nsubj"], ["in", "IN", "3", "dep"]], "question": "what county is heathrow airport in"}
{"id": "WebQTrn-73", "dep": [["what", "WDT", "2", "det"], ["form", "NN", "6", "nsubjpass"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["was", "VBD", "6", "auxpass"], ["practiced", "VBN", "0", "ROOT"], ["in", "IN", "8", "case"], ["sparta", "NN", "6", "nmod"]], "question": "what form of government was practiced in sparta"}
{"id": "WebQTrn-104", "dep": [["who", "WP", "7", "dobj"], ["was", "VBD", "1", "cop"], ["the", "DT", "4", "det"], ["president", "NN", "7", "nsubj"], ["after", "IN", "6", "case"], ["jfk", "NN", "4", "nmod"], ["died", "VBD", "0", "ROOT"]], "question": "who was the president after jfk died"}
{"id": "WebQTrn-126", "dep": [["where", "WRB", "3", "advmod"], ["to", "TO", "3", "mark"], ["visit", "VB", "0", "ROOT"], ["near", "IN", "5", "case"], ["bangkok", "NN", "3", "nmod"]], "question": "where to visit near bangkok"}
{"id": "WebQTrn-143", "dep": [["what", "WP", "0", "ROOT"], ["did", "VBD", "1", "root"], ["nick", "NNP", "5", "compound"], ["clegg", "NNP", "5", "compound"], ["study", "NN", "2", "dobj"], ["at", "IN", "7", "case"], ["university", "NN", "5", "nmod"]], "question": "what did nick clegg study at university"}
{"id": "WebQTrn-152", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["henry", "JJ", "4", "amod"], ["clay", "NN", "1", "nsubj"], ["known", "VBN", "4", "acl"], ["for", "IN", "5", "nmod"]], "question": "what is henry clay known for"}
{"id": "WebQTrn-164", "dep": [["where", "WRB", "3", "advmod"], ["was", "VBD", "3", "cop"], ["kennedy", "JJ", "0", "ROOT"], ["when", "WRB", "6", "advmod"], ["he", "PRP", "6", "nsubj"], ["got", "VBD", "3", "advcl"], ["shot", "NN", "6", "dobj"]], "question": "where was kennedy when he got shot"}
{"id": "WebQTrn-196", "dep": [["who", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["ben", "NNP", "4", "compound"], ["stiller", "NNP", "5", "nsubj"], ["play", "VB", "0", "ROOT"], ["in", "IN", "7", "case"], ["megamind", "NN", "5", "nmod"]], "question": "who did ben stiller play in megamind"}
{"id": "WebQTrn-206", "dep": [["who", "WP", "2", "nsubj"], ["won", "VBD", "0", "ROOT"], ["golden", "JJ", "4", "amod"], ["boot", "NN", "2", "dobj"]], "question": "who won golden boot"}
{"id": "WebQTrn-218", "dep": [["what", "WP", "0", "ROOT"], ["are", "VBP", "1", "cop"], ["all", "PDT", "5", "det:predet"], ["the", "DT", "5", "det"], ["names", "NNS", "1", "nsubj"], ["of", "IN", "8", "case"], ["harry", "NN", "8", "compound"], ["potter", "NN", "5", "nmod"]], "question": "what are all the names of harry potter"}
{"id": "WebQTrn-249", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["tia", "NN", "7", "compound"], ["and", "CC", "3", "cc"], ["tamera", "NN", "3", "conj"], ["mowry", "NN", "7", "compound"], ["parents", "NNS", "1", "nsubj"]], "question": "who is tia and tamera mowry parents"}
{"id": "WebQTrn-255", "dep": [["what", "WDT", "2", "det"], ["year", "NN", "7", "nsubj"], ["was", "VBD", "7", "cop"], ["the", "DT", "7", "det"], ["new", "JJ", "7", "amod"], ["york", "NN", "7", "compound"], ["blackout", "NN", "0", "ROOT"]], "question": "what year was the new york blackout"}
{"id": "WebQTrn-258", "dep": [["what", "WP", "0", "ROOT"], ["are", "VBP", "1", "cop"], ["major", "JJ", "4", "amod"], ["exports", "NNS", "1", "nsubj"], ["of", "IN", "7", "case"], ["the", "DT", "7", "det"], ["usa", "NN", "4", "nmod"]], "question": "what are major exports of the usa"}
{"id": "WebQTrn-267", "dep": [["what", "WDT", "3", "det"], ["other", "JJ", "3", "amod"], ["countries", "NNS", "0", "ROOT"], ["does", "VBZ", "3", "acl"], ["canada", "NN", "6", "compound"], ["trade", "NN", "4", "dobj"], ["with", "IN", "6", "dep"]], "question": "what other countries does canada trade with"}
{"id": "WebQTrn-279", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "4", "dobj"], ["they", "PRP", "4", "nsubj"], ["speak", "VBP", "0", "ROOT"], ["in", "IN", "7", "case"], ["the", "DT", "7", "det"], ["philippines", "NNS", "4", "nmod"]], "question": "what language they speak in the philippines"}
{"id": "WebQTrn-297", "dep": [["who", "WP", "5", "nsubj"], ["was", "VBD", "5", "cop"], ["real", "JJ", "5", "amod"], ["housewives", "NNS", "5", "compound"], ["brandi", "NNS", "0", "ROOT"], ["married", "VBN", "5", "acl"], ["to", "TO", "6", "nmod"]], "question": "who was real housewives brandi married to"}
{"id": "WebQTrn-326", "dep": [["what", "WDT", "3", "det"], ["time", "NN", "3", "compound"], ["zone", "NN", "6", "nsubj"], ["is", "VBZ", "6", "cop"], ["new", "JJ", "6", "amod"], ["york", "NN", "0", "ROOT"], ["under", "IN", "6", "dep"]], "question": "what time zone is new york under"}
{"id": "WebQTrn-328", "dep": [["what", "WP", "9", "dobj"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["name", "NN", "9", "nsubj"], ["of", "IN", "8", "case"], ["the", "DT", "8", "det"], ["book", "NN", "8", "compound"], ["hitler", "NN", "4", "nmod"], ["wrote", "VBD", "0", "ROOT"], ["while", "IN", "12", "case"], ["in", "IN", "12", "case"], ["prison", "NN", "9", "nmod"]], "question": "what is the name of the book hitler wrote while in prison"}
{"id": "WebQTrn-339", "dep": [["when", "WRB", "6", "advmod"], ["was", "VBD", "6", "auxpass"], ["the", "DT", "5", "det"], ["musical", "JJ", "5", "amod"], ["annie", "NN", "6", "nsubjpass"], ["written", "VBN", "0", "ROOT"]], "question": "when was the musical annie written"}
{"id": "WebQTrn-349", "dep": [["what", "WDT", "2", "det"], ["kind", "NN", "8", "dobj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["did", "VBD", "8", "aux"], ["benito", "NN", "7", "compound"], ["mussolini", "NNS", "8", "nsubj"], ["have", "VBP", "0", "ROOT"]], "question": "what kind of government did benito mussolini have"}
{"id": "WebQTrn-351", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["chelsea", "NN", "4", "compound"], ["kane", "NN", "1", "nsubj"]], "question": "who is chelsea kane"}
{"id": "WebQTrn-353", "dep": [["what", "WP", "0", "ROOT"], ["did", "VBD", "1", "root"], ["obama", "NN", "4", "compound"], ["study", "NN", "2", "dobj"], ["in", "IN", "6", "case"], ["school", "NN", "4", "nmod"]], "question": "what did obama study in school"}
{"id": "WebQTrn-358", "dep": [["what", "WDT", "2", "det"], ["movies", "NNS", "3", "nsubj"], ["has", "VBZ", "0", "ROOT"], ["taylor", "NN", "5", "compound"], ["lautner", "NN", "8", "nsubj"], ["been", "VBN", "8", "cop"], ["in", "IN", "8", "case"], ["2011", "CD", "3", "ccomp"]], "question": "what movies has taylor lautner been in 2011"}
{"id": "WebQTrn-361", "dep": [["what", "WDT", "2", "det"], ["company", "NN", "5", "dep"], ["did", "VBD", "5", "aux"], ["henry", "NNP", "5", "nsubj"], ["ford", "VBD", "0", "ROOT"], ["work", "NN", "5", "dobj"], ["for", "IN", "5", "dep"]], "question": "what company did henry ford work for"}
{"id": "WebQTrn-376", "dep": [["who", "WP", "2", "nsubj"], ["speaks", "VBZ", "0", "ROOT"], ["farsi", "NNS", "2", "dobj"]], "question": "who speaks farsi"}
{"id": "WebQTrn-398", "dep": [["what", "WDT", "2", "det"], ["states", "NNS", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["the", "DT", "7", "det"], ["missouri", "NN", "7", "compound"], ["river", "NN", "7", "compound"], ["touch", "NN", "3", "dobj"]], "question": "what states does the missouri river touch"}
{"id": "WebQTrn-414", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "6", "nsubj"], ["around", "IN", "5", "case"], ["the", "DT", "5", "det"], ["world", "NN", "2", "nmod"], ["speak", "VBP", "0", "ROOT"], ["french", "JJ", "6", "xcomp"]], "question": "what countries around the world speak french"}
{"id": "WebQTrn-418", "dep": [["where", "WRB", "5", "advmod"], ["was", "VBD", "5", "cop"], ["john", "NN", "5", "compound"], ["lennon", "JJ", "5", "amod"], ["standing", "NN", "0", "ROOT"], ["when", "WRB", "9", "advmod"], ["he", "PRP", "9", "nsubjpass"], ["was", "VBD", "9", "auxpass"], ["shot", "VBN", "5", "advcl"]], "question": "where was john lennon standing when he was shot"}
{"id": "WebQTrn-428", "dep": [["who", "WP", "5", "dep"], ["was", "VBD", "5", "auxpass"], ["judy", "JJ", "4", "amod"], ["garland", "NN", "5", "nsubjpass"], ["married", "VBN", "0", "ROOT"], ["to", "TO", "5", "nmod"]], "question": "who was judy garland married to"}
{"id": "WebQTrn-429", "dep": [["where", "WRB", "5", "advmod"], ["is", "VBZ", "5", "cop"], ["abraham", "NN", "5", "compound"], ["lincoln", "NN", "5", "compound"], ["hometown", "NN", "0", "ROOT"]], "question": "where is abraham lincoln hometown"}
{"id": "WebQTrn-430", "dep": [["what", "WDT", "2", "det"], ["disease", "NN", "0", "ROOT"], ["did", "VBD", "2", "dep"], ["helen", "NNP", "5", "compound"], ["keller", "NNP", "3", "dobj"]], "question": "what disease did helen keller"}
{"id": "WebQTrn-442", "dep": [["who", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["the", "DT", "5", "det"], ["first", "JJ", "5", "amod"], ["leader", "NN", "1", "nsubj"], ["of", "IN", "8", "case"], ["the", "DT", "8", "det"], ["afl", "NN", "5", "nmod"]], "question": "who was the first leader of the afl"}
{"id": "WebQTrn-443", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "7", "det"], ["arizona", "NN", "7", "compound"], ["cardinals", "NNS", "7", "compound"], ["football", "NN", "7", "compound"], ["coach", "NN", "1", "nsubj"]], "question": "who is the arizona cardinals football coach"}
{"id": "WebQTrn-457", "dep": [["who", "WP", "2", "nsubj"], ["did", "VBD", "0", "ROOT"], ["ayrton", "NN", "5", "compound"], ["senna", "NN", "5", "compound"], ["drive", "NN", "2", "dobj"], ["for", "IN", "2", "dep"]], "question": "who did ayrton senna drive for"}
{"id": "WebQTrn-459", "dep": [["where", "WRB", "2", "advmod"], ["is", "VBZ", "0", "ROOT"], ["the", "DT", "5", "det"], ["best", "JJS", "5", "amod"], ["place", "NN", "2", "nsubj"], ["to", "TO", "7", "case"], ["vacation", "NN", "5", "nmod"], ["in", "IN", "11", "case"], ["the", "DT", "11", "det"], ["dominican", "JJ", "11", "amod"], ["republic", "NN", "5", "nmod"]], "question": "where is the best place to vacation in the dominican republic"}
{"id": "WebQTrn-483", "dep": [["what", "WDT", "2", "det"], ["type", "NN", "8", "dobj"], ["of", "IN", "4", "case"], ["music", "NN", "2", "nmod"], ["did", "VBD", "8", "aux"], ["richard", "NNP", "7", "compound"], ["wagner", "NNP", "8", "nsubj"], ["play", "VB", "0", "ROOT"]], "question": "what type of music did richard wagner play"}
{"id": "WebQTrn-497", "dep": [["in", "IN", "3", "case"], ["what", "WDT", "3", "det"], ["country", "NN", "4", "nmod"], ["is", "VBZ", "0", "ROOT"], ["amsterdam", "NNP", "4", "nsubj"]], "question": "in what country is amsterdam"}
{"id": "WebQTrn-534", "dep": [["who", "WP", "5", "dobj"], ["does", "VBZ", "5", "aux"], ["michael", "NNP", "4", "compound"], ["vick", "NNP", "5", "nsubj"], ["play", "VB", "0", "ROOT"], ["for", "IN", "5", "nmod"]], "question": "who does michael vick play for"}
{"id": "WebQTrn-536", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["anderson", "NN", "4", "compound"], ["silva", "NN", "1", "nsubj"], ["trained", "VBN", "4", "acl"], ["in", "IN", "5", "nmod"]], "question": "what is anderson silva trained in"}
{"id": "WebQTrn-557", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["dorothy", "NNP", "2", "dobj"], ["in", "IN", "6", "case"], ["the", "DT", "6", "det"], ["wizard", "NN", "2", "nmod"], ["of", "IN", "9", "case"], ["oz", "NN", "9", "compound"], ["movie", "NN", "6", "nmod"]], "question": "who played dorothy in the wizard of oz movie"}
{"id": "WebQTrn-566", "dep": [["what", "WDT", "2", "det"], ["style", "NN", "6", "nsubj"], ["of", "IN", "4", "case"], ["art", "NN", "2", "nmod"], ["does", "VBZ", "6", "aux"], ["andy", "VB", "0", "ROOT"], ["warhol", "NNP", "6", "dobj"], ["do", "VB", "6", "dep"]], "question": "what style of art does andy warhol do"}
{"id": "WebQTrn-579", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["luke", "NN", "4", "compound"], ["skywalker", "NN", "2", "dobj"], ["in", "IN", "8", "case"], ["star", "NN", "8", "compound"], ["wars", "NNS", "8", "compound"], ["episode", "NN", "2", "nmod"], ["4", "CD", "8", "nummod"]], "question": "who played luke skywalker in star wars episode 4"}
{"id": "WebQTrn-608", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["shakira", "NN", "5", "compound"], ["s", "NNS", "5", "compound"], ["nationality", "NN", "1", "nsubj"]], "question": "what is shakira 's nationality"}
{"id": "WebQTrn-611", "dep": [["what", "WDT", "2", "det"], ["position", "NN", "6", "dobj"], ["did", "VBD", "6", "aux"], ["george", "NN", "5", "compound"], ["washington", "NNP", "6", "nsubj"], ["serve", "VB", "0", "ROOT"], ["in", "IN", "10", "case"], ["the", "DT", "10", "det"], ["constitutional", "JJ", "10", "amod"], ["convention", "NN", "6", "nmod"]], "question": "what position did george washington serve in the constitutional convention"}
{"id": "WebQTrn-639", "dep": [["where", "WRB", "4", "advmod"], ["do", "VBP", "4", "aux"], ["audi", "NNS", "4", "nsubj"], ["come", "VB", "0", "ROOT"], ["from", "IN", "4", "nmod"]], "question": "where do audi come from"}
{"id": "WebQTrn-700", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["juni", "NNS", "2", "dobj"], ["in", "IN", "6", "case"], ["spy", "NN", "6", "compound"], ["kids", "NNS", "2", "nmod"], ["4", "CD", "6", "nummod"]], "question": "who played juni in spy kids 4"}
{"id": "WebQTrn-701", "dep": [["what", "WDT", "2", "det"], ["year", "NN", "8", "nmod:tmod"], ["did", "VBD", "8", "aux"], ["the", "DT", "7", "det"], ["new", "JJ", "7", "amod"], ["york", "NN", "7", "compound"], ["mets", "NNS", "8", "nsubj"], ["start", "VBP", "0", "ROOT"]], "question": "what year did the new york mets start"}
{"id": "WebQTrn-722", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "6", "det"], ["current", "JJ", "6", "amod"], ["government", "NN", "6", "compound"], ["system", "NN", "1", "nsubj"], ["in", "IN", "8", "case"], ["france", "NN", "6", "nmod"]], "question": "what is the current government system in france"}
{"id": "WebQTrn-724", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["calabria", "NN", "1", "nsubj"], ["italy", "RB", "1", "advmod"]], "question": "what is calabria italy"}
{"id": "WebQTrn-733", "dep": [["who", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["heinrich", "NNP", "4", "compound"], ["himmler", "NNP", "5", "nsubj"], ["marry", "VB", "0", "ROOT"]], "question": "who did heinrich himmler marry"}
{"id": "WebQTrn-741", "dep": [["who", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["carrie", "NN", "4", "compound"], ["underwood", "NN", "1", "nsubj"], ["in", "IN", "7", "case"], ["soul", "NN", "7", "compound"], ["surfer", "NN", "4", "nmod"]], "question": "who was carrie underwood in soul surfer"}
{"id": "WebQTrn-747", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["venus", "NN", "1", "nsubj"], ["named", "VBN", "3", "acl"], ["for", "IN", "4", "nmod"]], "question": "what is venus named for"}
{"id": "WebQTrn-788", "dep": [["what", "WDT", "2", "det"], ["clubs", "NNS", "3", "nsubj"], ["has", "VBZ", "0", "ROOT"], ["peter", "NN", "5", "compound"], ["crouch", "NN", "6", "nsubj"], ["played", "VBD", "3", "ccomp"], ["for", "IN", "6", "nmod"]], "question": "what clubs has peter crouch played for"}
{"id": "WebQTrn-793", "dep": [["who", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["michael", "NNP", "4", "compound"], ["jordan", "NNP", "5", "nsubj"], ["marry", "VB", "0", "ROOT"]], "question": "who did michael jordan marry"}
{"id": "WebQTrn-810", "dep": [["what", "WDT", "2", "det"], ["date", "NN", "8", "nmod:tmod"], ["did", "VBD", "8", "aux"], ["the", "DT", "7", "det"], ["san", "NN", "7", "compound"], ["francisco", "NN", "7", "compound"], ["giants", "NNS", "8", "nsubj"], ["win", "VBP", "0", "ROOT"], ["the", "DT", "11", "det"], ["world", "NN", "11", "compound"], ["series", "NN", "8", "dobj"]], "question": "what date did the san francisco giants win the world series"}
{"id": "WebQTrn-844", "dep": [["what", "WDT", "3", "det"], ["3", "CD", "3", "nummod"], ["states", "NNS", "4", "nsubj"], ["border", "VBP", "0", "ROOT"], ["the", "DT", "7", "det"], ["pacific", "JJ", "7", "amod"], ["ocean", "NN", "4", "dobj"]], "question": "what 3 states border the pacific ocean"}
{"id": "WebQTrn-846", "dep": [["where", "WRB", "5", "advmod"], ["was", "VBD", "5", "auxpass"], ["avril", "NN", "4", "compound"], ["lavigne", "NN", "5", "nsubjpass"], ["born", "VBN", "0", "ROOT"]], "question": "where was avril lavigne born"}
{"id": "WebQTrn-858", "dep": [["which", "WDT", "2", "det"], ["countries", "NNS", "6", "nsubj"], ["does", "VBZ", "6", "aux"], ["south", "RB", "5", "advmod"], ["africa", "JJ", "6", "amod"], ["export", "NN", "0", "ROOT"], ["to", "TO", "6", "dep"]], "question": "which countries does south africa export to"}
{"id": "WebQTrn-868", "dep": [["what", "WP", "0", "ROOT"], ["a", "DT", "3", "det"], ["city", "NN", "1", "nsubj"], ["in", "IN", "5", "case"], ["montana", "FW", "3", "nmod"]], "question": "what a city in montana"}
{"id": "WebQTrn-889", "dep": [["who", "WP", "2", "nsubj"], ["play", "VBP", "0", "ROOT"], ["luke", "NN", "4", "compound"], ["skywalker", "NN", "2", "dobj"]], "question": "who play luke skywalker"}
{"id": "WebQTrn-891", "dep": [["where", "WRB", "0", "ROOT"], ["did", "VBD", "1", "root"], ["john", "NN", "2", "dobj"], ["howard", "JJ", "5", "amod"], ["live", "JJ", "3", "dep"]], "question": "where did john howard live"}
{"id": "WebQTrn-901", "dep": [["who", "WP", "4", "nsubj"], ["was", "VBD", "4", "cop"], ["judy", "JJ", "4", "amod"], ["collins", "NNS", "0", "ROOT"], ["married", "VBN", "4", "acl"], ["to", "TO", "5", "nmod"]], "question": "who was judy collins married to"}
{"id": "WebQTrn-907", "dep": [["what", "WP", "6", "dobj"], ["did", "VBD", "6", "aux"], ["nick", "NNP", "5", "compound"], ["carter", "NNP", "5", "compound"], ["sister", "NN", "6", "nsubj"], ["died", "VBD", "0", "ROOT"], ["of", "IN", "6", "nmod"]], "question": "what did nick carter sister died of"}
{"id": "WebQTrn-931", "dep": [["where", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["emily", "NNP", "4", "compound"], ["murphy", "NNP", "5", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "7", "case"], ["school", "NN", "5", "nmod"]], "question": "where did emily murphy go to school"}
{"id": "WebQTrn-938", "dep": [["what", "WP", "6", "dobj"], ["else", "RB", "1", "advmod"], ["did", "VBD", "6", "aux"], ["eli", "NNS", "6", "nsubj"], ["whitney", "JJ", "6", "amod"], ["invent", "VB", "0", "ROOT"]], "question": "what else did eli whitney invent"}
{"id": "WebQTrn-942", "dep": [["where", "WRB", "3", "advmod"], ["to", "TO", "3", "mark"], ["travel", "VB", "0", "ROOT"], ["around", "IN", "5", "case"], ["sydney", "NN", "3", "nmod"]], "question": "where to travel around sydney"}
{"id": "WebQTrn-947", "dep": [["where", "WRB", "0", "ROOT"], ["is", "VBZ", "1", "dep"], ["the", "DT", "5", "det"], ["head", "NN", "5", "compound"], ["office", "NN", "2", "nsubj"], ["of", "IN", "8", "case"], ["hsbc", "NN", "8", "compound"], ["bank", "NN", "5", "nmod"]], "question": "where is the head office of hsbc bank"}
{"id": "WebQTrn-956", "dep": [["what", "WDT", "2", "det"], ["year", "NN", "6", "nmod:tmod"], ["did", "VBD", "6", "aux"], ["florida", "NN", "5", "compound"], ["marlins", "NNS", "6", "nsubj"], ["win", "VBP", "0", "ROOT"], ["the", "DT", "9", "det"], ["world", "NN", "9", "compound"], ["series", "NN", "6", "dobj"]], "question": "what year did florida marlins win the world series"}
{"id": "WebQTrn-957", "dep": [["what", "WDT", "2", "det"], ["movie", "NN", "6", "dep"], ["did", "VBD", "6", "aux"], ["marlee", "NNP", "5", "compound"], ["matlin", "NNP", "6", "nsubj"], ["won", "VBD", "0", "ROOT"], ["an", "DT", "9", "det"], ["academy", "NN", "9", "compound"], ["award", "NN", "6", "dobj"], ["for", "IN", "9", "acl"]], "question": "what movie did marlee matlin won an academy award for"}
{"id": "WebQTrn-969", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["head", "NN", "5", "compound"], ["coach", "NN", "1", "nsubj"], ["of", "IN", "9", "case"], ["the", "DT", "9", "det"], ["tennessee", "NN", "9", "compound"], ["titans", "NNS", "5", "nmod"]], "question": "who is the head coach of the tennessee titans"}
{"id": "WebQTrn-1019", "dep": [["where", "WRB", "5", "advmod"], ["is", "VBZ", "5", "auxpass"], ["the", "DT", "4", "det"], ["mozambique", "NN", "5", "nsubjpass"], ["located", "JJ", "0", "ROOT"]], "question": "where is the mozambique located"}
{"id": "WebQTrn-1023", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["money", "NN", "1", "nsubj"], ["of", "IN", "6", "case"], ["argentina", "NN", "4", "nmod"], ["called", "VBN", "6", "acl"]], "question": "what is the money of argentina called"}
{"id": "WebQTrn-1025", "dep": [["what", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["bella", "NN", "4", "compound"], ["abzug", "NN", "5", "nsubj"], ["do", "VBP", "0", "ROOT"]], "question": "what did bella abzug do"}
{"id": "WebQTrn-1026", "dep": [["what", "WDT", "2", "det"], ["sights", "NNS", "4", "nsubj"], ["to", "TO", "4", "mark"], ["see", "VB", "0", "ROOT"], ["in", "IN", "6", "case"], ["madrid", "NN", "4", "nmod"]], "question": "what sights to see in madrid"}
{"id": "WebQTrn-1041", "dep": [["what", "WDT", "2", "det"], ["money", "NN", "4", "nsubjpass"], ["is", "VBZ", "4", "auxpass"], ["used", "VBN", "0", "ROOT"], ["in", "IN", "7", "case"], ["the", "DT", "7", "det"], ["maldives", "NNS", "4", "nmod"]], "question": "what money is used in the maldives"}
{"id": "WebQTrn-1053", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["liam", "NN", "4", "compound"], ["neeson", "NN", "6", "compound"], ["s", "VBZ", "6", "compound"], ["character", "NN", "1", "nsubj"], ["in", "IN", "9", "case"], ["star", "NN", "9", "compound"], ["wars", "NNS", "6", "nmod"]], "question": "what was liam neeson 's character in star wars"}
{"id": "WebQTrn-1054", "dep": [["who", "WP", "2", "nsubj"], ["plays", "VBZ", "0", "ROOT"], ["the", "DT", "4", "det"], ["voice", "NN", "2", "dobj"], ["of", "IN", "7", "case"], ["lois", "NN", "7", "compound"], ["griffin", "NN", "4", "nmod"]], "question": "who plays the voice of lois griffin"}
{"id": "WebQTrn-1067", "dep": [["who", "WP", "0", "ROOT"], ["are", "VBP", "1", "cop"], ["senators", "NNS", "1", "nsubj"], ["from", "IN", "6", "case"], ["new", "JJ", "6", "amod"], ["jersey", "NN", "3", "nmod"]], "question": "who are senators from new jersey"}
{"id": "WebQTrn-1069", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "0", "ROOT"], ["do", "VBP", "2", "dep"], ["denmark", "NN", "3", "dobj"]], "question": "what language do denmark"}
{"id": "WebQTrn-1084", "dep": [["what", "WDT", "2", "det"], ["timezone", "NN", "6", "nsubj"], ["is", "VBZ", "6", "cop"], ["texas", "JJ", "6", "amod"], ["san", "NN", "6", "compound"], ["antonio", "NN", "0", "ROOT"], ["in", "IN", "6", "dep"]], "question": "what timezone is texas san antonio in"}
{"id": "WebQTrn-1091", "dep": [["when", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["the", "DT", "4", "det"], ["raiders", "NNS", "5", "nsubj"], ["win", "VBP", "0", "ROOT"], ["the", "DT", "7", "det"], ["superbowl", "NN", "5", "dobj"]], "question": "when did the raiders win the superbowl"}
{"id": "WebQTrn-1109", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["currency", "NN", "1", "nsubj"], ["used", "VBN", "4", "acl"], ["in", "IN", "7", "case"], ["tunisia", "NN", "5", "nmod"]], "question": "what is the currency used in tunisia"}
{"id": "WebQTrn-1110", "dep": [["what", "WDT", "3", "det"], ["language", "NN", "3", "compound"], ["group", "NN", "6", "dobj"], ["does", "VBZ", "6", "aux"], ["polish", "NN", "6", "nsubj"], ["belong", "VB", "0", "ROOT"], ["to", "TO", "6", "dep"]], "question": "what language group does polish belong to"}
{"id": "WebQTrn-1111", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["name", "NN", "1", "nsubj"], ["of", "IN", "11", "case"], ["the", "DT", "11", "det"], ["new", "JJ", "11", "amod"], ["orleans", "NNS", "11", "compound"], ["saints", "NNS", "11", "compound"], ["football", "NN", "11", "compound"], ["stadium", "NN", "4", "nmod"]], "question": "what is the name of the new orleans saints football stadium"}
{"id": "WebQTrn-1144", "dep": [["what", "WDT", "2", "det"], ["team", "NN", "4", "nsubj"], ["does", "VBZ", "4", "aux"], ["drogba", "VB", "0", "ROOT"], ["play", "NN", "4", "dobj"], ["for", "IN", "7", "case"], ["2013", "CD", "4", "nmod"]], "question": "what team does drogba play for 2013"}
{"id": "WebQTrn-1148", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["northeast", "NN", "1", "nsubj"], ["of", "IN", "8", "case"], ["the", "DT", "8", "det"], ["united", "JJ", "8", "amod"], ["states", "NNS", "4", "nmod"]], "question": "what is the northeast of the united states"}
{"id": "WebQTrn-1150", "dep": [["what", "WDT", "2", "det"], ["currency", "NN", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["brazil", "NN", "5", "compound"], ["use", "NN", "3", "dobj"]], "question": "what currency does brazil use"}
{"id": "WebQTrn-1170", "dep": [["what", "WDT", "2", "det"], ["state", "NN", "6", "dobj"], ["did", "VBD", "6", "aux"], ["woodrow", "NNP", "5", "compound"], ["wilson", "NNP", "6", "nsubj"], ["represent", "VBP", "0", "ROOT"]], "question": "what state did woodrow wilson represent"}
{"id": "WebQTrn-1183", "dep": [["where", "WRB", "4", "advmod"], ["did", "VBD", "4", "aux"], ["galileo", "NNP", "4", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "6", "case"], ["school", "NN", "4", "nmod"]], "question": "where did galileo go to school"}
{"id": "WebQTrn-1201", "dep": [["what", "WDT", "2", "det"], ["state", "NN", "5", "nsubj"], ["was", "VBD", "5", "cop"], ["hillary", "JJ", "5", "amod"], ["clinton", "NN", "0", "ROOT"], ["a", "DT", "7", "det"], ["senator", "NN", "5", "dobj"], ["for", "IN", "7", "acl"]], "question": "what state was hillary clinton a senator for"}
{"id": "WebQTrn-1215", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "5", "nsubjpass"], ["is", "VBZ", "5", "auxpass"], ["mainly", "RB", "5", "advmod"], ["spoken", "VBN", "0", "ROOT"], ["in", "IN", "7", "case"], ["england", "NN", "5", "nmod"]], "question": "what language is mainly spoken in england"}
{"id": "WebQTrn-1221", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["braves", "NNS", "5", "compound"], ["mascot", "NN", "1", "nsubj"]], "question": "what is the braves mascot"}
{"id": "WebQTrn-1232", "dep": [["where", "WRB", "6", "advmod"], ["was", "VBD", "6", "aux"], ["the", "DT", "5", "det"], ["roman", "NN", "5", "compound"], ["empire", "NN", "6", "nsubj"], ["centered", "VBD", "0", "ROOT"]], "question": "where was the roman empire centered"}
{"id": "WebQTrn-1239", "dep": [["who", "WP", "2", "nsubj"], ["became", "VBD", "0", "ROOT"], ["president", "NN", "2", "xcomp"], ["when", "WRB", "7", "advmod"], ["jfk", "NN", "7", "nsubjpass"], ["was", "VBD", "7", "auxpass"], ["killed", "VBN", "2", "advcl"]], "question": "who became president when jfk was killed"}
{"id": "WebQTrn-1254", "dep": [["where", "WRB", "6", "advmod"], ["was", "VBD", "6", "aux"], ["the", "DT", "5", "det"], ["gallipoli", "NNS", "5", "compound"], ["campaign", "NN", "6", "nsubj"], ["waged", "VBD", "0", "ROOT"]], "question": "where was the gallipoli campaign waged"}
{"id": "WebQTrn-1257", "dep": [["what", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["king", "NN", "4", "compound"], ["george", "NN", "5", "nsubj"], ["vi", "LS", "0", "ROOT"], ["die", "VB", "5", "root"], ["of", "IN", "6", "nmod"]], "question": "what did king george vi die of"}
{"id": "WebQTrn-1259", "dep": [["where", "WRB", "0", "ROOT"], ["did", "VBD", "1", "root"], ["brad", "NN", "5", "compound"], ["paisley", "NN", "5", "compound"], ["graduate", "NN", "2", "dobj"], ["from", "IN", "7", "case"], ["college", "NN", "5", "nmod"]], "question": "where did brad paisley graduate from college"}
{"id": "WebQTrn-1271", "dep": [["what", "WDT", "2", "det"], ["type", "NN", "5", "dobj"], ["of", "IN", "4", "case"], ["music", "NN", "2", "nmod"], ["is", "VBZ", "0", "ROOT"], ["tchaikovsky", "NN", "5", "nsubj"]], "question": "what type of music is tchaikovsky"}
{"id": "WebQTrn-1277", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "5", "dobj"], ["do", "VBP", "5", "aux"], ["you", "PRP", "5", "nsubj"], ["speak", "VB", "0", "ROOT"], ["in", "IN", "7", "case"], ["iran", "NN", "5", "nmod"]], "question": "what language do you speak in iran"}
{"id": "WebQTrn-1307", "dep": [["who", "WP", "4", "nsubj"], ["is", "VBZ", "4", "cop"], ["prime", "JJ", "4", "amod"], ["minister", "NN", "0", "ROOT"], ["of", "IN", "7", "case"], ["japan", "JJ", "7", "amod"], ["2011", "CD", "4", "nmod"]], "question": "who is prime minister of japan 2011"}
{"id": "WebQTrn-1321", "dep": [["what", "WDT", "2", "det"], ["city", "NN", "3", "dobj"], ["is", "VBZ", "0", "ROOT"], ["the", "DT", "5", "det"], ["university", "NN", "3", "nsubj"], ["of", "IN", "9", "case"], ["maryland", "NN", "9", "compound"], ["university", "NN", "9", "compound"], ["college", "NN", "5", "nmod"], ["in", "IN", "3", "dep"]], "question": "what city is the university of maryland university college in"}
{"id": "WebQTrn-1340", "dep": [["what", "WP", "5", "nsubj"], ["all", "DT", "5", "dep"], ["does", "VBZ", "5", "aux"], ["google", "NN", "5", "nsubj"], ["have", "VB", "0", "ROOT"]], "question": "what all does google have"}
{"id": "WebQTrn-1372", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["zip", "NN", "5", "compound"], ["code", "NN", "1", "nsubj"], ["for", "IN", "8", "case"], ["trenton", "NN", "8", "compound"], ["ga", "NN", "5", "nmod"]], "question": "what is the zip code for trenton ga"}
{"id": "WebQTrn-1407", "dep": [["what", "WP", "2", "nsubj"], ["happened", "VBD", "0", "ROOT"], ["at", "IN", "7", "case"], ["fukushima", "NN", "7", "compound"], ["daiichi", "NNS", "7", "compound"], ["nuclear", "JJ", "7", "amod"], ["plant", "NN", "2", "nmod"]], "question": "what happened at fukushima daiichi nuclear plant"}
{"id": "WebQTrn-1424", "dep": [["where", "WRB", "3", "advmod"], ["is", "VBZ", "3", "cop"], ["oceania", "NN", "0", "ROOT"], ["on", "IN", "6", "case"], ["a", "DT", "6", "det"], ["map", "NN", "3", "nmod"]], "question": "where is oceania on a map"}
{"id": "WebQTrn-1428", "dep": [["when", "WRB", "0", "ROOT"], ["was", "VBD", "1", "root"], ["the", "DT", "5", "det"], ["last", "JJ", "5", "amod"], ["time", "NN", "2", "nsubj"], ["the", "DT", "8", "det"], ["ny", "NN", "8", "compound"], ["giants", "NNS", "5", "dep"], ["played", "VBN", "8", "acl"], ["in", "IN", "12", "case"], ["the", "DT", "12", "det"], ["superbowl", "NN", "9", "nmod"]], "question": "when was the last time the ny giants played in the superbowl"}
{"id": "WebQTrn-1430", "dep": [["who", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["the", "DT", "4", "det"], ["president", "NN", "1", "nsubj"], ["of", "IN", "6", "case"], ["pakistan", "NN", "4", "nmod"], ["in", "IN", "8", "case"], ["1980", "CD", "4", "nmod"]], "question": "who was the president of pakistan in 1980"}
{"id": "WebQTrn-1433", "dep": [["what", "WP", "0", "ROOT"], ["does", "VBZ", "1", "root"], ["the", "DT", "6", "det"], ["adriatic", "JJ", "6", "amod"], ["sea", "NN", "6", "compound"], ["border", "NN", "2", "dobj"]], "question": "what does the adriatic sea border"}
{"id": "WebQTrn-1449", "dep": [["what", "WP", "3", "nsubj"], ["to", "TO", "3", "mark"], ["do", "VB", "0", "ROOT"], ["when", "WRB", "5", "advmod"], ["traveling", "VBG", "3", "advcl"], ["to", "TO", "7", "mark"], ["london", "VB", "5", "xcomp"]], "question": "what to do when traveling to london"}
{"id": "WebQTrn-1466", "dep": [["what", "WP", "6", "dobj"], ["does", "VBZ", "6", "aux"], ["the", "DT", "4", "det"], ["letters", "NNS", "6", "nsubj"], ["eu", "NN", "6", "compound"], ["stand", "VB", "0", "ROOT"], ["for", "IN", "6", "nmod"]], "question": "what does the letters eu stand for"}
{"id": "WebQTrn-1474", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "5", "dobj"], ["do", "VBP", "5", "aux"], ["they", "PRP", "5", "nsubj"], ["speak", "VBP", "0", "ROOT"], ["italian", "JJ", "5", "xcomp"]], "question": "what countries do they speak italian"}
{"id": "WebQTrn-1485", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["francisco", "JJ", "6", "amod"], ["vasquez", "NN", "6", "compound"], ["de", "IN", "6", "amod"], ["coronado", "NN", "1", "nsubj"], ["known", "VBN", "6", "acl"], ["for", "IN", "7", "nmod"]], "question": "what was francisco vasquez de coronado known for"}
{"id": "WebQTrn-1515", "dep": [["where", "WRB", "5", "advmod"], ["was", "VBD", "5", "auxpass"], ["lance", "NN", "4", "compound"], ["armstrong", "NN", "5", "nsubjpass"], ["born", "VBN", "0", "ROOT"], ["and", "CC", "5", "cc"], ["in", "IN", "9", "case"], ["what", "WDT", "9", "det"], ["year", "NN", "5", "conj"]], "question": "where was lance armstrong born and in what year"}
{"id": "WebQTrn-1525", "dep": [["where", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["phil", "NN", "4", "compound"], ["mickelson", "NN", "5", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "7", "case"], ["college", "NN", "5", "nmod"]], "question": "where did phil mickelson go to college"}
{"id": "WebQTrn-1537", "dep": [["what", "WDT", "3", "det"], ["other", "JJ", "3", "amod"], ["languages", "NNS", "6", "dobj"], ["does", "VBZ", "6", "aux"], ["switzerland", "NN", "6", "nsubj"], ["speak", "VB", "0", "ROOT"]], "question": "what other languages does switzerland speak"}
{"id": "WebQTrn-1574", "dep": [["what", "WDT", "2", "det"], ["timezone", "NN", "3", "dobj"], ["is", "VBZ", "0", "ROOT"], ["toronto", "JJ", "5", "amod"], ["gmt", "NN", "3", "nsubj"]], "question": "what timezone is toronto gmt"}
{"id": "WebQTrn-1597", "dep": [["what", "WDT", "2", "det"], ["characters", "NNS", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["seth", "NN", "6", "compound"], ["macfarlane", "NN", "6", "compound"], ["voice", "NN", "3", "dobj"]], "question": "what characters does seth macfarlane voice"}
{"id": "WebQTrn-1599", "dep": [["what", "WP", "5", "dobj"], ["has", "VBZ", "5", "aux"], ["ian", "NNP", "4", "compound"], ["somerhalder", "NNP", "5", "nsubj"], ["played", "VBD", "0", "ROOT"], ["in", "IN", "5", "advcl"]], "question": "what has ian somerhalder played in"}
{"id": "WebQTrn-1603", "dep": [["what", "WDT", "2", "det"], ["form", "NN", "5", "nsubj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["exists", "VBZ", "0", "ROOT"], ["in", "IN", "7", "case"], ["china", "NN", "5", "nmod"]], "question": "what form of government exists in china"}
{"id": "WebQTrn-1618", "dep": [["what", "WDT", "2", "det"], ["type", "NN", "6", "nsubj"], ["of", "IN", "4", "case"], ["music", "NN", "2", "nmod"], ["does", "VBZ", "6", "aux"], ["ella", "VB", "0", "ROOT"], ["fitzgerald", "NN", "6", "dobj"], ["sing", "VB", "6", "dep"]], "question": "what type of music does ella fitzgerald sing"}
{"id": "WebQTrn-1629", "dep": [["who", "WP", "0", "ROOT"], ["the", "DT", "3", "det"], ["voice", "NN", "1", "nsubj"], ["of", "IN", "6", "case"], ["jack", "NN", "6", "compound"], ["skellington", "NN", "3", "nmod"]], "question": "who the voice of jack skellington"}
{"id": "WebQTrn-1653", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["andy", "NN", "2", "dobj"], ["in", "IN", "6", "case"], ["toy", "NN", "6", "compound"], ["story", "NN", "2", "nmod"], ["3", "CD", "6", "nummod"]], "question": "who played andy in toy story 3"}
{"id": "WebQTrn-1662", "dep": [["what", "WDT", "2", "det"], ["years", "NNS", "6", "nmod:tmod"], ["did", "VBD", "6", "aux"], ["jackie", "NNP", "5", "compound"], ["robinson", "NNP", "6", "nsubj"], ["play", "VB", "0", "ROOT"], ["baseball", "NN", "6", "dobj"]], "question": "what years did jackie robinson play baseball"}
{"id": "WebQTrn-1679", "dep": [["where", "WRB", "2", "advmod"], ["is", "VBZ", "0", "ROOT"], ["washington", "NN", "4", "compound"], ["d.c", "NN", "2", "nsubj"], ["at", "IN", "2", "dep"]], "question": "where is washington d.c. at"}
{"id": "WebQTrn-1682", "dep": [["when", "WRB", "4", "advmod"], ["did", "VBD", "4", "aux"], ["aldi", "NN", "4", "nsubj"], ["originate", "VB", "0", "ROOT"]], "question": "when did aldi originate"}
{"id": "WebQTrn-1706", "dep": [["who", "WP", "2", "nsubj"], ["controls", "VBZ", "0", "ROOT"], ["panama", "NN", "2", "dobj"]], "question": "who controls panama"}
{"id": "WebQTrn-1711", "dep": [["what", "WDT", "2", "det"], ["movies", "NNS", "6", "dobj"], ["did", "VBD", "6", "aux"], ["chris", "NNP", "5", "compound"], ["farley", "NNP", "6", "nsubj"], ["do", "VBP", "0", "ROOT"]], "question": "what movies did chris farley do"}
{"id": "WebQTrn-1728", "dep": [["what", "WDT", "2", "det"], ["kind", "NN", "7", "dobj"], ["of", "IN", "4", "case"], ["money", "NN", "2", "nmod"], ["do", "VBP", "7", "aux"], ["you", "PRP", "7", "nsubj"], ["use", "VB", "0", "ROOT"], ["in", "IN", "10", "case"], ["costa", "NN", "10", "compound"], ["rica", "NN", "7", "nmod"]], "question": "what kind of money do you use in costa rica"}
{"id": "WebQTrn-1741", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["carlton", "NN", "4", "compound"], ["banks", "NNS", "2", "dobj"]], "question": "who played carlton banks"}
{"id": "WebQTrn-1754", "dep": [["what", "WDT", "2", "nsubj"], ["influenced", "VBD", "0", "ROOT"], ["andy", "NN", "4", "compound"], ["warhol", "NN", "2", "dobj"], ["s", "VBZ", "6", "compound"], ["work", "NN", "2", "dep"]], "question": "what influenced andy warhol 's work"}
{"id": "WebQTrn-1756", "dep": [["who", "WP", "6", "nsubj"], ["is", "VBZ", "6", "cop"], ["angelina", "JJ", "6", "amod"], ["jolie", "NN", "6", "compound"], ["husband", "NN", "6", "compound"], ["name", "NN", "0", "ROOT"]], "question": "who is angelina jolie husband name"}
{"id": "WebQTrn-1758", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["present", "JJ", "5", "amod"], ["form", "NN", "1", "nsubj"], ["of", "IN", "7", "case"], ["government", "NN", "5", "nmod"], ["in", "IN", "9", "case"], ["iran", "NN", "5", "nmod"]], "question": "what is the present form of government in iran"}
{"id": "WebQTrn-1770", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["beyonce", "NN", "6", "compound"], ["s", "NNS", "6", "compound"], ["daughters", "NNS", "6", "compound"], ["name", "NN", "1", "nsubj"]], "question": "what is beyonce 's daughters name"}
{"id": "WebQTrn-1771", "dep": [["when", "WRB", "6", "advmod"], ["did", "VBD", "6", "aux"], ["the", "DT", "4", "det"], ["colts", "NNS", "6", "nsubj"], ["last", "JJ", "6", "advmod"], ["win", "VB", "0", "ROOT"], ["the", "DT", "8", "det"], ["superbowl", "NN", "6", "dobj"]], "question": "when did the colts last win the superbowl"}
{"id": "WebQTrn-1772", "dep": [["what", "WDT", "2", "det"], ["college", "NN", "6", "dep"], ["did", "VBD", "6", "aux"], ["michael", "NNP", "5", "compound"], ["jordan", "NNP", "6", "nsubj"], ["play", "VB", "0", "ROOT"], ["basketball", "NN", "6", "dobj"], ["for", "IN", "6", "dep"]], "question": "what college did michael jordan play basketball for"}
{"id": "WebQTrn-1774", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "4", "dobj"], ["are", "VBP", "4", "cop"], ["located", "JJ", "0", "ROOT"], ["in", "IN", "7", "case"], ["the", "DT", "7", "det"], ["netherlands", "NNS", "4", "nmod"]], "question": "what countries are located in the netherlands"}
{"id": "WebQTrn-1791", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["golfer", "NN", "5", "compound"], ["dustin", "NN", "5", "compound"], ["johnson", "NN", "1", "nsubj"], ["dating", "VBG", "5", "dep"]], "question": "who is golfer dustin johnson dating"}
{"id": "WebQTrn-1799", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["danielle", "NNP", "4", "compound"], ["rousseau", "NNP", "2", "dobj"], ["on", "IN", "6", "case"], ["lost", "VBN", "2", "nmod"]], "question": "who played danielle rousseau on lost"}
{"id": "WebQTrn-1816", "dep": [["what", "WDT", "2", "det"], ["part", "NN", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["seth", "NN", "6", "compound"], ["macfarlane", "NN", "6", "compound"], ["play", "NN", "3", "dobj"], ["in", "IN", "9", "case"], ["family", "NN", "9", "compound"], ["guy", "NN", "6", "nmod"]], "question": "what part does seth macfarlane play in family guy"}
{"id": "WebQTrn-1818", "dep": [["what", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["ben", "NNP", "4", "compound"], ["hall", "NN", "5", "nsubj"], ["do", "VBP", "0", "ROOT"]], "question": "what did ben hall do"}
{"id": "WebQTrn-1824", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["cruella", "NNP", "4", "compound"], ["deville", "NNP", "2", "dobj"], ["in", "IN", "7", "case"], ["102", "CD", "7", "nummod"], ["dalmatians", "NNS", "2", "nmod"]], "question": "who played cruella deville in 102 dalmatians"}
{"id": "WebQTrn-1830", "dep": [["what", "WDT", "2", "det"], ["ethnicity", "NN", "3", "dobj"], ["are", "VBP", "0", "ROOT"], ["people", "NNS", "3", "nsubj"], ["from", "IN", "6", "case"], ["iran", "NN", "4", "nmod"]], "question": "what ethnicity are people from iran"}
{"id": "WebQTrn-1843", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "4", "det"], ["publisher", "NN", "1", "nsubj"], ["of", "IN", "9", "case"], ["the", "DT", "9", "det"], ["wall", "NN", "9", "compound"], ["street", "NN", "9", "compound"], ["journal", "NN", "4", "nmod"]], "question": "who is the publisher of the wall street journal"}
{"id": "WebQTrn-1846", "dep": [["what", "WDT", "2", "det"], ["form", "NN", "7", "dobj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["does", "VBZ", "7", "aux"], ["afghanistan", "NNP", "7", "nsubj"], ["have", "VB", "0", "ROOT"]], "question": "what form of government does afghanistan have"}
{"id": "WebQTrn-1873", "dep": [["what", "WP", "2", "nsubj"], ["happened", "VBD", "0", "ROOT"], ["to", "TO", "4", "case"], ["nagasaki", "NNS", "2", "nmod"]], "question": "what happened to nagasaki"}
{"id": "WebQTrn-1875", "dep": [["what", "WDT", "2", "det"], ["movies", "NNS", "0", "ROOT"], ["did", "VBD", "2", "acl"], ["james", "NNS", "6", "compound"], ["franco", "JJ", "6", "amod"], ["play", "NN", "3", "dobj"], ["in", "IN", "6", "acl"]], "question": "what movies did james franco play in"}
{"id": "WebQTrn-1897", "dep": [["what", "WDT", "3", "det"], ["club", "NN", "3", "compound"], ["team", "NN", "6", "nsubj"], ["does", "VBZ", "6", "aux"], ["ronaldinho", "NN", "6", "compound"], ["play", "NN", "0", "ROOT"], ["for", "IN", "6", "dep"]], "question": "what club team does ronaldinho play for"}
{"id": "WebQTrn-1913", "dep": [["where", "WRB", "5", "advmod"], ["does", "VBZ", "5", "aux"], ["luke", "NN", "4", "compound"], ["skywalker", "NN", "5", "nsubj"], ["live", "VB", "0", "ROOT"], ["in", "IN", "8", "case"], ["star", "NN", "8", "compound"], ["wars", "NNS", "5", "nmod"]], "question": "where does luke skywalker live in star wars"}
{"id": "WebQTrn-1931", "dep": [["what", "WDT", "2", "det"], ["films", "NNS", "4", "nsubj"], ["has", "VBZ", "4", "aux"], ["morgan", "VBN", "0", "ROOT"], ["freeman", "NN", "4", "dobj"], ["narrated", "VBD", "4", "dep"]], "question": "what films has morgan freeman narrated"}
{"id": "WebQTrn-1935", "dep": [["where", "WRB", "5", "advmod"], ["was", "VBD", "5", "auxpass"], ["tommy", "JJ", "4", "amod"], ["emmanuel", "NN", "5", "nsubjpass"], ["born", "VBN", "0", "ROOT"]], "question": "where was tommy emmanuel born"}
{"id": "WebQTrn-1936", "dep": [["what", "WDT", "2", "det"], ["techniques", "NNS", "0", "ROOT"], ["did", "VBD", "2", "acl"], ["frida", "NN", "6", "compound"], ["kahlo", "JJ", "6", "amod"], ["use", "NN", "3", "dobj"], ["in", "IN", "9", "case"], ["her", "PRP$", "9", "nmod:poss"], ["paintings", "NNS", "6", "nmod"]], "question": "what techniques did frida kahlo use in her paintings"}
{"id": "WebQTrn-1946", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "3", "nsubj"], ["include", "VBP", "0", "ROOT"], ["western", "JJ", "5", "amod"], ["europe", "NN", "3", "dobj"]], "question": "what countries include western europe"}
{"id": "WebQTrn-1962", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["carl", "NN", "4", "compound"], ["fredricksen", "NN", "2", "dobj"], ["in", "IN", "6", "case"], ["up", "RB", "2", "nmod"]], "question": "who played carl fredricksen in up"}
{"id": "WebQTrn-1976", "dep": [["what", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["robert", "NNP", "4", "compound"], ["koch", "NNP", "5", "nsubj"], ["do", "VBP", "0", "ROOT"]], "question": "what did robert koch do"}
{"id": "WebQTrn-1981", "dep": [["who", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["lleyton", "NN", "4", "compound"], ["hewitt", "NN", "1", "nsubj"]], "question": "who is lleyton hewitt"}
{"id": "WebQTrn-1990", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["st", "NN", "5", "compound"], ["mary", "JJ", "5", "amod"], ["mascot", "NN", "1", "nsubj"]], "question": "what is st mary mascot"}
{"id": "WebQTrn-1992", "dep": [["who", "WP", "2", "nsubj"], ["shot", "VBD", "0", "ROOT"], ["j", "NN", "4", "compound"], ["lennon", "NN", "2", "dobj"]], "question": "who shot j lennon"}
{"id": "WebQTrn-2014", "dep": [["what", "WDT", "2", "det"], ["county", "NN", "4", "nsubj"], ["is", "VBZ", "4", "cop"], ["stockton", "NN", "0", "ROOT"], ["ca", "MD", "6", "aux"], ["located", "VBN", "4", "dep"], ["in", "IN", "6", "nmod"]], "question": "what county is stockton ca located in"}
{"id": "WebQTrn-2018", "dep": [["what", "WDT", "2", "det"], ["form", "NN", "6", "nsubj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["does", "VBZ", "6", "aux"], ["czech", "VB", "0", "ROOT"], ["republic", "NN", "6", "dobj"], ["have", "VBP", "6", "dep"]], "question": "what form of government does czech republic have"}
{"id": "WebQTrn-2034", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "6", "dobj"], ["do", "VBP", "6", "aux"], ["iraqi", "JJ", "5", "amod"], ["people", "NNS", "6", "nsubj"], ["speak", "VBP", "0", "ROOT"]], "question": "what language do iraqi people speak"}
{"id": "WebQTrn-2037", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["abe", "NN", "4", "compound"], ["lincoln", "NN", "1", "nsubj"], ["shot", "VBN", "4", "acl"], ["with", "IN", "5", "nmod"]], "question": "what was abe lincoln shot with"}
{"id": "WebQTrn-2056", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["the", "DT", "4", "det"], ["name", "NN", "1", "nsubj"], ["of", "IN", "10", "case"], ["martin", "NN", "10", "compound"], ["luther", "NN", "10", "compound"], ["king", "NN", "10", "compound"], ["jr", "NN", "10", "compound"], ["parents", "NNS", "4", "nmod"]], "question": "what was the name of martin luther king jr parents"}
{"id": "WebQTrn-2057", "dep": [["what", "WDT", "2", "det"], ["money", "NN", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["guatemala", "NN", "5", "compound"], ["use", "NN", "3", "dobj"]], "question": "what money does guatemala use"}
{"id": "WebQTrn-2059", "dep": [["what", "WDT", "2", "det"], ["year", "NN", "5", "nmod:tmod"], ["magic", "JJ", "4", "amod"], ["johnson", "NN", "5", "nsubj"], ["retired", "VBN", "0", "ROOT"]], "question": "what year magic johnson retired"}
{"id": "WebQTrn-2080", "dep": [["what", "WDT", "3", "det"], ["art", "NN", "3", "compound"], ["movement", "NN", "7", "nsubj"], ["was", "VBD", "7", "cop"], ["vincent", "JJ", "7", "amod"], ["van", "NN", "7", "compound"], ["gogh", "NN", "0", "ROOT"], ["apart", "RB", "7", "advmod"], ["of", "IN", "8", "mwe"]], "question": "what art movement was vincent van gogh apart of"}
{"id": "WebQTrn-2097", "dep": [["what", "WDT", "2", "det"], ["party", "NN", "5", "nsubj"], ["was", "VBD", "5", "cop"], ["winston", "JJ", "5", "amod"], ["churchill", "NN", "0", "ROOT"], ["in", "IN", "7", "case"], ["politics", "NNS", "5", "nmod"]], "question": "what party was winston churchill in politics"}
{"id": "WebQTrn-2117", "dep": [["what", "WDT", "2", "det"], ["types", "NNS", "7", "dobj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["does", "VBZ", "7", "aux"], ["china", "NN", "7", "nsubj"], ["have", "VB", "0", "ROOT"]], "question": "what types of government does china have"}
{"id": "WebQTrn-2172", "dep": [["what", "WDT", "2", "det"], ["school", "NN", "4", "nsubj"], ["does", "VBZ", "4", "aux"], ["bart", "VB", "0", "ROOT"], ["simpson", "NN", "6", "nsubj"], ["go", "VB", "4", "ccomp"], ["to", "TO", "6", "nmod"]], "question": "what school does bart simpson go to"}
{"id": "WebQTrn-2182", "dep": [["what", "WDT", "2", "det"], ["college", "NN", "6", "dep"], ["did", "VBD", "6", "aux"], ["ron", "NNP", "5", "compound"], ["jaworski", "NNP", "6", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "6", "nmod"]], "question": "what college did ron jaworski go to"}
{"id": "WebQTrn-2183", "dep": [["what", "WDT", "2", "det"], ["team", "NN", "6", "dobj"], ["does", "VBZ", "6", "aux"], ["allen", "NNP", "5", "compound"], ["iverson", "NNP", "6", "nsubj"], ["play", "VB", "0", "ROOT"], ["for", "IN", "8", "case"], ["now", "RB", "6", "advcl"]], "question": "what team does allen iverson play for now"}
{"id": "WebQTrn-2190", "dep": [["where", "WRB", "5", "advmod"], ["does", "VBZ", "5", "aux"], ["sam", "NNP", "4", "compound"], ["shepard", "NNP", "5", "nsubj"], ["live", "VB", "0", "ROOT"]], "question": "where does sam shepard live"}
{"id": "WebQTrn-2203", "dep": [["where", "WRB", "6", "advmod"], ["is", "VBZ", "6", "auxpass"], ["port", "NN", "5", "compound"], ["charlotte", "NN", "5", "compound"], ["florida", "NN", "6", "nsubjpass"], ["located", "JJ", "0", "ROOT"]], "question": "where is port charlotte florida located"}
{"id": "WebQTrn-2206", "dep": [["who", "WP", "2", "nsubj"], ["helped", "VBD", "0", "ROOT"], ["form", "VB", "2", "ccomp"], ["the", "DT", "6", "det"], ["american", "NN", "6", "compound"], ["federation", "NN", "3", "dobj"], ["of", "IN", "8", "case"], ["labor", "NN", "6", "nmod"]], "question": "who helped form the american federation of labor"}
{"id": "WebQTrn-2215", "dep": [["where", "WRB", "5", "advmod"], ["was", "VBD", "5", "auxpass"], ["brad", "JJ", "4", "amod"], ["paisley", "NN", "5", "nsubjpass"], ["born", "VBN", "0", "ROOT"], ["and", "CC", "5", "cc"], ["raised", "VBN", "5", "conj"]], "question": "where was brad paisley born and raised"}
{"id": "WebQTrn-2226", "dep": [["what", "WP", "0", "ROOT"], ["are", "VBP", "1", "cop"], ["all", "DT", "4", "det"], ["songs", "NNS", "1", "nsubj"], ["by", "IN", "7", "case"], ["taylor", "NN", "7", "compound"], ["swift", "NN", "4", "nmod"]], "question": "what are all songs by taylor swift"}
{"id": "WebQTrn-2228", "dep": [["who", "WP", "5", "dep"], ["was", "VBD", "5", "auxpass"], ["francis", "JJ", "4", "amod"], ["drake", "NN", "5", "nsubjpass"], ["married", "VBN", "0", "ROOT"], ["to", "TO", "5", "nmod"]], "question": "who was francis drake married to"}
{"id": "WebQTrn-2245", "dep": [["who", "WP", "2", "nsubj"], ["invented", "VBD", "0", "ROOT"], ["facebook", "NN", "4", "compound"], ["wikipedia", "NN", "2", "dobj"]], "question": "who invented facebook wikipedia"}
{"id": "WebQTrn-2263", "dep": [["what", "WP", "0", "ROOT"], ["did", "VBD", "1", "root"], ["sammy", "JJ", "6", "amod"], ["davis", "JJ", "6", "amod"], ["jr", "NN", "6", "compound"], ["die", "NN", "2", "dobj"], ["of", "IN", "6", "dep"]], "question": "what did sammy davis jr die of"}
{"id": "WebQTrn-2267", "dep": [["what", "WDT", "2", "det"], ["year", "NN", "5", "nmod:tmod"], ["did", "VBD", "5", "aux"], ["dwayne", "NNP", "5", "nsubj"], ["wade", "VB", "0", "ROOT"], ["came", "VBD", "5", "ccomp"], ["to", "TO", "9", "case"], ["the", "DT", "9", "det"], ["nba", "NN", "6", "nmod"]], "question": "what year did dwayne wade came to the nba"}
{"id": "WebQTrn-2298", "dep": [["what", "WP", "5", "dobj"], ["do", "VBP", "5", "aux"], ["michelle", "NN", "4", "compound"], ["obama", "NN", "5", "nsubj"], ["do", "VBP", "0", "ROOT"], ["for", "IN", "8", "case"], ["a", "DT", "8", "det"], ["living", "NN", "5", "nmod"]], "question": "what do michelle obama do for a living"}
{"id": "WebQTrn-2305", "dep": [["what", "WDT", "2", "det"], ["club", "NN", "5", "nsubj"], ["does", "VBZ", "5", "aux"], ["ronaldinho", "NN", "5", "compound"], ["play", "NN", "0", "ROOT"], ["for", "IN", "7", "case"], ["2012", "CD", "5", "nmod"]], "question": "what club does ronaldinho play for 2012"}
{"id": "WebQTrn-2311", "dep": [["what", "WDT", "2", "nsubj"], ["killed", "VBD", "0", "ROOT"], ["sammy", "JJ", "5", "amod"], ["davis", "NNP", "5", "compound"], ["jr", "NN", "2", "dobj"]], "question": "what killed sammy davis jr"}
{"id": "WebQTrn-2328", "dep": [["who", "WP", "2", "nsubj"], ["won", "VBD", "0", "ROOT"], ["the", "DT", "5", "det"], ["presidential", "JJ", "5", "amod"], ["race", "NN", "2", "dobj"], ["in", "IN", "7", "case"], ["venezuela", "NN", "2", "nmod"]], "question": "who won the presidential race in venezuela"}
{"id": "WebQTrn-2340", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["eli", "JJ", "5", "amod"], ["whitney", "JJ", "5", "amod"], ["job", "NN", "1", "nsubj"]], "question": "what was eli whitney job"}
{"id": "WebQTrn-2343", "dep": [["who", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["hermione", "NNP", "4", "compound"], ["granger", "NNP", "5", "nsubj"], ["marry", "VB", "0", "ROOT"]], "question": "who did hermione granger marry"}
{"id": "WebQTrn-2358", "dep": [["where", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["newt", "NN", "4", "compound"], ["gingrich", "NN", "5", "nsubj"], ["attend", "VB", "0", "ROOT"], ["college", "NN", "5", "dobj"]], "question": "where did newt gingrich attend college"}
{"id": "WebQTrn-2370", "dep": [["who", "WP", "3", "nsubj"], ["is", "VBZ", "3", "aux"], ["playing", "VBG", "0", "ROOT"], ["bilbo", "NN", "3", "dobj"], ["in", "IN", "8", "case"], ["the", "DT", "8", "det"], ["hobbit", "NN", "8", "compound"], ["movie", "NN", "3", "nmod"]], "question": "who is playing bilbo in the hobbit movie"}
{"id": "WebQTrn-2378", "dep": [["who", "WP", "5", "dobj"], ["does", "VBZ", "5", "aux"], ["jodelle", "NNP", "4", "compound"], ["ferland", "NNP", "5", "nsubj"], ["play", "VB", "0", "ROOT"], ["in", "IN", "7", "case"], ["eclipse", "NN", "5", "nmod"]], "question": "who does jodelle ferland play in eclipse"}
{"id": "WebQTrn-2388", "dep": [["what", "WDT", "2", "det"], ["films", "NNS", "3", "nsubj"], ["have", "VBP", "0", "ROOT"], ["taylor", "NN", "5", "compound"], ["lautner", "NN", "6", "nsubj"], ["starred", "VBD", "3", "ccomp"], ["in", "IN", "6", "compound:prt"]], "question": "what films have taylor lautner starred in"}
{"id": "WebQTrn-2392", "dep": [["what", "WDT", "2", "det"], ["airport", "NN", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["southwest", "JJS", "5", "amod"], ["fly", "NN", "3", "dobj"], ["to", "TO", "8", "case"], ["in", "IN", "8", "case"], ["denver", "NN", "5", "nmod"]], "question": "what airport does southwest fly to in denver"}
{"id": "WebQTrn-2395", "dep": [["where", "WRB", "0", "ROOT"], ["kurdish", "JJ", "3", "amod"], ["people", "NNS", "1", "root"], ["from", "IN", "3", "acl"]], "question": "where kurdish people from"}
{"id": "WebQTrn-2403", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "6", "det"], ["buddhist", "NN", "6", "compound"], ["holy", "JJ", "6", "amod"], ["book", "NN", "1", "nsubj"]], "question": "what is the buddhist holy book"}
{"id": "WebQTrn-2424", "dep": [["what", "WDT", "2", "det"], ["college", "NN", "6", "dobj"], ["did", "VBD", "6", "aux"], ["kris", "NNP", "5", "compound"], ["humphries", "NNS", "6", "nsubj"], ["play", "VBP", "0", "ROOT"], ["for", "IN", "6", "dep"]], "question": "what college did kris humphries play for"}
{"id": "WebQTrn-2429", "dep": [["what", "WDT", "2", "det"], ["type", "NN", "6", "dobj"], ["of", "IN", "4", "case"], ["government", "NN", "2", "nmod"], ["does", "VBZ", "6", "aux"], ["japan", "VB", "0", "ROOT"], ["currently", "RB", "6", "advmod"], ["have", "VBP", "6", "dep"]], "question": "what type of government does japan currently have"}
{"id": "WebQTrn-2454", "dep": [["what", "WDT", "2", "det"], ["airlines", "NNS", "3", "nsubj"], ["fly", "VBP", "0", "ROOT"], ["into", "IN", "7", "case"], ["ontario", "JJ", "7", "amod"], ["international", "JJ", "7", "amod"], ["airport", "NN", "3", "nmod"]], "question": "what airlines fly into ontario international airport"}
{"id": "WebQTrn-2465", "dep": [["what", "WP", "4", "dobj"], ["do", "VBP", "4", "aux"], ["you", "PRP", "4", "nsubj"], ["speak", "VB", "0", "ROOT"], ["in", "IN", "6", "case"], ["singapore", "NN", "4", "nmod"]], "question": "what do you speak in singapore"}
{"id": "WebQTrn-2478", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "6", "det"], ["syracuse", "NN", "6", "compound"], ["university", "NN", "6", "compound"], ["mascot", "NN", "1", "nsubj"]], "question": "what is the syracuse university mascot"}
{"id": "WebQTrn-2482", "dep": [["what", "WDT", "2", "det"], ["government", "NN", "5", "dobj"], ["did", "VBD", "5", "aux"], ["japan", "NN", "5", "nsubj"], ["have", "VBP", "0", "ROOT"]], "question": "what government did japan have"}
{"id": "WebQTrn-2485", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "0", "ROOT"], ["does", "VBZ", "2", "dep"], ["china", "NN", "5", "compound"], ["border", "NN", "3", "dobj"]], "question": "what countries does china border"}
{"id": "WebQTrn-2487", "dep": [["what", "WDT", "2", "det"], ["drug", "NN", "6", "dobj"], ["did", "VBD", "6", "aux"], ["mitch", "NNP", "5", "compound"], ["hedberg", "NNP", "6", "nsubj"], ["overdose", "VB", "0", "ROOT"], ["on", "IN", "6", "dep"]], "question": "what drug did mitch hedberg overdose on"}
{"id": "WebQTrn-2491", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "5", "dobj"], ["do", "VBP", "5", "aux"], ["you", "PRP", "5", "nsubj"], ["speak", "VB", "0", "ROOT"], ["in", "IN", "7", "case"], ["austria", "NN", "5", "nmod"]], "question": "what language do you speak in austria"}
{"id": "WebQTrn-2499", "dep": [["who", "WP", "5", "dobj"], ["did", "VBD", "5", "aux"], ["plaxico", "NNP", "4", "compound"], ["burress", "NNP", "5", "nsubj"], ["sign", "NN", "0", "ROOT"], ["with", "IN", "7", "case"], ["2012", "CD", "5", "nmod"]], "question": "who did plaxico burress sign with 2012"}
{"id": "WebQTrn-2505", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["vanderbilt", "NN", "5", "compound"], ["university", "NN", "5", "compound"], ["mascot", "NN", "1", "nsubj"]], "question": "what is vanderbilt university mascot"}
{"id": "WebQTrn-2510", "dep": [["what", "WDT", "2", "det"], ["country", "NN", "0", "ROOT"], ["did", "VBD", "2", "dep"], ["truman", "JJ", "5", "amod"], ["lead", "NN", "3", "dobj"]], "question": "what country did truman lead"}
{"id": "WebQTrn-2539", "dep": [["what", "WDT", "2", "det"], ["team", "NN", "6", "dobj"], ["did", "VBD", "6", "aux"], ["albert", "NNP", "5", "compound"], ["pujols", "NNS", "6", "nsubj"], ["play", "VBP", "0", "ROOT"], ["for", "IN", "6", "dep"]], "question": "what team did albert pujols play for"}
{"id": "WebQTrn-2555", "dep": [["what", "WP", "3", "nsubj"], ["do", "VBP", "3", "aux"], ["do", "VB", "0", "ROOT"], ["in", "IN", "6", "case"], ["new", "JJ", "6", "amod"], ["york", "NN", "3", "nmod"]], "question": "what do do in new york"}
{"id": "WebQTrn-2588", "dep": [["where", "WRB", "5", "advmod"], ["do", "VBP", "5", "aux"], ["navajo", "JJ", "4", "amod"], ["indians", "NNS", "5", "nsubj"], ["live", "VBP", "0", "ROOT"]], "question": "where do navajo indians live"}
{"id": "WebQTrn-2603", "dep": [["what", "WP", "5", "dobj"], ["do", "VBP", "5", "aux"], ["hungarian", "JJ", "4", "amod"], ["people", "NNS", "5", "nsubj"], ["speak", "VBP", "0", "ROOT"]], "question": "what do hungarian people speak"}
{"id": "WebQTrn-2609", "dep": [["who", "WP", "2", "nsubj"], ["speaks", "VBZ", "0", "ROOT"], ["stewie", "NN", "2", "dobj"], ["on", "IN", "6", "case"], ["family", "NN", "6", "compound"], ["guy", "NN", "3", "nmod"]], "question": "who speaks stewie on family guy"}
{"id": "WebQTrn-2631", "dep": [["what", "WDT", "2", "det"], ["countries", "NNS", "3", "dobj"], ["are", "VBP", "0", "ROOT"], ["the", "DT", "5", "det"], ["mediterranean", "JJ", "3", "nsubj"]], "question": "what countries are the mediterranean"}
{"id": "WebQTrn-2639", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["time", "NN", "4", "compound"], ["zone", "NN", "1", "nsubj"], ["in", "IN", "7", "case"], ["new", "JJ", "7", "amod"], ["orleans", "NNS", "4", "nmod"]], "question": "what is time zone in new orleans"}
{"id": "WebQTrn-2651", "dep": [["what", "WP", "4", "dep"], ["do", "VBP", "4", "aux"], ["they", "PRP", "4", "nsubj"], ["speak", "VBP", "0", "ROOT"], ["iceland", "NN", "4", "dobj"]], "question": "what do they speak iceland"}
{"id": "WebQTrn-2655", "dep": [["when", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["joe", "NNP", "4", "compound"], ["mcelderry", "NNP", "5", "nsubj"], ["won", "VBD", "0", "ROOT"], ["x", "NN", "7", "compound"], ["factor", "NN", "5", "dobj"]], "question": "when did joe mcelderry won x factor"}
{"id": "WebQTrn-2660", "dep": [["what", "WP", "0", "ROOT"], ["does", "VBZ", "1", "root"], ["a", "DT", "6", "det"], ["american", "JJ", "6", "amod"], ["rottweiler", "NN", "6", "compound"], ["look", "NN", "2", "dobj"], ["like", "IN", "6", "dep"]], "question": "what does a american rottweiler look like"}
{"id": "WebQTrn-2685", "dep": [["who", "WP", "2", "nsubj"], ["inspired", "VBD", "0", "ROOT"], ["antonio", "NN", "4", "compound"], ["vivaldi", "NNS", "2", "dobj"]], "question": "who inspired antonio vivaldi"}
{"id": "WebQTrn-2693", "dep": [["what", "WDT", "2", "det"], ["state", "NN", "6", "nsubj"], ["was", "VBD", "6", "cop"], ["ronald", "JJ", "6", "amod"], ["reagan", "JJ", "6", "amod"], ["governor", "NN", "0", "ROOT"], ["of", "IN", "6", "dep"]], "question": "what state was ronald reagan governor of"}
{"id": "WebQTrn-2726", "dep": [["who", "WP", "2", "nsubj"], ["owns", "VBZ", "0", "ROOT"], ["the", "DT", "8", "det"], ["new", "JJ", "8", "amod"], ["england", "NN", "8", "compound"], ["patriots", "NNS", "8", "compound"], ["football", "NN", "8", "compound"], ["team", "NN", "2", "dobj"]], "question": "who owns the new england patriots football team"}
{"id": "WebQTrn-2730", "dep": [["what", "WDT", "2", "nsubj"], ["has", "VBZ", "0", "ROOT"], ["lucy", "NN", "4", "compound"], ["hale", "NN", "2", "dobj"], ["played", "VBN", "4", "acl"], ["in", "IN", "5", "nmod"]], "question": "what has lucy hale played in"}
{"id": "WebQTrn-2734", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "0", "ROOT"], ["do", "VBP", "2", "dep"], ["argentina", "NN", "5", "compound"], ["use", "NN", "3", "dobj"]], "question": "what language do argentina use"}
{"id": "WebQTrn-2754", "dep": [["who", "WP", "2", "nsubj"], ["played", "VBD", "0", "ROOT"], ["as", "IN", "4", "case"], ["barney", "NN", "2", "nmod"]], "question": "who played as barney"}
{"id": "WebQTrn-2767", "dep": [["what", "WDT", "2", "det"], ["language", "NN", "4", "nsubj"], ["does", "VBZ", "4", "aux"], ["greece", "VB", "0", "ROOT"], ["use", "NN", "4", "dobj"]], "question": "what language does greece use"}
{"id": "WebQTrn-2777", "dep": [["where", "WRB", "0", "ROOT"], ["the", "DT", "3", "det"], ["queen", "NN", "1", "dep"], ["of", "IN", "6", "case"], ["denmark", "NN", "6", "compound"], ["lives", "NNS", "3", "nmod"]], "question": "where the queen of denmark lives"}
{"id": "WebQTrn-2786", "dep": [["where", "WRB", "6", "advmod"], ["does", "VBZ", "6", "aux"], ["the", "DT", "5", "det"], ["appalachian", "JJ", "5", "amod"], ["trail", "NN", "6", "nsubj"], ["run", "VBN", "0", "ROOT"], ["through", "IN", "6", "nmod"]], "question": "where does the appalachian trail run through"}
{"id": "WebQTrn-2822", "dep": [["what", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["the", "DT", "5", "det"], ["political", "JJ", "5", "amod"], ["system", "NN", "1", "nsubj"], ["in", "IN", "7", "case"], ["libya", "NN", "5", "nmod"]], "question": "what was the political system in libya"}
{"id": "WebQTrn-2828", "dep": [["where", "WRB", "5", "advmod"], ["does", "VBZ", "5", "aux"], ["sally", "NNP", "4", "compound"], ["pearson", "NNP", "5", "nsubj"], ["live", "VB", "0", "ROOT"]], "question": "where does sally pearson live"}
{"id": "WebQTrn-2830", "dep": [["where", "WRB", "2", "advmod"], ["is", "VBZ", "0", "ROOT"], ["madeira", "NN", "2", "nsubj"]], "question": "where is madeira"}
{"id": "WebQTrn-2835", "dep": [["what", "WDT", "2", "det"], ["things", "NNS", "5", "dobj"], ["did", "VBD", "5", "aux"], ["thomas", "NNS", "5", "nsubj"], ["edison", "VB", "0", "ROOT"], ["invent", "VB", "5", "ccomp"]], "question": "what things did thomas edison invent"}
{"id": "WebQTrn-2850", "dep": [["on", "IN", "3", "case"], ["what", "WDT", "3", "det"], ["continent", "NN", "5", "nmod"], ["is", "VBZ", "5", "cop"], ["canada", "NN", "0", "ROOT"], ["found", "VBN", "5", "acl"]], "question": "on what continent is canada found"}
{"id": "WebQTrn-2860", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["dominant", "JJ", "5", "amod"], ["language", "NN", "1", "nsubj"], ["of", "IN", "7", "case"], ["jamaica", "NN", "5", "nmod"]], "question": "what is the dominant language of jamaica"}
{"id": "WebQTrn-2891", "dep": [["what", "WDT", "3", "det"], ["art", "NN", "3", "compound"], ["movement", "NN", "7", "nsubj"], ["was", "VBD", "7", "cop"], ["pablo", "JJ", "7", "amod"], ["picasso", "NN", "7", "compound"], ["part", "NN", "0", "ROOT"], ["of", "IN", "7", "dep"]], "question": "what art movement was pablo picasso part of"}
{"id": "WebQTrn-2903", "dep": [["what", "WDT", "3", "det"], ["country", "NN", "3", "compound"], ["columbus", "NN", "5", "nsubjpass"], ["was", "VBD", "5", "auxpass"], ["born", "VBN", "0", "ROOT"]], "question": "what country columbus was born"}
{"id": "WebQTrn-2914", "dep": [["how", "WRB", "2", "advmod"], ["much", "JJ", "5", "amod"], ["indiana", "NN", "5", "compound"], ["jones", "NNS", "5", "compound"], ["movies", "NNS", "6", "nsubj"], ["are", "VBP", "0", "ROOT"], ["there", "EX", "6", "expl"]], "question": "how much indiana jones movies are there"}
{"id": "WebQTrn-2915", "dep": [["what", "WP", "5", "dobj"], ["does", "VBZ", "5", "aux"], ["george", "NN", "4", "compound"], ["soros", "NNS", "5", "nsubj"], ["own", "VBP", "0", "ROOT"]], "question": "what does george soros own"}
{"id": "WebQTrn-2921", "dep": [["who", "WP", "2", "nsubj"], ["started", "VBD", "0", "ROOT"], ["the", "DT", "6", "det"], ["federal", "JJ", "6", "amod"], ["reserve", "NN", "6", "compound"], ["board", "NN", "2", "dobj"]], "question": "who started the federal reserve board"}
{"id": "WebQTrn-2922", "dep": [["where", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["marco", "NNP", "4", "compound"], ["rubio", "NNP", "5", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "7", "case"], ["college", "NN", "5", "nmod"]], "question": "where did marco rubio go to college"}
{"id": "WebQTrn-2949", "dep": [["when", "WRB", "0", "ROOT"], ["did", "VBD", "1", "root"], ["romney", "NN", "5", "compound"], ["first", "JJ", "5", "amod"], ["run", "NN", "2", "dobj"], ["for", "IN", "7", "case"], ["president", "NN", "5", "nmod"]], "question": "when did romney first run for president"}
{"id": "WebQTrn-2965", "dep": [["what", "WP", "0", "ROOT"], ["is", "VBZ", "1", "cop"], ["the", "DT", "5", "det"], ["main", "JJ", "5", "amod"], ["language", "NN", "1", "nsubj"], ["spoken", "VBN", "5", "acl"], ["in", "IN", "8", "case"], ["switzerland", "NN", "6", "nmod"]], "question": "what is the main language spoken in switzerland"}
{"id": "WebQTrn-2975", "dep": [["what", "WDT", "2", "det"], ["movies", "NNS", "0", "ROOT"], ["does", "VBZ", "2", "acl"], ["johnny", "NNP", "6", "compound"], ["depp", "NN", "6", "compound"], ["play", "NN", "3", "dobj"], ["in", "IN", "6", "acl"]], "question": "what movies does johnny depp play in"}
{"id": "WebQTrn-2991", "dep": [["where", "WRB", "5", "advmod"], ["did", "VBD", "5", "aux"], ["nick", "NNP", "4", "compound"], ["clegg", "NNP", "5", "nsubj"], ["go", "VB", "0", "ROOT"], ["to", "TO", "8", "case"], ["prep", "JJ", "8", "amod"], ["school", "NN", "5", "nmod"]], "question": "where did nick clegg go to prep school"}
{"id": "WebQTrn-2993", "dep": [["what", "WDT", "2", "det"], ["currency", "NN", "5", "dobj"], ["should", "MD", "5", "aux"], ["i", "FW", "5", "nsubj"], ["take", "VB", "0", "ROOT"], ["to", "TO", "7", "case"], ["mauritius", "NN", "5", "nmod"]], "question": "what currency should i take to mauritius"}
{"id": "WebQTrn-2998", "dep": [["who", "WP", "5", "nsubj"], ["is", "VBZ", "1", "cop"], ["ellen", "NN", "4", "compound"], ["albertini", "NNS", "1", "nsubj"], ["dow", "VBP", "0", "ROOT"]], "question": "who is ellen albertini dow"}
{"id": "WebQTrn-3034", "dep": [["where", "WRB", "7", "advmod"], ["did", "VBD", "7", "aux"], ["dr", "NN", "6", "compound"], ["martin", "NN", "6", "compound"], ["luther", "NN", "6", "compound"], ["king", "NN", "7", "nsubj"], ["get", "VB", "0", "ROOT"], ["his", "PRP$", "9", "nmod:poss"], ["doctorate", "NN", "7", "dobj"]], "question": "where did dr martin luther king get his doctorate"}
{"id": "WebQTrn-3036", "dep": [["who", "WP", "0", "ROOT"], ["was", "VBD", "1", "cop"], ["the", "DT", "4", "det"], ["president", "NN", "1", "nsubj"], ["of", "IN", "8", "case"], ["indian", "JJ", "8", "amod"], ["national", "JJ", "8", "amod"], ["congress", "NN", "4", "nmod"]], "question": "who was the president of indian national congress"}
{"id": "WebQTrn-3037", "dep": [["where", "WRB", "0", "ROOT"], ["do", "VBP", "1", "root"], ["most", "JJS", "2", "dobj"], ["of", "IN", "6", "case"], ["the", "DT", "6", "det"], ["people", "NNS", "3", "nmod"], ["in", "IN", "9", "case"], ["egypt", "JJ", "9", "amod"], ["live", "JJ", "6", "nmod"]], "question": "where do most of the people in egypt live"}
{"id": "WebQTrn-3056", "dep": [["what", "WDT", "2", "det"], ["county", "NN", "3", "dobj"], ["is", "VBZ", "0", "ROOT"], ["denver", "NN", "5", "compound"], ["colorado", "NN", "3", "nsubj"], ["in", "IN", "3", "dep"]], "question": "what county is denver colorado in"}
{"id": "WebQTrn-3087", "dep": [["what", "WP", "3", "nsubj"], ["does", "VBZ", "3", "aux"], ["united", "VBN", "7", "csubj"], ["church", "NN", "3", "dobj"], ["of", "IN", "6", "case"], ["christ", "NN", "4", "nmod"], ["believe", "VBP", "0", "ROOT"]], "question": "what does united church of christ believe"}
{"id": "WebQTrn-3093", "dep": [["where", "WRB", "0", "ROOT"], ["is", "VBZ", "1", "dep"], ["the", "DT", "5", "det"], ["capital", "NN", "5", "compound"], ["city", "NN", "2", "nsubj"], ["of", "IN", "7", "case"], ["assyrians", "NNS", "5", "nmod"]], "question": "where is the capital city of assyrians"}
