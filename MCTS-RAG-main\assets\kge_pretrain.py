#!/usr/bin/env python3
"""
CCMGT预训练脚本 (重构版)
基于SimKGC、GraphMAE、KGTransformer源码的真正融合实现

使用模块化架构:
- ccmgt_config.py: 配置管理
- ccmgt_model.py: 核心模型
- ccmgt_trainer.py: 训练器
- negative_sampling.py: SimKGC负采样
- masking_strategy.py: GraphMAE掩码
- sequence_encoder.py: KGTransformer序列化
- loss_functions.py: 统一损失函数

使用方法:
    python kge_pretrain.py --data_folder data/webqsp/ --epochs 200 --batch_size 1024
"""

import os
import sys
import torch
import argparse
import logging
from typing import Dict, Any, List, Tuple
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入CCMGT模块
try:
    from kge.ccmgt_config import CCMGTConfig, create_default_config, create_lightweight_config
    from kge.ccmgt_model import CCMGTCore
    from kge.ccmgt_trainer import CCMGTTrainer, create_trainer_from_config
    from kge.utils import load_kg_triples  # 保持原有数据加载逻辑
except ImportError as e:
    print(f"导入CCMGT模块失败: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path}")
    raise ImportError("无法导入CCMGT模块，请检查模块化文件是否正确创建")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_data(data_folder: str) -> Tuple[List[Tuple[int, int, int]], Dict[str, int], Dict[str, int]]:
    """
    加载知识图谱数据
    保持与原有代码的兼容性
    """
    try:
        # 使用原有的数据加载函数
        train_triples, entity2id, relation2id = load_kg_triples(data_folder)
        logger.info(f"数据加载成功: {len(train_triples)}个三元组, {len(entity2id)}个实体, {len(relation2id)}个关系")
        return train_triples, entity2id, relation2id
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        # 生成模拟数据用于测试
        logger.warning("使用模拟数据进行测试")
        
        num_entities = 1000
        num_relations = 50
        num_triples = 5000
        
        entity2id = {f"entity_{i}": i for i in range(num_entities)}
        relation2id = {f"relation_{i}": i for i in range(num_relations)}
        
        import random
        train_triples = []
        for _ in range(num_triples):
            h = random.randint(0, num_entities - 1)
            r = random.randint(0, num_relations - 1)
            t = random.randint(0, num_entities - 1)
            if h != t:  # 避免自环
                train_triples.append((h, r, t))
        
        train_triples = list(set(train_triples))  # 去重
        logger.info(f"模拟数据生成: {len(train_triples)}个三元组")
        
        return train_triples, entity2id, relation2id


def create_config_from_args(args: argparse.Namespace, 
                          num_entities: int, 
                          num_relations: int) -> CCMGTConfig:
    """从命令行参数创建配置"""
    
    if args.lightweight:
        config = create_lightweight_config(num_entities, num_relations)
    else:
        config = create_default_config(num_entities, num_relations)
    
    # 更新配置参数
    config.complex_dim = args.embedding_dim
    config.hidden_size = args.hidden_size
    config.num_hidden_layers = args.num_hidden_layers
    config.num_attention_heads = args.num_attention_heads
    
    # 🔥 功能控制开关（新增）
    config.enable_simkgc = args.enable_simkgc
    config.enable_graphmae = args.enable_graphmae
    config.enable_kgtransformer = args.enable_kgtransformer
    config.enable_complex_attention = args.enable_complex_attention
    
    # SimKGC参数
    config.temperature = args.temperature
    config.pre_batch = args.pre_batch
    config.additive_margin = args.additive_margin
    
    # GraphMAE参数
    config.mask_rate = args.mask_rate
    config.replace_rate = args.replace_rate
    config.loss_fn = args.loss_fn
    config.alpha_l = args.alpha_l
    
    # 多任务权重
    config.lambda_link = args.lambda_link
    config.lambda_contrastive = args.lambda_contrastive
    config.lambda_recon = args.lambda_recon
    
    # 训练参数
    config.learning_rate = args.lr
    config.weight_decay = args.weight_decay
    config.warmup_ratio = args.warmup_ratio
    config.refinement_ratio = args.refinement_ratio
    config.min_mrr_threshold = args.min_mrr_threshold
    config.patience = args.patience
    
    # 🔥 显存优化参数 (SOTA策略)
    config.use_amp = args.use_amp
    config.gradient_checkpointing = args.gradient_checkpointing
    config.gradient_accumulation_steps = args.gradient_accumulation_steps
    
    # 🔥 重新计算动态参数，确保配置生效
    config.__post_init__()
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CCMGT: Complex Contrastive Masked Graph Transformer')
    
    # 🔥 基础参数
    parser.add_argument('--data_folder', type=str, default='data/webqsp/', help='数据文件夹路径')
    parser.add_argument('--embedding_dim', type=int, default=50, help='ComplX嵌入维度')
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.0005, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='权重衰减')
    parser.add_argument('--checkpoint_dir', type=str, default='./ccmgt_checkpoints/', help='检查点目录')
    parser.add_argument('--lightweight', action='store_true', help='使用轻量级配置')
    
    # 🔥 功能控制开关（新增）
    parser.add_argument('--enable_simkgc', action='store_true', help='启用SimKGC对比学习模块')
    parser.add_argument('--enable_graphmae', action='store_true', help='启用GraphMAE掩码自编码模块')
    parser.add_argument('--enable_kgtransformer', action='store_true', help='启用KGTransformer序列化模块')
    parser.add_argument('--enable_complex_attention', action='store_true', help='启用ComplX复数注意力机制')
    
    # 🔥 Transformer参数
    parser.add_argument('--hidden_size', type=int, default=256, help='Transformer隐藏维度')  # 🔧 优化默认值
    parser.add_argument('--num_hidden_layers', type=int, default=2, help='Transformer层数')  # 🔧 优化默认值
    parser.add_argument('--num_attention_heads', type=int, default=4, help='注意力头数')  # 🔧 优化默认值
    
    # 🔥 SimKGC参数 (基于源码)
    parser.add_argument('--temperature', type=float, default=0.05, help='对比学习温度')
    parser.add_argument('--pre_batch', type=int, default=2, help='历史批次负样本数量')
    parser.add_argument('--additive_margin', type=float, default=0.02, help='加性边界')
    
    # 🔥 GraphMAE参数 (基于源码)
    parser.add_argument('--mask_rate', type=float, default=0.3, help='掩码比例')
    parser.add_argument('--replace_rate', type=float, default=0.1, help='噪声替换比例')
    parser.add_argument('--loss_fn', type=str, default='sce', choices=['sce', 'mse'], help='重建损失函数')
    parser.add_argument('--alpha_l', type=float, default=3, help='SCE损失alpha参数')
    
    # 🔥 多任务权重
    parser.add_argument('--lambda_link', type=float, default=1.0, help='链接预测损失权重')
    parser.add_argument('--lambda_contrastive', type=float, default=0.1, help='对比损失权重')
    parser.add_argument('--lambda_recon', type=float, default=0.1, help='重建损失权重')
    
    # 🔥 训练策略
    parser.add_argument('--warmup_ratio', type=float, default=0.2, help='预热阶段比例')
    parser.add_argument('--refinement_ratio', type=float, default=0.2, help='精炼阶段比例')
    parser.add_argument('--min_mrr_threshold', type=float, default=0.15, help='最低MRR阈值')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心')
    
    # 🔥 评估参数
    parser.add_argument('--eval_every', type=int, default=1, help='评估间隔')
    parser.add_argument('--save_every', type=int, default=50, help='保存间隔')
    
    # 🔥 系统参数
    parser.add_argument('--device', type=str, default='auto', help='设备: auto, cpu, cuda')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    # 🔥 显存优化参数 (基于SOTA模型分析)
    parser.add_argument('--use_amp', action='store_true', default=True, help='启用混合精度训练 (SimKGC/GraphMAE策略)')
    parser.add_argument('--gradient_checkpointing', action='store_true', help='启用梯度检查点 (KGTransformer策略)')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=2, help='梯度累积步数 (内存优化)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"使用设备: {device}")
    
    # 调试模式设置
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")
    
    try:
        # 1. 加载数据
        logger.info("🔄 加载知识图谱数据...")
        train_triples, entity2id, relation2id = load_data(args.data_folder)
        
        # 分割训练/验证数据
        split_idx = int(0.9 * len(train_triples))
        train_data = train_triples[:split_idx]
        valid_data = train_triples[split_idx:]
        
        logger.info(f"数据分割: 训练={len(train_data)}, 验证={len(valid_data)}")
        
        # 2. 创建配置
        logger.info("🔧 创建CCMGT配置...")
        config = create_config_from_args(args, len(entity2id), len(relation2id))
        
        # 打印配置摘要
        logger.info("📋 CCMGT配置摘要:")
        logger.info(f"  🧠 模型: ComplX({config.complex_dim}→{config.final_embedding_dim}) + Transformer({config.hidden_size})")
        logger.info(f"  🔄 多任务: Link({config.lambda_link}) + Contr({config.lambda_contrastive}) + Recon({config.lambda_recon})")
        logger.info(f"  📊 SimKGC: temp={config.temperature}, pre_batch={config.pre_batch}, margin={config.additive_margin}")
        logger.info(f"  🎭 GraphMAE: mask={config.mask_rate}, replace={config.replace_rate}, loss={config.loss_fn}")
        logger.info(f"  ⚙️  训练: lr={config.learning_rate}, warmup={config.warmup_ratio:.1%}, refine={config.refinement_ratio:.1%}")
        
        # 🔥 功能配置详细说明
        logger.info("🎯 功能配置详情:")
        logger.info(f"  {config.get_feature_summary()}")
        enabled_features = config.get_enabled_features()
        if config.base_mode_only:
            logger.info("  📌 当前配置:")
            logger.info("    ✅ ComplX基础嵌入和评分函数")
            logger.info("    🔒 SimKGC对比学习（已禁用）")
            logger.info("    🔒 GraphMAE掩码自编码（已禁用）")
            logger.info("    🔒 KGTransformer序列化（已禁用）")
            logger.info("    🔒 ComplX复数注意力（已禁用）")
            logger.info("  💡 启用高级功能的方法:")
            logger.info("    python kge_pretrain.py --enable_simkgc              # 启用对比学习")
            logger.info("    python kge_pretrain.py --enable_graphmae            # 启用掩码自编码")
            logger.info("    python kge_pretrain.py --enable_kgtransformer       # 启用序列化")
            logger.info("    python kge_pretrain.py --enable_complex_attention   # 启用复数注意力")
            logger.info("    python kge_pretrain.py --enable_simkgc --enable_graphmae  # 组合启用")
        else:
            logger.info("  📌 当前启用的高级功能:")
            if config.enable_simkgc:
                logger.info("    ✅ SimKGC对比学习：批次内负采样，提升表示质量")
            if config.enable_graphmae:
                logger.info("    ✅ GraphMAE掩码自编码：实体关系掩码，增强泛化能力")
            if config.enable_kgtransformer:
                logger.info("    ✅ KGTransformer序列化：Transformer建模，捕捉长距离依赖")
            if config.enable_complex_attention:
                logger.info("    ✅ ComplX复数注意力：复数空间注意力机制，增强表示融合")
        
        # 🔥 新增：显示统一的嵌入参数配置
        logger.info("📊 统一嵌入参数配置:")
        logger.info(f"  🔢 数据集: {len(entity2id)}个实体, {len(relation2id)}个关系")
        logger.info(f"  📝 文本Token: {config.text_tokens_size} + {config.text_tokens_buffer}缓冲")
        logger.info(f"  📏 嵌入维度: ComplX={config.final_embedding_dim}, Transformer={config.hidden_size}")
        logger.info(f"  📐 序列长度: {config.max_sequence_length}, Token类型: {config.token_types}")
        
        # 🔥 显示内存预估
        embedding_stats = config.get_embedding_summary()
        total_memory = embedding_stats['total_summary']['total_memory_mb']
        largest_layer = embedding_stats['total_summary']['largest_layer']
        logger.info(f"  💾 预估嵌入内存: {total_memory:.0f} MB (含Adam状态)")
        logger.info(f"  🚨 最大内存层: {largest_layer}")
        
        # 🔥 SOTA优化策略提示
        logger.info("🚀 SOTA优化策略:")
        logger.info(f"  💾 统一参数管理: 启用 (避免重复定义)")
        logger.info(f"  🎯 混合精度训练: {'启用' if config.use_amp else '禁用'} (SimKGC/GraphMAE策略)")
        logger.info(f"  🔗 梯度检查点: {'启用' if config.gradient_checkpointing else '禁用'} (KGTransformer策略)")
        logger.info(f"  📦 梯度累积: {config.gradient_accumulation_steps}步 (内存优化)")
        logger.info(f"  ⚡ 优化器: Adam (支持密集梯度)")
        
        if config.use_amp and torch.cuda.is_available():
            logger.info("  🔥 建议: 您的显卡支持混合精度，预计显存节省50%+，训练加速20%+")
        logger.info("  🔥 配置统一管理: 所有嵌入参数由ccmgt_config.py统一控制")
        
        # 3. 创建训练器
        logger.info("🚀 创建CCMGT训练器...")
        trainer = create_trainer_from_config(
            config=config,
            train_triples=train_data,
            valid_triples=valid_data,
            device=device
        )
        
        # 4. 开始训练
        logger.info("🎯 开始CCMGT训练...")
        training_history = trainer.train(
            num_epochs=args.epochs,
            eval_every=args.eval_every,
            save_every=args.save_every,
            checkpoint_dir=args.checkpoint_dir
        )
        
        # 5. 保存训练历史
        history_path = os.path.join(args.checkpoint_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_history = {}
            for key, values in training_history.items():
                if isinstance(values, list) and values:
                    json_history[key] = [float(v) if hasattr(v, 'item') else v for v in values]
                else:
                    json_history[key] = values
            json.dump(json_history, f, indent=2)
        
        logger.info(f"训练历史已保存: {history_path}")
        
        # 6. 最终评估
        logger.info("📊 进行最终评估...")
        final_metrics = trainer.evaluate(valid_data)
        
        logger.info("🎊 CCMGT训练完成!")
        logger.info(f"  最佳MRR: {trainer.best_mrr:.4f}")
        logger.info(f"  最终MRR: {final_metrics['mrr']:.4f}")
        logger.info(f"  最终Hits@10: {final_metrics['hits@10']:.4f}")
        logger.info(f"  检查点目录: {args.checkpoint_dir}")
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        raise


if __name__ == '__main__':
    main() 