# coding=utf-8
"""
KGE (Knowledge Graph Embedding) Integration Module

This module provides integration of various Knowledge Graph Embedding models
to enhance entity representations in the GNN-KGQA system.

Supported Models:
- ComplEx: Complex-valued embeddings with bilinear scoring
- TransE: Translation-based embeddings (placeholder)
- RotatE: Rotation-based embeddings (placeholder)
"""

from .base import BaseKGEEmbedding
from .complex import ComplExEmbedding
from .transe import TransEEmbedding
from .rotate import RotatEEmbedding
from .utils import load_pretrained_embeddings, one_hot_distance, shortest_path_distance

__all__ = [
    'BaseKGEEmbedding',
    'ComplExEmbedding', 
    'TransEEmbedding',
    'RotatEEmbedding',
    'load_pretrained_embeddings',
    'one_hot_distance',
    'shortest_path_distance'
]

# KGE模型注册表
KGE_MODELS = {
    'ComplEx': ComplExEmbedding,
    'TransE': TransEEmbedding, 
    'RotatE': RotatEEmbedding
}

def create_kge_embedding(model_type, **kwargs):
    """
    工厂函数：根据模型类型创建相应的KGE嵌入实例
    
    Args:
        model_type (str): KGE模型类型 ('ComplEx', 'TransE', 'RotatE')
        **kwargs: 传递给具体模型构造函数的参数
        
    Returns:
        BaseKGEEmbedding: KGE嵌入实例
        
    Raises:
        ValueError: 不支持的模型类型
    """
    if model_type not in KGE_MODELS:
        raise ValueError(f"不支持的KGE模型类型: {model_type}. 支持的类型: {list(KGE_MODELS.keys())}")
    
    return KGE_MODELS[model_type](**kwargs) 