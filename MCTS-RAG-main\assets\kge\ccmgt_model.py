"""
CCMGT主模型: Complex Contrastive Masked Graph Transformer
真正融合四个组件的核心算法:
1. ComplX: 复数嵌入基础
2. SimKGC: 对比学习和负采样
3. GraphMAE: 掩码自编码
4. KGTransformer: 序列化Transformer

基于源码的精确实现，不是简单拼接
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import math

from .ccmgt_config import CCMGTConfig
from .negative_sampling import SimKGCNegativeSampler, TripletDict, TripletExample, construct_triplet_mask
from .masking_strategy import MaskingStrategy, MaskDecoder
from .sequence_encoder import KGSequenceEncoder, KGBertConfig, create_kg_sequence
from .loss_functions import CCMGTUnifiedLoss
from .complex import ComplExEmbedding  # 保持原有ComplX实现
from .complex_attention import (ComplexMultiHeadAttention, ComplexPositionalEncoding, 
                               ComplexTransformerBlock, create_complex_attention_config)
from .base import BaseKGEEmbedding  # 添加基类导入

logger = logging.getLogger(__name__)


class CCMGTEmbeddings(nn.Module):
    """
    CCMGT嵌入层
    整合ComplX复数嵌入与KGTransformer的扩展词汇表
    """
    
    def __init__(self, config: CCMGTConfig):
        super().__init__()
        self.config = config
        
        # 1. ComplX基础嵌入 (复数空间) - 使用config统一参数
        self.complex_entity_embeddings = nn.Embedding(
            config.complex_entity_vocab_size,  # 🔥 使用config统一参数
            config.final_embedding_dim,
            padding_idx=None,
            sparse=False  # 🔧 修复：改为密集嵌入，避免优化器兼容性问题
        )
        self.complex_relation_embeddings = nn.Embedding(
            config.complex_relation_vocab_size,  # 🔥 使用config统一参数
            config.final_embedding_dim,
            padding_idx=None,
            sparse=False  # 🔧 修复：改为密集嵌入，避免优化器兼容性问题
        )
        
        # 🔥 统一嵌入表：将实体、关系、文本token统一编码
        # 完全使用config管理的参数，避免重复定义
        self.unified_embeddings = nn.Embedding(
            config.total_vocab_size,  # 🔥 使用config统一参数
            config.hidden_size,
            sparse=False  # 🔧 修复：改为密集嵌入，避免优化器兼容性问题
        )
        
        # 3. 映射层: 将ComplX嵌入映射到Transformer空间
        self.complex_to_transformer = nn.Linear(config.final_embedding_dim, config.hidden_size)
        
        # 4. 可学习掩码token (GraphMAE风格)
        self.entity_mask_token = nn.Parameter(torch.zeros(1, config.final_embedding_dim))
        self.relation_mask_token = nn.Parameter(torch.zeros(1, config.final_embedding_dim))
        
        # 5. 位置和类型嵌入 (KGTransformer风格) - 使用config统一参数
        self.position_embeddings = nn.Embedding(config.max_sequence_length, config.hidden_size)
        self.token_type_embeddings = nn.Embedding(config.token_types, config.hidden_size)
        
        # 6. 层归一化和dropout
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(0.1)
        
        # 初始化
        self._init_weights()
        
        # 🔥 统一的日志输出，显示所有配置参数
        logger.info(f"🔧 CCMGT嵌入层初始化完成:")
        logger.info(f"  📊 ComplX实体嵌入: {config.complex_entity_vocab_size} × {config.final_embedding_dim}")
        logger.info(f"  📊 ComplX关系嵌入: {config.complex_relation_vocab_size} × {config.final_embedding_dim}")
        logger.info(f"  📊 统一嵌入表: {config.total_vocab_size} × {config.hidden_size}")
        logger.info(f"  📊 位置嵌入: {config.max_sequence_length} × {config.hidden_size}")
        logger.info(f"  📊 Token类型嵌入: {config.token_types} × {config.hidden_size}")
        
        # 显示内存统计
        embedding_stats = config.get_embedding_summary()
        total_memory = embedding_stats['total_summary']['total_memory_mb']
        largest_layer = embedding_stats['total_summary']['largest_layer']
        logger.info(f"  💾 预估总内存占用: {total_memory:.1f} MB (包含Adam状态)")
        logger.info(f"  🔥 最大内存消耗层: {largest_layer}")
    
    def _init_weights(self):
        """初始化权重"""
        # ComplX嵌入使用小的初始化尺度
        nn.init.uniform_(self.complex_entity_embeddings.weight, -self.config.init_scale, self.config.init_scale)
        nn.init.uniform_(self.complex_relation_embeddings.weight, -self.config.init_scale, self.config.init_scale)
        
        # 统一嵌入使用标准初始化
        nn.init.normal_(self.unified_embeddings.weight, std=0.02)
        
        # 掩码token初始化
        nn.init.normal_(self.entity_mask_token, std=0.02)
        nn.init.normal_(self.relation_mask_token, std=0.02)
    
    def get_complex_embeddings(self, 
                             entity_ids: Optional[torch.Tensor], 
                             relation_ids: Optional[torch.Tensor] = None,
                             apply_mask: bool = False,
                             entity_mask: Optional[torch.Tensor] = None,
                             relation_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取ComplX嵌入 (支持掩码)
        
        Args:
            entity_ids: 实体ID [batch_size] 或 [batch_size, seq_len]，可以为None
            relation_ids: 关系ID [batch_size] 或 [batch_size, seq_len]，可以为None
            apply_mask: 是否应用掩码
            entity_mask: 实体掩码 [batch_size] 或 [batch_size, seq_len]
            relation_mask: 关系掩码 [batch_size] 或 [batch_size, seq_len]
        
        Returns:
            entity_embeddings, relation_embeddings
        """
        # 获取基础嵌入
        if entity_ids is not None:
            # 确保索引在有效范围内
            valid_entity_ids = torch.clamp(entity_ids, 0, self.complex_entity_embeddings.num_embeddings - 1)
            entity_emb = self.complex_entity_embeddings(valid_entity_ids)
        else:
            entity_emb = None
        
        if relation_ids is not None:
            # 确保索引在有效范围内
            valid_relation_ids = torch.clamp(relation_ids, 0, self.complex_relation_embeddings.num_embeddings - 1)
            relation_emb = self.complex_relation_embeddings(valid_relation_ids)
        else:
            relation_emb = None
        
        # 应用掩码 (GraphMAE风格)
        if apply_mask:
            if entity_mask is not None and entity_emb is not None:
                # 检查掩码维度匹配
                if entity_mask.numel() > 0 and entity_mask.any():
                    if entity_emb.dim() == 2 and entity_mask.dim() == 1:  # [batch_size, embed_dim] and [batch_size]
                        # 扩展mask token到正确维度
                        mask_token_expanded = self.entity_mask_token.expand(entity_mask.sum(), -1)
                        entity_emb[entity_mask] = mask_token_expanded
                    elif entity_emb.dim() == 3 and entity_mask.dim() == 2:  # [batch_size, seq_len, embed_dim] and [batch_size, seq_len]
                        mask_token_expanded = self.entity_mask_token.expand(entity_mask.sum(), -1)
                        entity_emb[entity_mask] = mask_token_expanded
            
            if relation_mask is not None and relation_emb is not None:
                # 检查掩码维度匹配
                if relation_mask.numel() > 0 and relation_mask.any():
                    if relation_emb.dim() == 2 and relation_mask.dim() == 1:  # [batch_size, embed_dim] and [batch_size]
                        mask_token_expanded = self.relation_mask_token.expand(relation_mask.sum(), -1)
                        relation_emb[relation_mask] = mask_token_expanded
                    elif relation_emb.dim() == 3 and relation_mask.dim() == 2:  # [batch_size, seq_len, embed_dim] and [batch_size, seq_len]
                        mask_token_expanded = self.relation_mask_token.expand(relation_mask.sum(), -1)
                        relation_emb[relation_mask] = mask_token_expanded
        
        return entity_emb, relation_emb
    
    def get_transformer_embeddings(self,
                                 input_ids: torch.Tensor,
                                 token_type_ids: torch.Tensor,
                                 position_ids: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        获取Transformer嵌入 (KGTransformer风格)
        
        Args:
            input_ids: 输入ID [batch_size, seq_len]
            token_type_ids: token类型ID [batch_size, seq_len]
            position_ids: 位置ID [batch_size, seq_len]
        
        Returns:
            transformer_embeddings [batch_size, seq_len, hidden_size]
        """
        seq_length = input_ids.size(1)
        
        if position_ids is None:
            position_ids = torch.arange(seq_length, device=input_ids.device).unsqueeze(0).expand_as(input_ids)
        
        # 获取各种嵌入
        word_embeddings = self.unified_embeddings(input_ids)
        position_embeddings = self.position_embeddings(position_ids)
        token_type_embeddings = self.token_type_embeddings(token_type_ids)
        
        # 组合嵌入
        embeddings = word_embeddings + position_embeddings + token_type_embeddings
        embeddings = self.LayerNorm(embeddings)
        embeddings = self.dropout(embeddings)
        
        return embeddings


class CCMGTCore(nn.Module):
    """
    CCMGT核心模型
    """
    
    def __init__(self, config: CCMGTConfig):
        super().__init__()
        self.config = config
        
        # 1. 嵌入层（始终需要）
        self.embeddings = CCMGTEmbeddings(config)
        
        # 2. ComplX评分函数（始终需要，基础功能）
        # 通过检查已有参数推断设备
        device = next(self.embeddings.parameters()).device if hasattr(self.embeddings, 'parameters') and list(self.embeddings.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.complex_scorer = ComplExEmbedding(
            num_entities=config.num_entities,
            num_relations=config.num_relations,
            hidden_dim=config.complex_dim,
            device=device,
            enable_ccmgt=False  # 禁用原CCMGT功能，使用新实现
        )
        
        # =============== 条件性初始化各功能模块 ===============
        
        # 3. SimKGC负采样器（仅在启用时初始化）
        if config.enable_simkgc:
            self.negative_sampler = SimKGCNegativeSampler(
                embedding_dim=config.final_embedding_dim,
                batch_size=64,  # 默认值，会在训练时动态设置
                **config.get_simkgc_config()
            )
            logger.info("✅ SimKGC对比学习模块已启用")
        else:
            self.negative_sampler = None
            logger.info("🔒 SimKGC对比学习模块已禁用")
        
        # 4. GraphMAE掩码策略（仅在启用时初始化）
        if config.enable_graphmae:
            self.masking_strategy = MaskingStrategy(
                embedding_dim=config.final_embedding_dim,
                **config.get_graphmae_config()
            )
            
            # 5. 掩码解码器
            self.mask_decoder = MaskDecoder(
                input_dim=config.hidden_size,
                hidden_dim=config.hidden_size,
                output_dim=config.final_embedding_dim,
                num_layers=2,
                concat_hidden=config.concat_hidden,
                transformer_layers=config.num_hidden_layers
            )
            logger.info("✅ GraphMAE掩码自编码模块已启用")
        else:
            self.masking_strategy = None
            self.mask_decoder = None
            logger.info("🔒 GraphMAE掩码自编码模块已禁用")
        
        # 6. KGTransformer序列编码器（仅在启用时初始化）
        if config.enable_kgtransformer:
            kg_config = KGBertConfig(
                vocab_size_token=config.text_tokens_size + config.text_tokens_buffer,  # 🔧 修复：包含buffer以匹配unified_embeddings
                vocab_size_entity=config.num_entities,
                vocab_size_relation=config.num_relations,
                **config.get_kgtransformer_config()
            )
            self.sequence_encoder = KGSequenceEncoder(kg_config)
            logger.info("✅ KGTransformer序列化模块已启用")
        else:
            self.sequence_encoder = None
            logger.info("🔒 KGTransformer序列化模块已禁用")
        
        # 7. 复数注意力机制（仅在启用时初始化）
        if config.enable_complex_attention:
            self.complex_pos_encoding = ComplexPositionalEncoding(
                d_model=config.final_embedding_dim,
                max_seq_length=config.max_sequence_length
            )
            
            # 构建复数Transformer层栈
            self.complex_transformer_layers = nn.ModuleList([
                ComplexTransformerBlock(
                    d_model=config.final_embedding_dim,
                    num_heads=config.complex_attention_heads,
                    d_ff=config.complex_d_ff,
                    dropout=config.complex_dropout
                ) for _ in range(config.complex_attention_layers)
            ])
            
            # 复数到实数的融合层
            if config.complex_fusion_strategy == 'gate':
                self.complex_real_gate = nn.Sequential(
                    nn.Linear(config.final_embedding_dim, config.final_embedding_dim),
                    nn.Sigmoid()
                )
                self.complex_imag_gate = nn.Sequential(
                    nn.Linear(config.final_embedding_dim, config.final_embedding_dim),
                    nn.Sigmoid()
                )
            
            logger.info(f"✅ 复数注意力机制已启用: {config.complex_attention_layers}层, "
                       f"{config.complex_attention_heads}头, 融合策略={config.complex_fusion_strategy}")
        else:
            self.complex_pos_encoding = None
            self.complex_transformer_layers = None
            self.complex_real_gate = None
            self.complex_imag_gate = None
            logger.info("🔒 复数注意力机制已禁用")
        
        # 8. 融合层（条件性初始化）
        if not config.base_mode_only:
            self.complex_fusion = nn.Linear(config.final_embedding_dim, config.hidden_size)
            self.output_projection = nn.Linear(config.hidden_size, config.final_embedding_dim)
        else:
            self.complex_fusion = None
            self.output_projection = None
        
        # 9. 梯度检查点支持（KGTransformer优化策略）
        self.gradient_checkpointing = getattr(config, 'gradient_checkpointing', False)
        
        # 10. 统一损失函数（根据启用的功能动态调整权重）
        self.unified_loss = CCMGTUnifiedLoss(
            lambda_link=config.lambda_link,
            lambda_contrastive=config.lambda_contrastive if config.enable_simkgc else 0.0,
            lambda_recon=config.lambda_recon if config.enable_graphmae else 0.0,
            lambda_kgtrans=config.lambda_kgtransformer if config.enable_kgtransformer else 0.0,
            complex_regularization=1e-6,
            contrastive_temperature=config.temperature
        )
        
        # 输出功能配置摘要
        logger.info(f"🎯 CCMGT核心模型初始化完成")
        logger.info(f"📊 {config.get_feature_summary()}")
        if config.base_mode_only:
            logger.info("💡 当前为基础模式，可通过以下参数启用高级功能：")
            logger.info("   --enable_simkgc: 启用对比学习")
            logger.info("   --enable_graphmae: 启用掩码自编码")
            logger.info("   --enable_kgtransformer: 启用序列化Transformer")
    
    def encode_triples_complex(self,
                             head_ids: torch.Tensor,
                             relation_ids: torch.Tensor,
                             tail_ids: torch.Tensor,
                             apply_masking: bool = False) -> Dict[str, torch.Tensor]:
        """
        ComplX风格的三元组编码
        
        Args:
            head_ids: 头实体ID [batch_size]
            relation_ids: 关系ID [batch_size]
            tail_ids: 尾实体ID [batch_size]
            apply_masking: 是否应用GraphMAE掩码
        
        Returns:
            包含各种嵌入和评分的字典
        """
        # 1. 应用掩码 (GraphMAE风格)
        mask_info = None
        if apply_masking:
            masked_heads, masked_relations, masked_tails, mask_info = self.masking_strategy.apply_triple_masking(
                head_ids, relation_ids, tail_ids, 
                self.config.num_entities, self.config.num_relations
            )
        else:
            masked_heads, masked_relations, masked_tails = head_ids, relation_ids, tail_ids
        
        # 2. 获取ComplX嵌入
        head_emb, _ = self.embeddings.get_complex_embeddings(
            masked_heads, 
            apply_mask=apply_masking,
            entity_mask=mask_info['head_mask'] if mask_info else None
        )
        tail_emb, _ = self.embeddings.get_complex_embeddings(
            masked_tails,
            apply_mask=apply_masking, 
            entity_mask=mask_info['tail_mask'] if mask_info else None
        )
        _, relation_emb = self.embeddings.get_complex_embeddings(
            None,
            masked_relations,
            apply_mask=apply_masking,
            relation_mask=mask_info['relation_mask'] if mask_info else None
        )
        
        # 3. ComplX评分 (使用原有的score_triples逻辑)
        scores = self._complex_score_triples(head_emb, relation_emb, tail_emb)
        
        # 4. 为对比学习准备hr_vector (SimKGC风格)
        hr_vector = self._combine_head_relation(head_emb, relation_emb)
        
        return {
            'head_embeddings': head_emb,
            'relation_embeddings': relation_emb,
            'tail_embeddings': tail_emb,
            'hr_vector': hr_vector,
            'scores': scores,
            'mask_info': mask_info
        }
    
    def encode_triples_sequence(self,
                              head_ids: torch.Tensor,
                              relation_ids: torch.Tensor,
                              tail_ids: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        KGTransformer风格的序列编码
        
        Args:
            head_ids: 头实体ID [batch_size]
            relation_ids: 关系ID [batch_size] 
            tail_ids: 尾实体ID [batch_size]
        
        Returns:
            序列编码结果
        """
        batch_size = head_ids.size(0)
        
        # 1. 创建序列化输入 (KGTransformer风格)
        # 格式: [CLS] head relation tail [SEP]
        cls_token = torch.full((batch_size, 1), 0, device=head_ids.device)  # 🔧 修复：使用0作为CLS token
        sep_token = torch.full((batch_size, 1), 1, device=head_ids.device)  # �� 修复：使用1作为SEP token
        
        # 转换到统一词汇表空间
        vocab_offset_entity = self.config.text_tokens_size + self.config.text_tokens_buffer  # 🔧 修复：使用config的正确值而非硬编码1000
        vocab_offset_relation = vocab_offset_entity + self.config.num_entities
        
        head_vocab = head_ids + vocab_offset_entity
        relation_vocab = relation_ids + vocab_offset_relation
        tail_vocab = tail_ids + vocab_offset_entity
        
        # 构建序列
        input_ids = torch.cat([
            cls_token,
            head_vocab.unsqueeze(1),
            relation_vocab.unsqueeze(1), 
            tail_vocab.unsqueeze(1),
            sep_token
        ], dim=1)  # [batch_size, 5]
        
        # token类型: 3=special, 0=head, 1=relation, 2=tail
        token_type_ids = torch.tensor([3, 0, 1, 2, 3], device=head_ids.device).unsqueeze(0).repeat(batch_size, 1)
        
        # 注意力mask
        attention_mask = torch.ones_like(input_ids)
        
        # 2. Transformer编码
        transformer_output = self.sequence_encoder.encoder(
            input_ids=input_ids,
            token_type_ids=token_type_ids,
            attention_mask=attention_mask
        )
        
        return {
            'sequence_embeddings': transformer_output['last_hidden_state'],  # [batch_size, 5, hidden_size]
            'pooled_output': transformer_output['pooler_output'],  # [batch_size, hidden_size]
            'head_seq_emb': transformer_output['last_hidden_state'][:, 1],  # head位置
            'relation_seq_emb': transformer_output['last_hidden_state'][:, 2],  # relation位置
            'tail_seq_emb': transformer_output['last_hidden_state'][:, 3]  # tail位置
        }
    
    def encode_triples_complex_attention(self,
                                       head_ids: torch.Tensor,
                                       relation_ids: torch.Tensor,
                                       tail_ids: torch.Tensor,
                                       apply_masking: bool = False) -> Dict[str, torch.Tensor]:
        """
        复数注意力机制编码 (SOTA核心创新)
        
        在ComplX复数空间中应用多头注意力，实现几何感知的表示学习
        
        Args:
            head_ids: 头实体ID [batch_size]
            relation_ids: 关系ID [batch_size]
            tail_ids: 尾实体ID [batch_size]
            apply_masking: 是否应用掩码
            
        Returns:
            复数注意力编码结果
        """
        if not self.config.enable_complex_attention:
            # 如果未启用，返回空结果
            return {}
            
        batch_size = head_ids.size(0)
        
        # 1. 获取ComplX嵌入 (复数形式)
        head_emb, _ = self.embeddings.get_complex_embeddings(head_ids, None)
        _, relation_emb = self.embeddings.get_complex_embeddings(None, relation_ids)
        tail_emb, _ = self.embeddings.get_complex_embeddings(tail_ids, None)
        
        # 2. 分离实部和虚部
        head_real, head_imag = torch.chunk(head_emb, 2, dim=-1)
        relation_real, relation_imag = torch.chunk(relation_emb, 2, dim=-1)
        tail_real, tail_imag = torch.chunk(tail_emb, 2, dim=-1)
        
        # 3. 构建序列: [head, relation, tail]
        seq_real = torch.stack([head_real, relation_real, tail_real], dim=1)  # [batch_size, 3, dim//2]
        seq_imag = torch.stack([head_imag, relation_imag, tail_imag], dim=1)   # [batch_size, 3, dim//2]
        
        # 4. 添加复数位置编码
        seq_real, seq_imag = self.complex_pos_encoding(seq_real, seq_imag)
        
        # 5. 通过复数Transformer层栈
        for i, transformer_layer in enumerate(self.complex_transformer_layers):
            # 🔥 KGTransformer策略：使用梯度检查点节省显存
            if self.gradient_checkpointing and self.training:
                def create_custom_forward(module):
                    def custom_forward(*inputs):
                        return module(*inputs)
                    return custom_forward
                
                seq_real, seq_imag = torch.utils.checkpoint.checkpoint(
                    create_custom_forward(transformer_layer),
                    seq_real, seq_imag
                )
            else:
                seq_real, seq_imag = transformer_layer(seq_real, seq_imag)
            logger.debug(f"复数Transformer层{i+1}输出: 实部{seq_real.shape}, 虚部{seq_imag.shape}")
        
        # 6. 提取各位置的复数表示
        enhanced_head_real, enhanced_head_imag = seq_real[:, 0], seq_imag[:, 0]
        enhanced_relation_real, enhanced_relation_imag = seq_real[:, 1], seq_imag[:, 1]
        enhanced_tail_real, enhanced_tail_imag = seq_real[:, 2], seq_imag[:, 2]
        
        # 7. 重新组合为完整复数嵌入
        enhanced_head_emb = torch.cat([enhanced_head_real, enhanced_head_imag], dim=-1)
        enhanced_relation_emb = torch.cat([enhanced_relation_real, enhanced_relation_imag], dim=-1)
        enhanced_tail_emb = torch.cat([enhanced_tail_real, enhanced_tail_imag], dim=-1)
        
        # 8. 复数到实数的融合
        fused_head_emb = self._fuse_complex_to_real(enhanced_head_real, enhanced_head_imag)
        fused_relation_emb = self._fuse_complex_to_real(enhanced_relation_real, enhanced_relation_imag)
        fused_tail_emb = self._fuse_complex_to_real(enhanced_tail_real, enhanced_tail_imag)
        
        # 9. ComplX评分 (使用增强后的嵌入)
        enhanced_scores = self._complex_score_triples(enhanced_head_emb, enhanced_relation_emb, enhanced_tail_emb)
        
        # 10. 复数空间中的对比学习hr向量
        enhanced_hr_vector = self._combine_head_relation(enhanced_head_emb, enhanced_relation_emb)
        
        return {
            'enhanced_head_embeddings': enhanced_head_emb,
            'enhanced_relation_embeddings': enhanced_relation_emb,
            'enhanced_tail_embeddings': enhanced_tail_emb,
            'fused_head_embeddings': fused_head_emb,
            'fused_relation_embeddings': fused_relation_emb,
            'fused_tail_embeddings': fused_tail_emb,
            'enhanced_hr_vector': enhanced_hr_vector,
            'enhanced_scores': enhanced_scores,
            'complex_sequence_real': seq_real,  # 用于分析
            'complex_sequence_imag': seq_imag   # 用于分析
        }
    
    def _fuse_complex_to_real(self, 
                             real_part: torch.Tensor, 
                             imag_part: torch.Tensor) -> torch.Tensor:
        """
        复数到实数的融合策略
        
        Args:
            real_part: 复数实部 [batch_size, dim//2]
            imag_part: 复数虚部 [batch_size, dim//2]
            
        Returns:
            融合后的实数表示 [batch_size, dim]
        """
        if self.config.complex_fusion_strategy == 'add':
            # 简单加法融合
            magnitude = torch.sqrt(real_part**2 + imag_part**2 + 1e-8)
            return torch.cat([real_part, magnitude], dim=-1)
            
        elif self.config.complex_fusion_strategy == 'concat':
            # 直接拼接实部和虚部
            return torch.cat([real_part, imag_part], dim=-1)
            
        elif self.config.complex_fusion_strategy == 'gate':
            # 门控融合
            real_gate = self.complex_real_gate(real_part)
            imag_gate = self.complex_imag_gate(imag_part)
            
            # 加权融合
            fused_real = real_gate * real_part
            fused_imag = imag_gate * imag_part
            
            return torch.cat([fused_real, fused_imag], dim=-1)
            
        else:
            raise ValueError(f"未知的复数融合策略: {self.config.complex_fusion_strategy}")
    
    def fuse_representations(self,
                           complex_output: Dict[str, torch.Tensor],
                           sequence_output: Dict[str, torch.Tensor],
                           complex_attention_output: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """
        融合ComplX、序列和复数注意力表示 (三分支创新融合点)
        
        Args:
            complex_output: ComplX编码输出
            sequence_output: 序列编码输出
            complex_attention_output: 复数注意力编码输出 (可选)
        
        Returns:
            融合后的表示
        """
        # 1. 将ComplX嵌入映射到Transformer空间
        head_complex_proj = self.complex_fusion(complex_output['head_embeddings'])
        relation_complex_proj = self.complex_fusion(complex_output['relation_embeddings'])
        tail_complex_proj = self.complex_fusion(complex_output['tail_embeddings'])
        
        # 2. 基础残差连接融合 (ComplX + Transformer)
        fused_head = head_complex_proj + sequence_output['head_seq_emb']
        fused_relation = relation_complex_proj + sequence_output['relation_seq_emb'] 
        fused_tail = tail_complex_proj + sequence_output['tail_seq_emb']
        
        # 3. 如果有复数注意力输出，进行三分支融合 (SOTA创新)
        if complex_attention_output and complex_attention_output:
            # 复数注意力增强的嵌入
            enhanced_head = complex_attention_output.get('fused_head_embeddings')
            enhanced_relation = complex_attention_output.get('fused_relation_embeddings')
            enhanced_tail = complex_attention_output.get('fused_tail_embeddings')
            
            if enhanced_head is not None:
                # 投影到Transformer空间
                enhanced_head_proj = self.complex_fusion(enhanced_head)
                enhanced_relation_proj = self.complex_fusion(enhanced_relation)
                enhanced_tail_proj = self.complex_fusion(enhanced_tail)
                
                # 三分支加权融合 (ComplX + Transformer + 复数注意力)
                attention_weight = 0.3  # 复数注意力权重
                transformer_weight = 0.4  # Transformer权重
                complex_weight = 0.3  # ComplX权重
                
                fused_head = (complex_weight * head_complex_proj + 
                             transformer_weight * sequence_output['head_seq_emb'] +
                             attention_weight * enhanced_head_proj)
                fused_relation = (complex_weight * relation_complex_proj + 
                                 transformer_weight * sequence_output['relation_seq_emb'] +
                                 attention_weight * enhanced_relation_proj)
                fused_tail = (complex_weight * tail_complex_proj + 
                             transformer_weight * sequence_output['tail_seq_emb'] +
                             attention_weight * enhanced_tail_proj)
                
                logger.debug(f"应用三分支融合: ComplX({complex_weight}) + Transformer({transformer_weight}) + ComplexAttention({attention_weight})")
        
        # 4. 投影回ComplX空间用于评分
        fused_head_complex = self.output_projection(fused_head)
        fused_relation_complex = self.output_projection(fused_relation)
        fused_tail_complex = self.output_projection(fused_tail)
        
        # 5. 重新计算ComplX评分
        fused_scores = self._complex_score_triples(fused_head_complex, fused_relation_complex, fused_tail_complex)
        
        # 6. 为对比学习准备增强的hr_vector
        fused_hr_vector = self._combine_head_relation(fused_head_complex, fused_relation_complex)
        
        # 7. 如果有复数注意力，使用其增强的hr_vector
        if complex_attention_output and 'enhanced_hr_vector' in complex_attention_output:
            enhanced_hr = complex_attention_output['enhanced_hr_vector']
            # 融合原始和增强的hr_vector
            fused_hr_vector = 0.7 * fused_hr_vector + 0.3 * enhanced_hr
        
        return {
            'fused_head': fused_head_complex,
            'fused_relation': fused_relation_complex,
            'fused_tail': fused_tail_complex,
            'fused_scores': fused_scores,
            'fused_hr_vector': fused_hr_vector,
            'transformer_features': {
                'head': fused_head,
                'relation': fused_relation,
                'tail': fused_tail,
                'pooled': sequence_output['pooled_output']
            }
        }
    
    def forward(self,
               head_ids: torch.Tensor,
               relation_ids: torch.Tensor,
               tail_ids: torch.Tensor,
               negative_tail_ids: Optional[torch.Tensor] = None,
               batch_examples: Optional[List[TripletExample]] = None,
               triplet_dict: Optional[TripletDict] = None,
               mode: str = "train",
               use_amp: bool = False) -> Dict[str, torch.Tensor]:  # 🔥 添加混合精度支持
        """
        CCMGT前向传播
        
        Args:
            head_ids: 头实体ID [batch_size]
            relation_ids: 关系ID [batch_size]
            tail_ids: 尾实体ID [batch_size]
            negative_tail_ids: 负样本尾实体ID [batch_size, num_negatives]
            batch_examples: 当前batch的三元组样本 (用于SimKGC)
            triplet_dict: 三元组字典 (用于过滤)
            mode: 模式 ("train" 或 "eval")
            use_amp: 是否使用混合精度训练 (SimKGC/GraphMAE优化)
        
        Returns:
            包含各种输出的字典
        """
        # 🔥 混合精度训练上下文管理器 (SimKGC/GraphMAE策略)
        autocast_context = torch.cuda.amp.autocast() if use_amp and torch.cuda.is_available() else torch.no_grad() if not mode == "train" else torch.enable_grad()
        
        with autocast_context:
            # 1. ComplX编码分支（基础功能，始终执行）
            complex_output = self.encode_triples_complex(
                head_ids, relation_ids, tail_ids,
                apply_masking=(mode == "train" and self.config.enable_graphmae)  # 只有启用GraphMAE时才应用掩码
            )
            
            # 2. 序列编码分支（仅在启用KGTransformer时执行）
            if self.config.enable_kgtransformer:
                sequence_output = self.encode_triples_sequence(head_ids, relation_ids, tail_ids)
            else:
                # 创建空的序列输出以保持接口一致性
                batch_size = head_ids.size(0)
                empty_emb = torch.zeros(batch_size, self.config.hidden_size, device=head_ids.device)
                sequence_output = {
                    'head_seq_emb': empty_emb,
                    'relation_seq_emb': empty_emb,
                    'tail_seq_emb': empty_emb,
                    'pooled_output': empty_emb,
                    'sequence_embeddings': empty_emb.unsqueeze(1).repeat(1, 4, 1),  # [batch_size, 4, hidden_size]
                    'sequence_scores': torch.zeros(batch_size, device=head_ids.device)
                }
            
            # 3. 复数注意力机制编码（仅在启用时执行）
            if self.config.enable_complex_attention:
                complex_attention_output = self.encode_triples_complex_attention(
                    head_ids, relation_ids, tail_ids,
                    apply_masking=(mode == "train" and self.config.enable_graphmae)
                )
            else:
                complex_attention_output = {}
            
            # 4. 多分支表示融合（仅在非基础模式时执行）
            if not self.config.base_mode_only:
                fused_output = self.fuse_representations(complex_output, sequence_output, complex_attention_output)
            else:
                # 基础模式：直接使用ComplX输出
                fused_output = {
                    'fused_head': complex_output['head_embeddings'],
                    'fused_relation': complex_output['relation_embeddings'],
                    'fused_tail': complex_output['tail_embeddings'],
                    'fused_scores': complex_output['complex_scores'],
                    'fused_hr_vector': complex_output['hr_vector'],
                    'transformer_features': {
                        'head': complex_output['head_embeddings'],
                        'relation': complex_output['relation_embeddings'],
                        'tail': complex_output['tail_embeddings'],
                        'pooled': complex_output['head_embeddings']  # 使用头实体嵌入作为池化输出
                    }
                }
        
        # 6. 准备输出字典（在autocast外部，确保输出类型正确）
        output = {
            'complex': complex_output,
            'sequence': sequence_output,
            'complex_attention': complex_attention_output,  # 复数注意力输出
            'fused': fused_output,
            'final_scores': fused_output['fused_scores']
        }
        
        # 7. 训练时计算额外信息
        if mode == "train":
            # 负采样（SimKGC风格，仅在启用SimKGC时执行）
            if self.config.enable_simkgc and negative_tail_ids is not None:
                negative_scores = self._score_negatives(
                    fused_output['fused_hr_vector'],
                    negative_tail_ids
                )
                output['negative_scores'] = negative_scores
            
            # 对比学习信息（仅在启用SimKGC时执行）
            if self.config.enable_simkgc and batch_examples is not None and triplet_dict is not None:
                # 获取当前模型的设备
                model_device = next(self.parameters()).device
                triplet_mask = construct_triplet_mask(batch_examples, None, triplet_dict, model_device)
                output['triplet_mask'] = triplet_mask
            
            # 掩码重建信息（仅在启用GraphMAE时执行）
            if self.config.enable_graphmae and complex_output.get('mask_info') is not None:
                original_embeddings = self.embeddings.get_complex_embeddings(
                    head_ids, relation_ids, apply_mask=False
                )[0]  # 获取原始头实体嵌入
                
                # 通过解码器重建
                # 获取所有隐藏层状态用于concat_hidden
                all_hidden_states = None
                if self.config.concat_hidden and 'sequence' in output:
                    # 从sequence_output中提取所有隐藏层
                    sequence_emb = output['sequence']['sequence_embeddings']  # [batch_size, seq_len, hidden_size]
                    head_seq_emb = sequence_emb[:, 1]  # head位置的嵌入 [batch_size, hidden_size]
                    # 模拟多层隐藏状态（实际应该从Transformer的所有层获取）
                    all_hidden_states = [head_seq_emb] * self.config.num_hidden_layers
                
                reconstructed = self.mask_decoder(
                    fused_output['transformer_features']['head'],
                    complex_output['mask_info']['head_mask'],
                    all_hidden_states
                )
                
                output['reconstruction'] = {
                    'reconstructed': reconstructed,
                    'original': original_embeddings,
                    'mask_indices': complex_output['mask_info']['head_mask'].nonzero().flatten()
                }
        
        return output
    
    def _complex_score_triples(self,
                             head_emb: torch.Tensor,
                             relation_emb: torch.Tensor,
                             tail_emb: torch.Tensor) -> torch.Tensor:
        """
        ComplX评分函数
        Re(<h, r, t̄>) where t̄ is complex conjugate of t
        """
        # 分离实部和虚部
        head_real, head_imag = torch.chunk(head_emb, 2, dim=-1)
        rel_real, rel_imag = torch.chunk(relation_emb, 2, dim=-1)
        tail_real, tail_imag = torch.chunk(tail_emb, 2, dim=-1)
        
        # ComplX评分计算
        score = (head_real * rel_real * tail_real +
                head_real * rel_imag * (-tail_imag) +
                head_imag * rel_real * (-tail_imag) +
                head_imag * rel_imag * tail_real)
        
        return score.sum(dim=-1)
    
    def _combine_head_relation(self,
                             head_emb: torch.Tensor,
                             relation_emb: torch.Tensor) -> torch.Tensor:
        """
        ComplX复数空间中的head-relation组合 (用于SimKGC对比学习)
        使用ComplX的复数乘法: h ∘ r
        """
        # 分离实部和虚部
        head_real, head_imag = torch.chunk(head_emb, 2, dim=-1)
        rel_real, rel_imag = torch.chunk(relation_emb, 2, dim=-1)
        
        # ComplX复数乘法: (a + bi) * (c + di) = (ac - bd) + (ad + bc)i
        hr_real = head_real * rel_real - head_imag * rel_imag
        hr_imag = head_real * rel_imag + head_imag * rel_real
        
        # 拼接实部和虚部
        hr_vector = torch.cat([hr_real, hr_imag], dim=-1)
        
        return hr_vector
    
    def _score_negatives(self,
                        hr_vector: torch.Tensor,
                        negative_tail_ids: torch.Tensor) -> torch.Tensor:
        """计算负样本评分"""
        batch_size, num_negatives = negative_tail_ids.shape
        
        # 获取负样本嵌入
        neg_tail_emb, _ = self.embeddings.get_complex_embeddings(negative_tail_ids.view(-1))
        neg_tail_emb = neg_tail_emb.view(batch_size, num_negatives, -1)
        
        # 计算评分 (简化版本，实际需要重构ComplX评分)
        hr_expanded = hr_vector.unsqueeze(1).expand(-1, num_negatives, -1)
        scores = (hr_expanded * neg_tail_emb).sum(dim=-1)
        
        return scores


class CCMGT(BaseKGEEmbedding):
    """
    CCMGT主模型：整合ComplX、SimKGC、GraphMAE、KGTransformer
    
    实现BaseKGEEmbedding接口，确保与gnn系统的兼容性
    """
    
    def __init__(self, config: CCMGTConfig):
        super().__init__(config.num_entities, config.num_relations, config.final_embedding_dim)
        self.config = config
        self.core = CCMGTCore(config)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info("CCMGT模型初始化完成")
    
    def to(self, device):
        """移动到设备"""
        self.device = device
        return super().to(device)
    
    def get_entity_embedding(self, entity_ids: torch.Tensor) -> torch.Tensor:
        """
        获取实体嵌入（BaseKGEEmbedding接口）
        
        Args:
            entity_ids: 实体ID张量 [batch_size] 或 [batch_size, num_entities]
            
        Returns:
            实体嵌入张量 [batch_size, embedding_dim] 或 [batch_size, num_entities, embedding_dim]
        """
        # 如果处于基础模式，直接返回ComplX嵌入
        if self.config.base_mode_only:
            if entity_ids.dim() == 1:
                return self.core.embeddings.complex_entity_embeddings(entity_ids)
            else:
                # 批量处理：[batch_size, num_entities] -> [batch_size, num_entities, embedding_dim]
                batch_size, num_entities = entity_ids.shape
                flat_ids = entity_ids.view(-1)  # [batch_size * num_entities]
                flat_embs = self.core.embeddings.complex_entity_embeddings(flat_ids)  # [batch_size * num_entities, embedding_dim]
                return flat_embs.view(batch_size, num_entities, -1)  # [batch_size, num_entities, embedding_dim]
        else:
            # 增强模式：使用完整的前向传播
            if entity_ids.dim() == 1:
                # 创建虚拟的关系和尾实体
                dummy_rel = torch.zeros_like(entity_ids)
                dummy_tail = torch.zeros_like(entity_ids)
                output = self.core(entity_ids, dummy_rel, dummy_tail, mode="eval")
                return output['fused']['fused_head']
            else:
                # 批量处理
                batch_size, num_entities = entity_ids.shape
                results = []
                for i in range(batch_size):
                    entity_batch = entity_ids[i]  # [num_entities]
                    dummy_rel = torch.zeros_like(entity_batch)
                    dummy_tail = torch.zeros_like(entity_batch)
                    output = self.core(entity_batch, dummy_rel, dummy_tail, mode="eval")
                    results.append(output['fused']['fused_head'])
                return torch.stack(results, dim=0)  # [batch_size, num_entities, embedding_dim]
    
    def get_relation_embedding(self, relation_ids: torch.Tensor) -> torch.Tensor:
        """
        获取关系嵌入（BaseKGEEmbedding接口）
        
        Args:
            relation_ids: 关系ID张量 [batch_size] 或 [batch_size, num_relations]
            
        Returns:
            关系嵌入张量 [batch_size, embedding_dim] 或 [batch_size, num_relations, embedding_dim]
        """
        # 如果处于基础模式，直接返回ComplX嵌入
        if self.config.base_mode_only:
            if relation_ids.dim() == 1:
                return self.core.embeddings.complex_relation_embeddings(relation_ids)
            else:
                # 批量处理
                batch_size, num_relations = relation_ids.shape
                flat_ids = relation_ids.view(-1)
                flat_embs = self.core.embeddings.complex_relation_embeddings(flat_ids)
                return flat_embs.view(batch_size, num_relations, -1)
        else:
            # 增强模式：使用完整的前向传播
            if relation_ids.dim() == 1:
                dummy_head = torch.zeros_like(relation_ids)
                dummy_tail = torch.zeros_like(relation_ids)
                output = self.core(dummy_head, relation_ids, dummy_tail, mode="eval")
                return output['fused']['fused_relation']
            else:
                # 批量处理
                batch_size, num_relations = relation_ids.shape
                results = []
                for i in range(batch_size):
                    relation_batch = relation_ids[i]
                    dummy_head = torch.zeros_like(relation_batch)
                    dummy_tail = torch.zeros_like(relation_batch)
                    output = self.core(dummy_head, relation_batch, dummy_tail, mode="eval")
                    results.append(output['fused']['fused_relation'])
                return torch.stack(results, dim=0)
    
    def score_triples(self,
                     head_ids: torch.Tensor,
                     relation_ids: torch.Tensor,
                     tail_ids: torch.Tensor) -> torch.Tensor:
        """
        三元组评分（BaseKGEEmbedding接口 + 保持与ComplExEmbedding的兼容性）
        """
        output = self.core(head_ids, relation_ids, tail_ids, mode="eval")
        return output['final_scores']
    
    def forward(self, *args, **kwargs):
        """前向传播"""
        return self.core(*args, **kwargs)
    
    def compute_loss(self,
                    head_ids: torch.Tensor,
                    relation_ids: torch.Tensor,
                    tail_ids: torch.Tensor,
                    negative_tail_ids: Optional[torch.Tensor] = None,
                    subsampling_weight: Optional[torch.Tensor] = None,
                    batch_examples: Optional[List[TripletExample]] = None,
                    triplet_dict: Optional[TripletDict] = None) -> Dict[str, torch.Tensor]:
        """
        计算统一损失
        """
        # 前向传播
        output = self.core(
            head_ids, relation_ids, tail_ids,
            negative_tail_ids=negative_tail_ids,
            batch_examples=batch_examples,
            triplet_dict=triplet_dict,
            mode="train"
        )
        
        # 准备损失组件
        loss_components = {}
        
        # 1. ComplX链接预测损失
        if 'negative_scores' in output:
            loss_components['complex'] = {
                'positive_scores': output['final_scores'],
                'negative_scores': output['negative_scores'],
                'subsampling_weight': subsampling_weight,
                'entity_embeddings': self.core.embeddings.complex_entity_embeddings.weight,
                'relation_embeddings': self.core.embeddings.complex_relation_embeddings.weight
            }
        
        # 2. SimKGC对比学习损失
        if 'triplet_mask' in output:
            loss_components['contrastive'] = {
                'hr_vector': output['fused']['fused_hr_vector'],
                'tail_vector': output['fused']['fused_tail'],
                'triplet_mask': output['triplet_mask']
            }
        
        # 3. GraphMAE重建损失
        if 'reconstruction' in output:
            loss_components['reconstruction'] = output['reconstruction']
        
        # 计算统一损失
        losses = self.core.unified_loss(loss_components)
        
        return losses 