class ContextChainFinder:
    def __init__(self, graph):
        self.graph = graph
        self.reverse_graph = self.build_reverse_graph(graph)

    def build_reverse_graph(self, graph):
        reverse_graph = {}
        for node, successors in graph.items():
            for succ in successors:
                if succ not in reverse_graph:
                    reverse_graph[succ] = []
                reverse_graph[succ].append(node)
        return reverse_graph

    def find_root_nodes(self):
        """找出所有根节点（没有前驱的节点）"""
        all_nodes = set(self.graph.keys())
        successors = set()
        for node in self.graph:
            successors.update(self.graph[node])
        return list(all_nodes - successors)

    def find_context_chains(self, target):
        root_nodes = self.find_root_nodes()  # 只从根节点开始搜索
        result = []
        for node in root_nodes:
            self.dfs_chain(
                current=node,
                target=target,
                path=[],
                visited=set(),
                is_after_target=False,
                chains=result
            )
        return result

    def dfs_chain(self, current, target, path, visited, is_after_target, chains):
        if current in visited:
            return
        visited.add(current)
        path.append(current)

        # 到达目标节点后，切换到后续阶段
        if current == target:
            is_after_target = True

        if is_after_target:
            # 后续阶段：继续延伸至叶子节点
            has_successors = False
            for neighbor in self.graph.get(current, []):
                if neighbor not in visited:
                    has_successors = True
                    self.dfs_chain(
                        neighbor, target, path, visited.copy(),
                        True, chains
                    )
            # 如果是叶子节点，记录完整路径
            if not has_successors and target in path:
                chains.append(list(path))
        else:
            # 前导阶段：搜索到目标节点
            for neighbor in self.graph.get(current, []):
                self.dfs_chain(
                    neighbor, target, path, visited.copy(),
                    False, chains
                )

        path.pop()
        visited.remove(current)


def main(graph,node):

    finder = ContextChainFinder(graph)
    chains = finder.find_context_chains(node)

    # 打印结果
    for idx, chain in enumerate(chains):
        print(f"Chain {idx+1}: {' -> '.join(chain)}")

if __name__ == '__main__':
    # 修正后的图结构（确保 C 的后继为 D 和 M）
    graph = {
        'A': ['B'],
        'B': ['C'],
        'C': ['D', 'M', 'L'],  # 合并 C 的后继
        'D': ['K'],
        'F': ['G'],
        'G': ['C'],
        'M': ['N'],
        'L': ['P'],
        'K': [],
        'N': []
    }
    main(graph, 'C')