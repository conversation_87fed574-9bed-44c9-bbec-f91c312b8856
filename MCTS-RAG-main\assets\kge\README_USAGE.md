# CCMGT框架使用说明

## 🎯 框架概述

CCMGT (Complex Contrastive Masked Graph Transformer) 是一个集成了四大SOTA技术的知识图谱嵌入框架：

1. **ComplX**: 复数嵌入基础
2. **SimKGC**: 对比学习和负采样  
3. **GraphMAE**: 掩码自编码重建
4. **KGTransformer**: 序列化Transformer编码
5. **复数注意力** (可选): ComplX空间中的多头注意力机制

## 🚀 快速开始

### 基础使用 (禁用复数注意力)

```python
from ccmgt_config import create_default_config
from ccmgt_model import CCMGTCore
from ccmgt_trainer import create_trainer_from_config

# 1. 准备数据
entity2id = {'entity1': 0, 'entity2': 1, 'entity3': 2}
relation2id = {'relation1': 0, 'relation2': 1}

# 2. 创建配置 (默认禁用复数注意力)
config = create_default_config(entity2id, relation2id)
print(f"复数注意力状态: {config.use_complex_attention}")  # False

# 3. 创建模型
model = CCMGTCore(config)

# 4. 前向传播
head_ids = torch.tensor([0, 1, 2])
relation_ids = torch.tensor([0, 1, 0]) 
tail_ids = torch.tensor([1, 2, 0])

output = model(head_ids, relation_ids, tail_ids, mode="eval")
scores = output['final_scores']
```

### 启用复数注意力 (实验性SOTA功能)

```python
# 方法1: 修改现有配置
config = create_default_config(entity2id, relation2id)
config.use_complex_attention = True
config.complex_attention_heads = 8
config.complex_attention_layers = 4

# 方法2: 直接在配置中设置
config = CCMGTConfig(
    num_entities=len(entity2id),
    num_relations=len(relation2id),
    use_complex_attention=True,
    complex_attention_heads=8,
    complex_attention_layers=4,
    # ... 其他参数
)

# 创建包含复数注意力的模型
model = CCMGTCore(config)

# 检查复数注意力输出
output = model(head_ids, relation_ids, tail_ids, mode="eval")
complex_attention_result = output['complex_attention']  # 非空字典
```

## ⚙️ 配置参数说明

### 复数注意力相关参数

```python
class CCMGTConfig:
    # 复数注意力开关
    use_complex_attention: bool = False  # 默认禁用
    
    # 复数注意力架构参数
    complex_attention_heads: int = 8     # 注意力头数
    complex_attention_layers: int = 4    # Transformer层数
    complex_d_ff: int = 512             # 前馈网络维度
    complex_dropout: float = 0.1        # Dropout率
    
    # 高级参数
    use_phase_aware_weights: bool = True     # 相位感知权重
    complex_fusion_strategy: str = 'add'     # 融合策略: 'add', 'concat', 'gate'
```

### 其他重要参数

```python
class CCMGTConfig:
    # ComplX参数
    complex_dim: int = 50               # 复数嵌入维度
    
    # SimKGC参数
    temperature: float = 0.05           # 对比学习温度
    pre_batch: int = 0                 # 预批次负采样
    
    # GraphMAE参数
    mask_rate: float = 0.5             # 掩码率
    loss_fn: str = "sce"               # 重建损失函数
    
    # KGTransformer参数  
    hidden_size: int = 768             # Transformer隐藏维度
    num_attention_heads: int = 12      # 标准注意力头数
    
    # 多任务学习权重
    lambda_link: float = 1.0           # 链接预测权重
    lambda_recon: float = 0.1          # 重建损失权重
    lambda_contrastive: float = 0.1    # 对比损失权重
    lambda_kgtransformer: float = 0.1  # KGTransformer损失权重
```

## 🧪 测试验证

### 基础功能测试

```bash
# 测试基础四组件融合 (禁用复数注意力)
python test_basic_ccmgt.py

# 预期输出:
# ✅ 基础CCMGT框架功能正常
# ✅ ComplX嵌入和评分功能正常
# ✅ 复数注意力机制已正确禁用
```

### 复数注意力测试

```bash
# 测试复数注意力机制 (需要先启用)
python test_complex_attention_integration.py

# 预期输出:
# ✅ 复数注意力机制基础功能正常
# ✅ CCMGT框架集成成功
```

## 📈 性能对比

| 配置 | 参数量 | 推理时间 | 表示能力 | 适用场景 |
|------|--------|----------|----------|----------|
| **基础CCMGT** | 100% | 100% | 高 | 生产环境，稳定训练 |
| **+复数注意力** | ~250% | ~320% | 极高 | 研究实验，SOTA性能 |

## 🔄 动态切换

可以在运行时动态启用/禁用复数注意力：

```python
# 创建基础模型
config = create_default_config(entity2id, relation2id)
model = CCMGTCore(config)

# 方法1: 修改配置 (需要重新创建模型)
config.use_complex_attention = True
model_with_attention = CCMGTCore(config)

# 方法2: 运行时检查
def run_experiment(use_attention=False):
    config.use_complex_attention = use_attention
    model = CCMGTCore(config)
    # ... 训练逻辑
    return results

# 对比实验
baseline_results = run_experiment(use_attention=False)
sota_results = run_experiment(use_attention=True)
```

## 📊 实验建议

### 阶段1: 基础验证
```python
# 禁用复数注意力，验证基础框架
config.use_complex_attention = False

# 确保以下功能正常：
# - ComplX复数嵌入
# - SimKGC对比学习  
# - GraphMAE掩码重建
# - KGTransformer序列编码
# - 四组件融合
```

### 阶段2: SOTA实验
```python
# 启用复数注意力，追求SOTA性能
config.use_complex_attention = True
config.complex_attention_heads = 8
config.complex_attention_layers = 4

# 对比性能提升：
# - 链接预测准确率
# - 复杂查询回答能力
# - 表示空间质量
```

## ⚠️ 注意事项

1. **内存使用**: 复数注意力会增加约1.8x内存使用
2. **计算时间**: 会增加约3.2x推理时间
3. **训练稳定性**: 建议先用基础版本验证，再启用复数注意力
4. **GPU要求**: 复数注意力推荐使用RTX 3080及以上GPU

## 🔧 故障排除

### 常见问题

**Q: 复数注意力不生效？**
```python
# 检查配置
print(f"复数注意力启用: {config.use_complex_attention}")

# 检查输出
output = model(head_ids, relation_ids, tail_ids)
print(f"复数注意力输出: {len(output.get('complex_attention', {}))}")
```

**Q: 内存不足？**
```python
# 使用轻量级配置
config = create_lightweight_config(len(entity2id), len(relation2id))
config.use_complex_attention = False  # 先禁用
```

**Q: 训练不稳定？**
```python
# 调整学习率和批次大小
config.learning_rate = 1e-4  # 降低学习率
config.grad_clip = 0.5       # 增强梯度裁剪
```

## 📚 更多资源

- **技术文档**: `COMPLEX_ATTENTION_SUMMARY.md`
- **API参考**: 查看各模块的docstring
- **示例代码**: `test_*.py` 文件
- **配置模板**: `ccmgt_config.py`

---

**🎉 总结**: CCMGT框架提供了灵活的配置选项，您可以根据实验需求选择使用基础四组件融合或包含复数注意力的SOTA版本。建议先验证基础功能，再逐步启用高级特性！ 