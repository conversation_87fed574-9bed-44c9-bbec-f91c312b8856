"""
CCMGT Configuration Module
整合SimKGC、GraphMAE、KGTransformer的核心配置参数

基于源码分析:
- SimKGC: 对比学习参数 (temperature, pre_batch, additive_margin)
- GraphMAE: 掩码策略参数 (mask_rate, replace_rate, drop_edge_rate)  
- KGTransformer: 序列化参数 (token_types, hidden_layers, vocab_size)
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
import torch


@dataclass
class CCMGTConfig:
    """CCMGT统一配置类"""
    
    # =============== 基础ComplX参数 ===============
    num_entities: int
    num_relations: int
    complex_dim: int = 50  # ComplX基础维度，最终嵌入维度=complex_dim*2
    init_scale: float = 1e-3
    
    # =============== 功能模块控制开关 ===============
    # 🔥 新增：逐步开启功能的控制参数
    enable_simkgc: bool = False  # 是否启用SimKGC对比学习（默认关闭）
    enable_graphmae: bool = False  # 是否启用GraphMAE掩码自编码（默认关闭）
    enable_kgtransformer: bool = False  # 是否启用KGTransformer序列化（默认关闭）
    enable_complex_attention: bool = False  # 是否启用复数注意力机制（默认关闭）
    
    # 基础模式配置（当所有功能都关闭时使用）
    base_mode_only: bool = True  # 仅使用ComplX基础模式（自动计算）
    
    # =============== 统一嵌入参数配置 ===============
    # 🔥 所有嵌入层的参数都在此统一定义，避免重复
    text_tokens_size: int = 100  # 🔧🔧 大幅减少文本token数量 (500→100)
    text_tokens_buffer: int = 10  # 🔧🔧 减少文本token缓冲区 (50→10)
    
    # 动态计算的嵌入大小 (将在__post_init__中计算)
    total_vocab_size: Optional[int] = None  # 统一嵌入表大小: entities + relations + text_tokens + buffer
    complex_entity_vocab_size: Optional[int] = None  # ComplX实体嵌入大小: entities + 1 (mask)
    complex_relation_vocab_size: Optional[int] = None  # ComplX关系嵌入大小: relations + 1 (mask)
    
    # =============== SimKGC对比学习参数 ===============
    # 基于SimKGC/models.py L28-40
    temperature: float = 0.05  # 对应log_inv_t的初始值1.0/args.t
    finetune_temperature: bool = True  # 对应args.finetune_t
    additive_margin: float = 0.02  # 对比学习的additive margin
    pre_batch: int = 2  # 历史批次数量，提升对比学习效果（可根据内存调整为0）
    pre_batch_weight: float = 0.5  # 历史批次负样本权重
    use_self_negative: bool = True  # 是否使用头实体作为负样本
    
    # =============== GraphMAE掩码参数 ===============  
    # 基于GraphMAE/configs.yml L10 (cora.mask_rate)
    mask_rate: float = 0.5  # 节点掩码比例 (GraphMAE源码原版)
    replace_rate: float = 0.1  # 噪声替换比例
    drop_edge_rate: float = 0.0  # 边dropout比例
    mask_token_rate: float = 0.9  # mask token比例 (1 - replace_rate)
    loss_fn: str = "sce"  # 重建损失函数: "sce" or "mse"
    alpha_l: float = 3  # SCE损失的alpha参数
    concat_hidden: bool = False  # 是否连接所有隐藏层 (暂时禁用，避免维度问题)
    
    # =============== KGTransformer序列化参数 ===============
    # 基于KGTransformer/kg_bert.py L20-35
    hidden_size: int = 256  # 🔧🔧 极限降低hidden_size (768→256)，减少75%参数量
    num_hidden_layers: int = 2  # 🔧 减少Transformer层数 (原来是4)
    num_attention_heads: int = 4  # 🔧 减少注意力头数 (原来是12)
    intermediate_size: Optional[int] = None  # FFN中间层维度，默认=hidden_size*4
    token_types: int = 4  # token类型数量: 0=head, 1=relation, 2=tail, 3=special
    max_sequence_length: int = 32  # 🔧 减少最大序列长度 (原来是126)
    vocab_size: Optional[int] = None  # 词汇表大小，None表示根据数据动态计算
    
    # =============== 复数注意力机制参数 ===============
    use_complex_attention: bool = False  # 启用ComplX复数注意力机制 (暂时禁用，后续测试)
    complex_attention_heads: int = 8    # 复数注意力头数 (必须整除complex_dim)
    complex_attention_layers: int = 4   # 复数注意力层数
    complex_d_ff: int = 512            # 复数前馈网络维度
    complex_dropout: float = 0.1       # 复数层dropout率
    use_phase_aware_weights: bool = True  # 启用相位感知权重调制
    complex_fusion_strategy: str = 'add'  # 复数与实数特征融合: 'add', 'concat', 'gate'
    
    # =============== 多任务学习参数 ===============
    lambda_link: float = 1.0  # 链接预测损失权重
    lambda_recon: float = 0.1  # 重建损失权重  
    lambda_contrastive: float = 0.1  # 对比损失权重
    lambda_kgtransformer: float = 0.1  # KGTransformer损失权重 (保持一致)
    
    # =============== 训练参数 ===============
    warmup_ratio: float = 0.2  # 预热阶段比例
    refinement_ratio: float = 0.2  # 精炼阶段比例
    learning_rate: float = 5e-4
    weight_decay: float = 1e-5
    grad_clip: float = 1.0  # 梯度裁剪
    lr_scheduler: str = 'linear'  # 学习率调度器: 'linear' or 'cosine' (SimKGC风格)
    
    # =============== 显存优化参数 ===============
    # 🔥 基于SimKGC、GraphMAE、KGTransformer的优化策略
    use_amp: bool = True  # 混合精度训练 (SimKGC/GraphMAE策略)
    gradient_checkpointing: bool = True  # 🔧 启用梯度检查点，大幅降低激活内存
    gradient_accumulation_steps: int = 8  # 🔧 增加梯度累积步数，配合小batch_size
    
    # =============== 质量保障参数 ===============
    min_mrr_threshold: float = 0.15  # 最低MRR阈值
    patience: int = 5  # 早停耐心
    
    def __post_init__(self):
        """后处理配置"""
        if self.intermediate_size is None:
            self.intermediate_size = self.hidden_size * 4
        
        # 🔥 新增：根据功能开关自动设置base_mode_only
        self.base_mode_only = not (self.enable_simkgc or self.enable_graphmae or self.enable_kgtransformer or self.enable_complex_attention)
        
        # 计算最终嵌入维度    
        self.final_embedding_dim = self.complex_dim * 2
        
        # 🔥 统一计算所有嵌入表大小
        self.total_vocab_size = self.num_entities + self.num_relations + self.text_tokens_size + self.text_tokens_buffer
        self.complex_entity_vocab_size = self.num_entities + 1  # +1 for mask token
        self.complex_relation_vocab_size = self.num_relations + 1  # +1 for mask token
        
        # 验证参数合理性
        assert 0 < self.mask_rate < 1, f"mask_rate必须在(0,1)范围内: {self.mask_rate}"
        assert 0 <= self.replace_rate <= 1, f"replace_rate必须在[0,1]范围内: {self.replace_rate}"
        assert self.hidden_size % self.num_attention_heads == 0, "hidden_size必须被num_attention_heads整除"
        
        # 更新mask_token_rate
        self.mask_token_rate = 1 - self.replace_rate
    
    def get_embedding_summary(self) -> Dict[str, Any]:
        """获取嵌入层参数统计"""
        def calculate_param_size(vocab_size: int, embedding_dim: int) -> float:
            """计算参数大小(MB)"""
            return (vocab_size * embedding_dim * 4) / (1024 * 1024)  # 4 bytes per float32
        
        def calculate_adam_memory(param_size_mb: float) -> float:
            """计算Adam优化器额外内存(MB)"""
            return param_size_mb * 2  # exp_avg + exp_avg_sq
        
        # 计算各嵌入层的参数量
        complex_entity_params = calculate_param_size(self.complex_entity_vocab_size, self.final_embedding_dim)
        complex_relation_params = calculate_param_size(self.complex_relation_vocab_size, self.final_embedding_dim)
        unified_params = calculate_param_size(self.total_vocab_size, self.hidden_size)
        position_params = calculate_param_size(self.max_sequence_length, self.hidden_size)
        token_type_params = calculate_param_size(self.token_types, self.hidden_size)
        
        total_embedding_params = complex_entity_params + complex_relation_params + unified_params + position_params + token_type_params
        total_adam_memory = calculate_adam_memory(total_embedding_params)
        
        return {
            'embedding_layers': {
                'complex_entity': {
                    'vocab_size': self.complex_entity_vocab_size,
                    'embedding_dim': self.final_embedding_dim,
                    'param_size_mb': complex_entity_params,
                    'adam_memory_mb': calculate_adam_memory(complex_entity_params)
                },
                'complex_relation': {
                    'vocab_size': self.complex_relation_vocab_size,
                    'embedding_dim': self.final_embedding_dim,
                    'param_size_mb': complex_relation_params,
                    'adam_memory_mb': calculate_adam_memory(complex_relation_params)
                },
                'unified_embedding': {
                    'vocab_size': self.total_vocab_size,
                    'embedding_dim': self.hidden_size,
                    'param_size_mb': unified_params,
                    'adam_memory_mb': calculate_adam_memory(unified_params)
                },
                'position_embedding': {
                    'vocab_size': self.max_sequence_length,
                    'embedding_dim': self.hidden_size,
                    'param_size_mb': position_params,
                    'adam_memory_mb': calculate_adam_memory(position_params)
                },
                'token_type_embedding': {
                    'vocab_size': self.token_types,
                    'embedding_dim': self.hidden_size,
                    'param_size_mb': token_type_params,
                    'adam_memory_mb': calculate_adam_memory(token_type_params)
                }
            },
            'total_summary': {
                'total_embedding_params_mb': total_embedding_params,
                'total_adam_memory_mb': total_adam_memory,
                'total_memory_mb': total_embedding_params + total_adam_memory,
                'largest_layer': 'unified_embedding' if unified_params == max(complex_entity_params, complex_relation_params, unified_params, position_params, token_type_params) else 'complex_entity'
            }
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CCMGTConfig':
        """从字典创建配置"""
        return cls(**{k: v for k, v in config_dict.items() if k in cls.__annotations__})
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {k: getattr(self, k) for k in self.__annotations__}
    
    def get_simkgc_config(self) -> Dict[str, Any]:
        """获取SimKGC相关配置"""
        return {
            'temperature': self.temperature,
            'finetune_temperature': self.finetune_temperature,
            'additive_margin': self.additive_margin,
            'pre_batch': self.pre_batch,
            'pre_batch_weight': self.pre_batch_weight,
            'use_self_negative': self.use_self_negative
        }
    
    def get_graphmae_config(self) -> Dict[str, Any]:
        """获取GraphMAE相关配置"""
        return {
            'mask_rate': self.mask_rate,
            'replace_rate': self.replace_rate,
            'drop_edge_rate': self.drop_edge_rate,
            'mask_token_rate': self.mask_token_rate,
            'loss_fn': self.loss_fn,
            'alpha_l': self.alpha_l,
            'concat_hidden': self.concat_hidden
        }
    
    def get_kgtransformer_config(self) -> Dict[str, Any]:
        """获取KGTransformer相关配置"""
        return {
            'hidden_size': self.hidden_size,
            'num_hidden_layers': self.num_hidden_layers,
            'num_attention_heads': self.num_attention_heads,
            'intermediate_size': self.intermediate_size,
            'token_types': self.token_types,
            'max_position_embeddings': self.max_sequence_length
        }
    
    def get_enabled_features(self) -> List[str]:
        """获取启用的功能列表"""
        enabled = []
        if self.enable_simkgc:
            enabled.append("SimKGC")
        if self.enable_graphmae:
            enabled.append("GraphMAE") 
        if self.enable_kgtransformer:
            enabled.append("KGTransformer")
        if self.enable_complex_attention:
            enabled.append("ComplexAttention")
        if self.base_mode_only:
            enabled.append("ComplX-Base")
        return enabled
    
    def get_feature_summary(self) -> str:
        """获取功能配置摘要"""
        enabled_features = self.get_enabled_features()
        if self.base_mode_only:
            return "🔧 基础模式：仅使用ComplX基础嵌入和评分函数"
        else:
            return f"🚀 增强模式：启用 {' + '.join(enabled_features)}"


def create_default_config(num_entities: int, num_relations: int) -> CCMGTConfig:
    """创建默认配置 - 针对大规模数据集优化"""
    return CCMGTConfig(
        num_entities=num_entities,
        num_relations=num_relations,
        complex_dim=50,
        hidden_size=256,  # 🔧 使用优化的hidden_size
        num_hidden_layers=2,  # 🔧 使用优化的层数
        num_attention_heads=4,  # 🔧 使用优化的注意力头数
        text_tokens_size=100,  # 🔧 使用优化的文本token数量
        text_tokens_buffer=10,  # 🔧 使用优化的缓冲区
        max_sequence_length=32,  # 🔧 使用优化的序列长度
        # 🔥 功能开关默认关闭（用户需要显式启用）
        enable_simkgc=False,
        enable_graphmae=False,
        enable_kgtransformer=False,
        enable_complex_attention=False
    )


def create_lightweight_config(num_entities: int, num_relations: int) -> CCMGTConfig:
    """创建轻量级配置（用于快速测试）"""
    return CCMGTConfig(
        num_entities=num_entities,
        num_relations=num_relations,
        complex_dim=32,
        hidden_size=256,
        num_hidden_layers=2,
        num_attention_heads=8,
        mask_rate=0.2,
        pre_batch=1,
        max_sequence_length=64,
        # 🔥 轻量级配置也默认关闭所有高级功能
        enable_simkgc=False,
        enable_graphmae=False,
        enable_kgtransformer=False,
        enable_complex_attention=False
    ) 